{"ast": null, "code": "import { createElementVNode as _createElementVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, resolveComponent as _resolveComponent, createBlock as _createBlock, withCtx as _withCtx, createCommentVNode as _createCommentVNode, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, createVNode as _createVNode, withModifiers as _withModifiers, resolveDirective as _resolveDirective, withDirectives as _withDirectives } from \"vue\";\nconst _hoisted_1 = {\n  class: \"discussion\"\n};\nconst _hoisted_2 = {\n  class: \"card-header\"\n};\nconst _hoisted_3 = {\n  class: \"header-actions\"\n};\nconst _hoisted_4 = {\n  key: 1,\n  class: \"current-team-info\"\n};\nconst _hoisted_5 = {\n  class: \"team-name\"\n};\nconst _hoisted_6 = {\n  key: 2,\n  class: \"no-team-info\"\n};\nconst _hoisted_7 = {\n  class: \"discussion-list\"\n};\nconst _hoisted_8 = {\n  key: 0,\n  class: \"empty-state\"\n};\nconst _hoisted_9 = {\n  key: 1,\n  class: \"discussion-grid\"\n};\nconst _hoisted_10 = {\n  class: \"discussion-description\"\n};\nconst _hoisted_11 = {\n  class: \"discussion-meta\"\n};\nconst _hoisted_12 = {\n  class: \"discussion-author\"\n};\nconst _hoisted_13 = {\n  class: \"discussion-info\"\n};\nconst _hoisted_14 = {\n  class: \"info-item\"\n};\nconst _hoisted_15 = {\n  key: 0,\n  class: \"info-item\"\n};\nconst _hoisted_16 = {\n  class: \"discussion-footer\"\n};\nconst _hoisted_17 = {\n  key: 0,\n  class: \"pagination\"\n};\nconst _hoisted_18 = {\n  key: 0,\n  class: \"reply-context\"\n};\nconst _hoisted_19 = {\n  class: \"original-content\"\n};\nconst _hoisted_20 = {\n  key: 0,\n  class: \"discussion-detail\"\n};\nconst _hoisted_21 = {\n  class: \"main-discussion\"\n};\nconst _hoisted_22 = {\n  class: \"discussion-header\"\n};\nconst _hoisted_23 = {\n  class: \"header-left\"\n};\nconst _hoisted_24 = {\n  class: \"discussion-title\"\n};\nconst _hoisted_25 = {\n  class: \"discussion-tags\"\n};\nconst _hoisted_26 = {\n  class: \"header-right\"\n};\nconst _hoisted_27 = {\n  class: \"author-info\"\n};\nconst _hoisted_28 = {\n  class: \"author-details\"\n};\nconst _hoisted_29 = {\n  class: \"author-name\"\n};\nconst _hoisted_30 = {\n  class: \"publish-time\"\n};\nconst _hoisted_31 = {\n  class: \"discussion-content\"\n};\nconst _hoisted_32 = {\n  class: \"content-text\"\n};\nconst _hoisted_33 = {\n  key: 0,\n  class: \"attachments-section\"\n};\nconst _hoisted_34 = {\n  class: \"attachments-title\"\n};\nconst _hoisted_35 = {\n  class: \"attachments-list\"\n};\nconst _hoisted_36 = {\n  class: \"file-name\"\n};\nconst _hoisted_37 = {\n  class: \"replies-section\"\n};\nconst _hoisted_38 = {\n  class: \"replies-header\"\n};\nconst _hoisted_39 = {\n  class: \"replies-title\"\n};\nconst _hoisted_40 = {\n  key: 0,\n  class: \"loading-state\"\n};\nconst _hoisted_41 = {\n  key: 1,\n  class: \"empty-state\"\n};\nconst _hoisted_42 = {\n  key: 2,\n  class: \"replies-list\"\n};\nconst _hoisted_43 = {\n  class: \"reply-number\"\n};\nconst _hoisted_44 = {\n  class: \"reply-content-wrapper\"\n};\nconst _hoisted_45 = {\n  class: \"reply-header\"\n};\nconst _hoisted_46 = {\n  class: \"reply-author\"\n};\nconst _hoisted_47 = {\n  class: \"author-info\"\n};\nconst _hoisted_48 = {\n  class: \"author-name\"\n};\nconst _hoisted_49 = {\n  class: \"reply-time\"\n};\nconst _hoisted_50 = {\n  class: \"reply-content\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_option = _resolveComponent(\"el-option\");\n  const _component_el_select = _resolveComponent(\"el-select\");\n  const _component_el_tag = _resolveComponent(\"el-tag\");\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_el_empty = _resolveComponent(\"el-empty\");\n  const _component_Clock = _resolveComponent(\"Clock\");\n  const _component_el_icon = _resolveComponent(\"el-icon\");\n  const _component_Document = _resolveComponent(\"Document\");\n  const _component_el_card = _resolveComponent(\"el-card\");\n  const _component_el_pagination = _resolveComponent(\"el-pagination\");\n  const _component_el_form_item = _resolveComponent(\"el-form-item\");\n  const _component_el_input = _resolveComponent(\"el-input\");\n  const _component_el_form = _resolveComponent(\"el-form\");\n  const _component_el_dialog = _resolveComponent(\"el-dialog\");\n  const _component_el_avatar = _resolveComponent(\"el-avatar\");\n  const _component_ChatDotRound = _resolveComponent(\"ChatDotRound\");\n  const _component_el_divider = _resolveComponent(\"el-divider\");\n  const _component_Loading = _resolveComponent(\"Loading\");\n  const _directive_loading = _resolveDirective(\"loading\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_el_card, null, {\n    header: _withCtx(() => [_createElementVNode(\"div\", _hoisted_2, [_cache[21] || (_cache[21] = _createElementVNode(\"h3\", null, \"项目讨论\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_3, [$setup.isTeacher ? (_openBlock(), _createBlock(_component_el_select, {\n      key: 0,\n      modelValue: $setup.currentTeamId,\n      \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $setup.currentTeamId = $event),\n      placeholder: \"选择团队\",\n      onChange: $setup.loadDiscussions\n    }, {\n      default: _withCtx(() => [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.myTeams, team => {\n        return _openBlock(), _createBlock(_component_el_option, {\n          key: team.id,\n          label: `${team.name} (${team.projectName || '未知项目'})`,\n          value: team.id\n        }, null, 8 /* PROPS */, [\"label\", \"value\"]);\n      }), 128 /* KEYED_FRAGMENT */))]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\", \"onChange\"])) : $setup.currentTeam ? (_openBlock(), _createElementBlock(\"div\", _hoisted_4, [_createElementVNode(\"span\", _hoisted_5, _toDisplayString($setup.currentTeam.name), 1 /* TEXT */), _createVNode(_component_el_tag, {\n      size: \"small\",\n      type: \"info\"\n    }, {\n      default: _withCtx(() => _cache[17] || (_cache[17] = [_createTextVNode(\"我的团队\")])),\n      _: 1 /* STABLE */,\n      __: [17]\n    })])) : (_openBlock(), _createElementBlock(\"div\", _hoisted_6, [_createVNode(_component_el_tag, {\n      size: \"small\",\n      type: \"warning\"\n    }, {\n      default: _withCtx(() => _cache[18] || (_cache[18] = [_createTextVNode(\"未加入团队\")])),\n      _: 1 /* STABLE */,\n      __: [18]\n    })])), $setup.currentTeamId ? (_openBlock(), _createBlock(_component_el_button, {\n      key: 3,\n      type: \"primary\",\n      onClick: _cache[1] || (_cache[1] = $event => $setup.showCreateDialog = true),\n      icon: _ctx.Plus\n    }, {\n      default: _withCtx(() => _cache[19] || (_cache[19] = [_createTextVNode(\" 发起讨论 \")])),\n      _: 1 /* STABLE */,\n      __: [19]\n    }, 8 /* PROPS */, [\"icon\"])) : _createCommentVNode(\"v-if\", true), _createVNode(_component_el_button, {\n      onClick: $setup.loadDiscussions,\n      icon: _ctx.Refresh\n    }, {\n      default: _withCtx(() => _cache[20] || (_cache[20] = [_createTextVNode(\" 刷新 \")])),\n      _: 1 /* STABLE */,\n      __: [20]\n    }, 8 /* PROPS */, [\"onClick\", \"icon\"])])])]),\n    default: _withCtx(() => [_withDirectives((_openBlock(), _createElementBlock(\"div\", _hoisted_7, [$setup.discussions.length === 0 && !$setup.loading ? (_openBlock(), _createElementBlock(\"div\", _hoisted_8, [_createVNode(_component_el_empty, {\n      description: \"暂无讨论记录\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_button, {\n        type: \"primary\",\n        onClick: _cache[2] || (_cache[2] = $event => $setup.showCreateDialog = true)\n      }, {\n        default: _withCtx(() => _cache[22] || (_cache[22] = [_createTextVNode(\" 发起讨论 \")])),\n        _: 1 /* STABLE */,\n        __: [22]\n      })]),\n      _: 1 /* STABLE */\n    })])) : (_openBlock(), _createElementBlock(\"div\", _hoisted_9, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.discussions, discussion => {\n      return _openBlock(), _createElementBlock(\"div\", {\n        key: discussion.id,\n        class: \"discussion-card\"\n      }, [_createVNode(_component_el_card, {\n        shadow: \"hover\",\n        onClick: $event => $setup.viewDiscussionDetail(discussion)\n      }, {\n        default: _withCtx(() => [_createElementVNode(\"h4\", null, _toDisplayString(discussion.title || '无标题讨论'), 1 /* TEXT */), _createElementVNode(\"p\", _hoisted_10, _toDisplayString(discussion.content), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_11, [discussion.subType ? (_openBlock(), _createBlock(_component_el_tag, {\n          key: 0,\n          type: $setup.getTypeColor(discussion.subType),\n          size: \"small\"\n        }, {\n          default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.getTypeText(discussion.subType)), 1 /* TEXT */)]),\n          _: 2 /* DYNAMIC */\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"type\"])) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"span\", _hoisted_12, _toDisplayString(discussion.userName), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_13, [_createElementVNode(\"div\", _hoisted_14, [_createVNode(_component_el_icon, null, {\n          default: _withCtx(() => [_createVNode(_component_Clock)]),\n          _: 1 /* STABLE */\n        }), _createElementVNode(\"span\", null, _toDisplayString($setup.formatDate(discussion.createTime)), 1 /* TEXT */)]), discussion.attachments && discussion.attachments.length > 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_15, [_createVNode(_component_el_icon, null, {\n          default: _withCtx(() => [_createVNode(_component_Document)]),\n          _: 1 /* STABLE */\n        }), _createElementVNode(\"span\", null, _toDisplayString(discussion.attachments.length) + \"个附件\", 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"div\", _hoisted_16, [_createVNode(_component_el_button, {\n          size: \"small\",\n          onClick: _withModifiers($event => $setup.viewDiscussionDetail(discussion), [\"stop\"])\n        }, {\n          default: _withCtx(() => [...(_cache[23] || (_cache[23] = [_createTextVNode(\" 查看详情 \")]))]),\n          _: 2 /* DYNAMIC */,\n          __: [23]\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"]), _createVNode(_component_el_button, {\n          size: \"small\",\n          onClick: _withModifiers($event => $setup.replyToDiscussion(discussion), [\"stop\"]),\n          icon: _ctx.ChatDotRound\n        }, {\n          default: _withCtx(() => [...(_cache[24] || (_cache[24] = [_createTextVNode(\" 回复 \")]))]),\n          _: 2 /* DYNAMIC */,\n          __: [24]\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\", \"icon\"]), $setup.canEdit(discussion) ? (_openBlock(), _createBlock(_component_el_button, {\n          key: 0,\n          size: \"small\",\n          onClick: _withModifiers($event => $setup.editDiscussion(discussion), [\"stop\"]),\n          icon: _ctx.Edit\n        }, {\n          default: _withCtx(() => [...(_cache[25] || (_cache[25] = [_createTextVNode(\" 编辑 \")]))]),\n          _: 2 /* DYNAMIC */,\n          __: [25]\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\", \"icon\"])) : _createCommentVNode(\"v-if\", true), $setup.canDelete(discussion) ? (_openBlock(), _createBlock(_component_el_button, {\n          key: 1,\n          size: \"small\",\n          type: \"danger\",\n          onClick: _withModifiers($event => $setup.deleteDiscussion(discussion.id), [\"stop\"]),\n          icon: _ctx.Delete\n        }, {\n          default: _withCtx(() => [...(_cache[26] || (_cache[26] = [_createTextVNode(\" 删除 \")]))]),\n          _: 2 /* DYNAMIC */,\n          __: [26]\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\", \"icon\"])) : _createCommentVNode(\"v-if\", true)])]),\n        _: 2 /* DYNAMIC */\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"])]);\n    }), 128 /* KEYED_FRAGMENT */))]))])), [[_directive_loading, $setup.loading]]), $setup.total > 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_17, [_createVNode(_component_el_pagination, {\n      \"current-page\": $setup.currentPage,\n      \"onUpdate:currentPage\": _cache[3] || (_cache[3] = $event => $setup.currentPage = $event),\n      \"page-size\": $setup.pageSize,\n      \"onUpdate:pageSize\": _cache[4] || (_cache[4] = $event => $setup.pageSize = $event),\n      total: $setup.total,\n      \"page-sizes\": [10, 20, 50],\n      layout: \"total, sizes, prev, pager, next, jumper\",\n      onSizeChange: $setup.loadDiscussions,\n      onCurrentChange: $setup.loadDiscussions\n    }, null, 8 /* PROPS */, [\"current-page\", \"page-size\", \"total\", \"onSizeChange\", \"onCurrentChange\"])])) : _createCommentVNode(\"v-if\", true)]),\n    _: 1 /* STABLE */\n  }), _createCommentVNode(\" 创建/编辑讨论对话框 \"), _createVNode(_component_el_dialog, {\n    modelValue: $setup.showCreateDialog,\n    \"onUpdate:modelValue\": _cache[9] || (_cache[9] = $event => $setup.showCreateDialog = $event),\n    title: $setup.editingDiscussion ? '编辑讨论' : '发起讨论',\n    width: \"600px\"\n  }, {\n    footer: _withCtx(() => [_createVNode(_component_el_button, {\n      onClick: _cache[8] || (_cache[8] = $event => $setup.showCreateDialog = false)\n    }, {\n      default: _withCtx(() => _cache[27] || (_cache[27] = [_createTextVNode(\"取消\")])),\n      _: 1 /* STABLE */,\n      __: [27]\n    }), _createVNode(_component_el_button, {\n      type: \"primary\",\n      onClick: $setup.submitDiscussion,\n      loading: $setup.submitting\n    }, {\n      default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.editingDiscussion ? '更新' : '发布'), 1 /* TEXT */)]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"onClick\", \"loading\"])]),\n    default: _withCtx(() => [_createVNode(_component_el_form, {\n      ref: \"formRef\",\n      model: $setup.discussionForm,\n      rules: $setup.formRules,\n      \"label-width\": \"80px\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_form_item, {\n        label: \"讨论类型\",\n        prop: \"type\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_select, {\n          modelValue: $setup.discussionForm.type,\n          \"onUpdate:modelValue\": _cache[5] || (_cache[5] = $event => $setup.discussionForm.type = $event),\n          placeholder: \"请选择讨论类型\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_option, {\n            label: \"一般讨论\",\n            value: \"DISCUSSION\"\n          }), _createVNode(_component_el_option, {\n            label: \"进度汇报\",\n            value: \"PROGRESS\"\n          }), _createVNode(_component_el_option, {\n            label: \"问题讨论\",\n            value: \"ISSUE\"\n          }), _createVNode(_component_el_option, {\n            label: \"技术提问\",\n            value: \"QUESTION\"\n          }), _createVNode(_component_el_option, {\n            label: \"公告通知\",\n            value: \"ANNOUNCEMENT\"\n          }), _createVNode(_component_el_option, {\n            label: \"资源分享\",\n            value: \"RESOURCE\"\n          }), _createVNode(_component_el_option, {\n            label: \"其他\",\n            value: \"OTHER\"\n          })]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"讨论标题\",\n        prop: \"title\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.discussionForm.title,\n          \"onUpdate:modelValue\": _cache[6] || (_cache[6] = $event => $setup.discussionForm.title = $event),\n          placeholder: \"请输入讨论标题\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"讨论内容\",\n        prop: \"content\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.discussionForm.content,\n          \"onUpdate:modelValue\": _cache[7] || (_cache[7] = $event => $setup.discussionForm.content = $event),\n          type: \"textarea\",\n          rows: 6,\n          placeholder: \"请输入讨论内容\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"model\", \"rules\"])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"title\"]), _createCommentVNode(\" 回复对话框 \"), _createVNode(_component_el_dialog, {\n    modelValue: $setup.showReplyDialog,\n    \"onUpdate:modelValue\": _cache[12] || (_cache[12] = $event => $setup.showReplyDialog = $event),\n    title: \"回复讨论\",\n    width: \"600px\",\n    \"close-on-click-modal\": false\n  }, {\n    footer: _withCtx(() => [_createVNode(_component_el_button, {\n      onClick: _cache[11] || (_cache[11] = $event => $setup.showReplyDialog = false)\n    }, {\n      default: _withCtx(() => _cache[29] || (_cache[29] = [_createTextVNode(\"取消\")])),\n      _: 1 /* STABLE */,\n      __: [29]\n    }), _createVNode(_component_el_button, {\n      type: \"primary\",\n      onClick: $setup.submitReply,\n      loading: $setup.submitting\n    }, {\n      default: _withCtx(() => _cache[30] || (_cache[30] = [_createTextVNode(\" 发布回复 \")])),\n      _: 1 /* STABLE */,\n      __: [30]\n    }, 8 /* PROPS */, [\"onClick\", \"loading\"])]),\n    default: _withCtx(() => [$setup.replyingToDiscussion ? (_openBlock(), _createElementBlock(\"div\", _hoisted_18, [_createElementVNode(\"h4\", null, \"回复：\" + _toDisplayString($setup.replyingToDiscussion.title), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_19, [_createElementVNode(\"p\", null, [_createElementVNode(\"strong\", null, _toDisplayString($setup.replyingToDiscussion.userName), 1 /* TEXT */), _cache[28] || (_cache[28] = _createTextVNode(\" 说：\"))]), _createElementVNode(\"p\", null, _toDisplayString($setup.replyingToDiscussion.content), 1 /* TEXT */)])])) : _createCommentVNode(\"v-if\", true), _createVNode(_component_el_form, {\n      ref: \"replyFormRef\",\n      model: $setup.replyForm,\n      rules: $setup.replyRules,\n      \"label-width\": \"80px\",\n      style: {\n        \"margin-top\": \"20px\"\n      }\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_form_item, {\n        label: \"回复内容\",\n        prop: \"content\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.replyForm.content,\n          \"onUpdate:modelValue\": _cache[10] || (_cache[10] = $event => $setup.replyForm.content = $event),\n          type: \"textarea\",\n          rows: 6,\n          placeholder: \"请输入您的回复...\",\n          maxlength: \"1000\",\n          \"show-word-limit\": \"\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"model\", \"rules\"])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"]), _createCommentVNode(\" 讨论详情对话框 \"), _createVNode(_component_el_dialog, {\n    modelValue: $setup.showDetailDialog,\n    \"onUpdate:modelValue\": _cache[16] || (_cache[16] = $event => $setup.showDetailDialog = $event),\n    title: \"讨论详情\",\n    width: \"800px\",\n    \"close-on-click-modal\": false\n  }, {\n    footer: _withCtx(() => [_createVNode(_component_el_button, {\n      onClick: _cache[14] || (_cache[14] = $event => $setup.showDetailDialog = false)\n    }, {\n      default: _withCtx(() => _cache[33] || (_cache[33] = [_createTextVNode(\"关闭\")])),\n      _: 1 /* STABLE */,\n      __: [33]\n    }), _createVNode(_component_el_button, {\n      type: \"primary\",\n      onClick: _cache[15] || (_cache[15] = $event => $setup.replyToDiscussion($setup.currentDiscussion))\n    }, {\n      default: _withCtx(() => _cache[34] || (_cache[34] = [_createTextVNode(\" 回复 \")])),\n      _: 1 /* STABLE */,\n      __: [34]\n    })]),\n    default: _withCtx(() => [$setup.currentDiscussion ? (_openBlock(), _createElementBlock(\"div\", _hoisted_20, [_createCommentVNode(\" 主讨论内容 \"), _createElementVNode(\"div\", _hoisted_21, [_createElementVNode(\"div\", _hoisted_22, [_createElementVNode(\"div\", _hoisted_23, [_createElementVNode(\"h2\", _hoisted_24, _toDisplayString($setup.currentDiscussion.title), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_25, [_createVNode(_component_el_tag, {\n      type: $setup.getTypeColor($setup.currentDiscussion.type),\n      size: \"small\"\n    }, {\n      default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.getTypeText($setup.currentDiscussion.type)), 1 /* TEXT */)]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"type\"])])]), _createElementVNode(\"div\", _hoisted_26, [_createElementVNode(\"div\", _hoisted_27, [_createVNode(_component_el_avatar, {\n      size: 40,\n      class: \"author-avatar\",\n      src: $setup.getAvatarUrl($setup.currentDiscussion.userAvatar)\n    }, {\n      default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.getInitial($setup.currentDiscussion.userName)), 1 /* TEXT */)]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"src\"]), _createElementVNode(\"div\", _hoisted_28, [_createElementVNode(\"div\", _hoisted_29, _toDisplayString($setup.currentDiscussion.userName), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_30, _toDisplayString($setup.formatDate($setup.currentDiscussion.createTime)), 1 /* TEXT */)])])])]), _createElementVNode(\"div\", _hoisted_31, [_createElementVNode(\"div\", _hoisted_32, _toDisplayString($setup.currentDiscussion.content), 1 /* TEXT */), $setup.currentDiscussion.attachments && $setup.currentDiscussion.attachments.length > 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_33, [_createElementVNode(\"div\", _hoisted_34, [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [_createVNode(_component_Document)]),\n      _: 1 /* STABLE */\n    }), _createElementVNode(\"span\", null, \"附件 (\" + _toDisplayString($setup.currentDiscussion.attachments.length) + \")\", 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_35, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.currentDiscussion.attachments, file => {\n      return _openBlock(), _createElementBlock(\"div\", {\n        key: file.id,\n        class: \"attachment-item\"\n      }, [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [_createVNode(_component_Document)]),\n        _: 1 /* STABLE */\n      }), _createElementVNode(\"span\", _hoisted_36, _toDisplayString(file.name), 1 /* TEXT */)]);\n    }), 128 /* KEYED_FRAGMENT */))])])) : _createCommentVNode(\"v-if\", true)])]), _createCommentVNode(\" 回复列表 \"), _createElementVNode(\"div\", _hoisted_37, [_createElementVNode(\"div\", _hoisted_38, [_createElementVNode(\"div\", _hoisted_39, [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [_createVNode(_component_ChatDotRound)]),\n      _: 1 /* STABLE */\n    }), _createElementVNode(\"span\", null, \"回复讨论 (\" + _toDisplayString($setup.discussionReplies.length) + \")\", 1 /* TEXT */)]), _createVNode(_component_el_divider)]), $setup.loadingReplies ? (_openBlock(), _createElementBlock(\"div\", _hoisted_40, [_createVNode(_component_el_icon, {\n      class: \"is-loading\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_Loading)]),\n      _: 1 /* STABLE */\n    }), _cache[31] || (_cache[31] = _createElementVNode(\"span\", null, \"加载回复中...\", -1 /* CACHED */))])) : $setup.discussionReplies.length === 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_41, [_createVNode(_component_el_empty, {\n      description: \"暂无回复\",\n      \"image-size\": 80\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_button, {\n        type: \"primary\",\n        onClick: _cache[13] || (_cache[13] = $event => $setup.replyToDiscussion($setup.currentDiscussion))\n      }, {\n        default: _withCtx(() => _cache[32] || (_cache[32] = [_createTextVNode(\" 发表回复 \")])),\n        _: 1 /* STABLE */,\n        __: [32]\n      })]),\n      _: 1 /* STABLE */\n    })])) : (_openBlock(), _createElementBlock(\"div\", _hoisted_42, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.discussionReplies, (reply, index) => {\n      return _openBlock(), _createElementBlock(\"div\", {\n        key: reply.id,\n        class: \"reply-item\"\n      }, [_createElementVNode(\"div\", _hoisted_43, \"#\" + _toDisplayString(index + 1), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_44, [_createElementVNode(\"div\", _hoisted_45, [_createElementVNode(\"div\", _hoisted_46, [_createVNode(_component_el_avatar, {\n        size: 32,\n        class: \"reply-avatar\",\n        src: $setup.getAvatarUrl(reply.userAvatar)\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.getInitial(reply.userName)), 1 /* TEXT */)]),\n        _: 2 /* DYNAMIC */\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"src\"]), _createElementVNode(\"div\", _hoisted_47, [_createElementVNode(\"span\", _hoisted_48, _toDisplayString(reply.userName), 1 /* TEXT */), _createVNode(_component_el_tag, {\n        type: reply.userRole === 'TEACHER' ? 'warning' : 'info',\n        size: \"small\",\n        class: \"role-tag\"\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString(reply.userRole === 'TEACHER' ? '教师' : '学生'), 1 /* TEXT */)]),\n        _: 2 /* DYNAMIC */\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"type\"])])]), _createElementVNode(\"div\", _hoisted_49, _toDisplayString($setup.formatDate(reply.createTime)), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_50, _toDisplayString(reply.content), 1 /* TEXT */)])]);\n    }), 128 /* KEYED_FRAGMENT */))]))])])) : _createCommentVNode(\"v-if\", true)]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_card", "header", "_withCtx", "_createElementVNode", "_hoisted_2", "_hoisted_3", "$setup", "<PERSON><PERSON><PERSON>er", "_createBlock", "_component_el_select", "currentTeamId", "$event", "placeholder", "onChange", "loadDiscussions", "_Fragment", "_renderList", "myTeams", "team", "_component_el_option", "key", "id", "label", "name", "projectName", "value", "currentTeam", "_hoisted_4", "_hoisted_5", "_toDisplayString", "_component_el_tag", "size", "type", "_cache", "_hoisted_6", "_component_el_button", "onClick", "showCreateDialog", "icon", "_ctx", "Plus", "Refresh", "_hoisted_7", "discussions", "length", "loading", "_hoisted_8", "_component_el_empty", "description", "_hoisted_9", "discussion", "shadow", "viewDiscussionDetail", "title", "_hoisted_10", "content", "_hoisted_11", "subType", "getTypeColor", "getTypeText", "_hoisted_12", "userName", "_hoisted_13", "_hoisted_14", "_component_el_icon", "_component_Clock", "formatDate", "createTime", "attachments", "_hoisted_15", "_component_Document", "_hoisted_16", "_withModifiers", "replyToDiscussion", "ChatDotRound", "canEdit", "editDiscussion", "Edit", "canDelete", "deleteDiscussion", "Delete", "total", "_hoisted_17", "_component_el_pagination", "currentPage", "pageSize", "layout", "onSizeChange", "onCurrentChange", "_createCommentVNode", "_component_el_dialog", "editingDiscussion", "width", "footer", "submitDiscussion", "submitting", "_component_el_form", "ref", "model", "discussionForm", "rules", "formRules", "_component_el_form_item", "prop", "_component_el_input", "rows", "showReplyDialog", "submitReply", "replyingToDiscussion", "_hoisted_18", "_hoisted_19", "replyForm", "replyRules", "style", "maxlength", "showDetailDialog", "currentDiscussion", "_hoisted_20", "_hoisted_21", "_hoisted_22", "_hoisted_23", "_hoisted_24", "_hoisted_25", "_hoisted_26", "_hoisted_27", "_component_el_avatar", "src", "getAvatarUrl", "userAvatar", "getInitial", "_hoisted_28", "_hoisted_29", "_hoisted_30", "_hoisted_31", "_hoisted_32", "_hoisted_33", "_hoisted_34", "_hoisted_35", "file", "_hoisted_36", "_hoisted_37", "_hoisted_38", "_hoisted_39", "_component_ChatDotRound", "discussionReplies", "_component_el_divider", "loadingReplies", "_hoisted_40", "_component_Loading", "_hoisted_41", "_hoisted_42", "reply", "index", "_hoisted_43", "_hoisted_44", "_hoisted_45", "_hoisted_46", "_hoisted_47", "_hoisted_48", "userRole", "_hoisted_49", "_hoisted_50"], "sources": ["D:\\workspace\\idea\\worker\\work_cli\\src\\views\\collaboration\\DiscussionView.vue"], "sourcesContent": ["<template>\n  <div class=\"discussion\">\n    <el-card>\n      <template #header>\n        <div class=\"card-header\">\n          <h3>项目讨论</h3>\n          <div class=\"header-actions\">\n            <el-select\n              v-if=\"isTeacher\"\n              v-model=\"currentTeamId\"\n              placeholder=\"选择团队\"\n              @change=\"loadDiscussions\"\n            >\n              <el-option\n                v-for=\"team in myTeams\"\n                :key=\"team.id\"\n                :label=\"`${team.name} (${team.projectName || '未知项目'})`\"\n                :value=\"team.id\"\n              />\n            </el-select>\n            <div v-else-if=\"currentTeam\" class=\"current-team-info\">\n              <span class=\"team-name\">{{ currentTeam.name }}</span>\n              <el-tag size=\"small\" type=\"info\">我的团队</el-tag>\n            </div>\n            <div v-else class=\"no-team-info\">\n              <el-tag size=\"small\" type=\"warning\">未加入团队</el-tag>\n            </div>\n\n            <el-button\n              v-if=\"currentTeamId\"\n              type=\"primary\"\n              @click=\"showCreateDialog = true\"\n              :icon=\"Plus\"\n            >\n              发起讨论\n            </el-button>\n            <el-button @click=\"loadDiscussions\" :icon=\"Refresh\">\n              刷新\n            </el-button>\n          </div>\n        </div>\n      </template>\n      \n      <!-- 讨论列表 -->\n      <div class=\"discussion-list\" v-loading=\"loading\">\n        <div v-if=\"discussions.length === 0 && !loading\" class=\"empty-state\">\n          <el-empty description=\"暂无讨论记录\">\n            <el-button type=\"primary\" @click=\"showCreateDialog = true\">\n              发起讨论\n            </el-button>\n          </el-empty>\n        </div>\n        \n        <div v-else class=\"discussion-grid\">\n          <div v-for=\"discussion in discussions\" :key=\"discussion.id\" class=\"discussion-card\">\n            <el-card shadow=\"hover\" @click=\"viewDiscussionDetail(discussion)\">\n              <h4>{{ discussion.title || '无标题讨论' }}</h4>\n              <p class=\"discussion-description\">{{ discussion.content }}</p>\n\n              <div class=\"discussion-meta\">\n                <el-tag v-if=\"discussion.subType\" :type=\"getTypeColor(discussion.subType)\" size=\"small\">\n                  {{ getTypeText(discussion.subType) }}\n                </el-tag>\n                <span class=\"discussion-author\">{{ discussion.userName }}</span>\n              </div>\n\n              <div class=\"discussion-info\">\n                <div class=\"info-item\">\n                  <el-icon><Clock /></el-icon>\n                  <span>{{ formatDate(discussion.createTime) }}</span>\n                </div>\n                <div class=\"info-item\" v-if=\"discussion.attachments && discussion.attachments.length > 0\">\n                  <el-icon><Document /></el-icon>\n                  <span>{{ discussion.attachments.length }}个附件</span>\n                </div>\n              </div>\n\n              <div class=\"discussion-footer\">\n                <el-button size=\"small\" @click.stop=\"viewDiscussionDetail(discussion)\">\n                  查看详情\n                </el-button>\n                <el-button size=\"small\" @click.stop=\"replyToDiscussion(discussion)\" :icon=\"ChatDotRound\">\n                  回复\n                </el-button>\n                <el-button v-if=\"canEdit(discussion)\" size=\"small\" @click.stop=\"editDiscussion(discussion)\" :icon=\"Edit\">\n                  编辑\n                </el-button>\n                <el-button v-if=\"canDelete(discussion)\" size=\"small\" type=\"danger\" @click.stop=\"deleteDiscussion(discussion.id)\" :icon=\"Delete\">\n                  删除\n                </el-button>\n              </div>\n            </el-card>\n          </div>\n        </div>\n      </div>\n      \n      <!-- 分页 -->\n      <div v-if=\"total > 0\" class=\"pagination\">\n        <el-pagination\n          v-model:current-page=\"currentPage\"\n          v-model:page-size=\"pageSize\"\n          :total=\"total\"\n          :page-sizes=\"[10, 20, 50]\"\n          layout=\"total, sizes, prev, pager, next, jumper\"\n          @size-change=\"loadDiscussions\"\n          @current-change=\"loadDiscussions\"\n        />\n      </div>\n    </el-card>\n    \n    <!-- 创建/编辑讨论对话框 -->\n    <el-dialog\n      v-model=\"showCreateDialog\"\n      :title=\"editingDiscussion ? '编辑讨论' : '发起讨论'\"\n      width=\"600px\"\n    >\n      <el-form\n        ref=\"formRef\"\n        :model=\"discussionForm\"\n        :rules=\"formRules\"\n        label-width=\"80px\"\n      >\n        <el-form-item label=\"讨论类型\" prop=\"type\">\n          <el-select v-model=\"discussionForm.type\" placeholder=\"请选择讨论类型\">\n            <el-option label=\"一般讨论\" value=\"DISCUSSION\" />\n            <el-option label=\"进度汇报\" value=\"PROGRESS\" />\n            <el-option label=\"问题讨论\" value=\"ISSUE\" />\n            <el-option label=\"技术提问\" value=\"QUESTION\" />\n            <el-option label=\"公告通知\" value=\"ANNOUNCEMENT\" />\n            <el-option label=\"资源分享\" value=\"RESOURCE\" />\n            <el-option label=\"其他\" value=\"OTHER\" />\n          </el-select>\n        </el-form-item>\n        \n        <el-form-item label=\"讨论标题\" prop=\"title\">\n          <el-input v-model=\"discussionForm.title\" placeholder=\"请输入讨论标题\" />\n        </el-form-item>\n        \n        <el-form-item label=\"讨论内容\" prop=\"content\">\n          <el-input\n            v-model=\"discussionForm.content\"\n            type=\"textarea\"\n            :rows=\"6\"\n            placeholder=\"请输入讨论内容\"\n          />\n        </el-form-item>\n      </el-form>\n      \n      <template #footer>\n        <el-button @click=\"showCreateDialog = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"submitDiscussion\" :loading=\"submitting\">\n          {{ editingDiscussion ? '更新' : '发布' }}\n        </el-button>\n      </template>\n    </el-dialog>\n\n    <!-- 回复对话框 -->\n    <el-dialog\n      v-model=\"showReplyDialog\"\n      title=\"回复讨论\"\n      width=\"600px\"\n      :close-on-click-modal=\"false\"\n    >\n      <div v-if=\"replyingToDiscussion\" class=\"reply-context\">\n        <h4>回复：{{ replyingToDiscussion.title }}</h4>\n        <div class=\"original-content\">\n          <p><strong>{{ replyingToDiscussion.userName }}</strong> 说：</p>\n          <p>{{ replyingToDiscussion.content }}</p>\n        </div>\n      </div>\n\n      <el-form\n        ref=\"replyFormRef\"\n        :model=\"replyForm\"\n        :rules=\"replyRules\"\n        label-width=\"80px\"\n        style=\"margin-top: 20px;\"\n      >\n        <el-form-item label=\"回复内容\" prop=\"content\">\n          <el-input\n            v-model=\"replyForm.content\"\n            type=\"textarea\"\n            :rows=\"6\"\n            placeholder=\"请输入您的回复...\"\n            maxlength=\"1000\"\n            show-word-limit\n          />\n        </el-form-item>\n      </el-form>\n\n      <template #footer>\n        <el-button @click=\"showReplyDialog = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"submitReply\" :loading=\"submitting\">\n          发布回复\n        </el-button>\n      </template>\n    </el-dialog>\n\n    <!-- 讨论详情对话框 -->\n    <el-dialog\n      v-model=\"showDetailDialog\"\n      title=\"讨论详情\"\n      width=\"800px\"\n      :close-on-click-modal=\"false\"\n    >\n      <div v-if=\"currentDiscussion\" class=\"discussion-detail\">\n        <!-- 主讨论内容 -->\n        <div class=\"main-discussion\">\n          <div class=\"discussion-header\">\n            <div class=\"header-left\">\n              <h2 class=\"discussion-title\">{{ currentDiscussion.title }}</h2>\n              <div class=\"discussion-tags\">\n                <el-tag :type=\"getTypeColor(currentDiscussion.type)\" size=\"small\">\n                  {{ getTypeText(currentDiscussion.type) }}\n                </el-tag>\n              </div>\n            </div>\n            <div class=\"header-right\">\n              <div class=\"author-info\">\n                <el-avatar :size=\"40\" class=\"author-avatar\" :src=\"getAvatarUrl(currentDiscussion.userAvatar)\">\n                  {{ getInitial(currentDiscussion.userName) }}\n                </el-avatar>\n                <div class=\"author-details\">\n                  <div class=\"author-name\">{{ currentDiscussion.userName }}</div>\n                  <div class=\"publish-time\">{{ formatDate(currentDiscussion.createTime) }}</div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div class=\"discussion-content\">\n            <div class=\"content-text\">{{ currentDiscussion.content }}</div>\n\n            <div v-if=\"currentDiscussion.attachments && currentDiscussion.attachments.length > 0\" class=\"attachments-section\">\n              <div class=\"attachments-title\">\n                <el-icon><Document /></el-icon>\n                <span>附件 ({{ currentDiscussion.attachments.length }})</span>\n              </div>\n              <div class=\"attachments-list\">\n                <div v-for=\"file in currentDiscussion.attachments\" :key=\"file.id\" class=\"attachment-item\">\n                  <el-icon><Document /></el-icon>\n                  <span class=\"file-name\">{{ file.name }}</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- 回复列表 -->\n        <div class=\"replies-section\">\n          <div class=\"replies-header\">\n            <div class=\"replies-title\">\n              <el-icon><ChatDotRound /></el-icon>\n              <span>回复讨论 ({{ discussionReplies.length }})</span>\n            </div>\n            <el-divider />\n          </div>\n\n          <div v-if=\"loadingReplies\" class=\"loading-state\">\n            <el-icon class=\"is-loading\"><Loading /></el-icon>\n            <span>加载回复中...</span>\n          </div>\n\n          <div v-else-if=\"discussionReplies.length === 0\" class=\"empty-state\">\n            <el-empty description=\"暂无回复\" :image-size=\"80\">\n              <el-button type=\"primary\" @click=\"replyToDiscussion(currentDiscussion)\">\n                发表回复\n              </el-button>\n            </el-empty>\n          </div>\n\n          <div v-else class=\"replies-list\">\n            <div v-for=\"(reply, index) in discussionReplies\" :key=\"reply.id\" class=\"reply-item\">\n              <div class=\"reply-number\">#{{ index + 1 }}</div>\n              <div class=\"reply-content-wrapper\">\n                <div class=\"reply-header\">\n                  <div class=\"reply-author\">\n                    <el-avatar :size=\"32\" class=\"reply-avatar\" :src=\"getAvatarUrl(reply.userAvatar)\">\n                      {{ getInitial(reply.userName) }}\n                    </el-avatar>\n                    <div class=\"author-info\">\n                      <span class=\"author-name\">{{ reply.userName }}</span>\n                      <el-tag :type=\"reply.userRole === 'TEACHER' ? 'warning' : 'info'\" size=\"small\" class=\"role-tag\">\n                        {{ reply.userRole === 'TEACHER' ? '教师' : '学生' }}\n                      </el-tag>\n                    </div>\n                  </div>\n                  <div class=\"reply-time\">\n                    {{ formatDate(reply.createTime) }}\n                  </div>\n                </div>\n                <div class=\"reply-content\">\n                  {{ reply.content }}\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <template #footer>\n        <el-button @click=\"showDetailDialog = false\">关闭</el-button>\n        <el-button type=\"primary\" @click=\"replyToDiscussion(currentDiscussion)\">\n          回复\n        </el-button>\n      </template>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { ref, reactive, onMounted, computed } from 'vue'\nimport { useStore } from 'vuex'\nimport { recordAPI, teamAPI, projectAPI } from '@/api'\nimport { ElMessage, ElMessageBox } from 'element-plus'\nimport { getAvatarUrl, getInitial } from '@/utils/avatar'\nimport {\n  Document,\n  ChatDotRound,\n  Edit,\n  Delete,\n  Plus,\n  Loading,\n  Refresh,\n  Clock\n} from '@element-plus/icons-vue'\n\nexport default {\n  name: 'DiscussionView',\n  components: {\n    Document,\n    ChatDotRound,\n    Edit,\n    Delete,\n    Plus,\n    Loading,\n    Refresh,\n    Clock\n  },\n  setup() {\n    const store = useStore()\n    const formRef = ref()\n    const replyFormRef = ref()\n    \n    const loading = ref(false)\n    const submitting = ref(false)\n    const showCreateDialog = ref(false)\n    const showDetailDialog = ref(false)\n    const showReplyDialog = ref(false)\n    const editingDiscussion = ref(null)\n    const currentDiscussion = ref(null)\n    const replyingToDiscussion = ref(null)\n    const discussionReplies = ref([])\n    const loadingReplies = ref(false)\n    \n    const discussions = ref([])\n    const myTeams = ref([])\n    const currentTeam = ref(null)\n    const currentTeamId = ref(null)\n    const currentPage = ref(1)\n    const pageSize = ref(10)\n    const total = ref(0)\n    \n    const discussionForm = reactive({\n      type: '',\n      title: '',\n      content: ''\n    })\n\n    const replyForm = reactive({\n      content: ''\n    })\n    \n    const formRules = {\n      type: [\n        { required: true, message: '请选择讨论类型', trigger: 'change' }\n      ],\n      title: [\n        { required: true, message: '请输入讨论标题', trigger: 'blur' },\n        { min: 2, max: 100, message: '标题长度在 2 到 100 个字符', trigger: 'blur' }\n      ],\n      content: [\n        { required: true, message: '请输入讨论内容', trigger: 'blur' },\n        { min: 10, max: 2000, message: '内容长度在 10 到 2000 个字符', trigger: 'blur' }\n      ]\n    }\n\n    const replyRules = {\n      content: [\n        { required: true, message: '请输入回复内容', trigger: 'blur' },\n        { min: 5, max: 1000, message: '回复内容长度在 5 到 1000 个字符', trigger: 'blur' }\n      ]\n    }\n    \n    // 使用与DashboardView相同的方式获取用户信息\n    const currentUser = computed(() => store.getters.currentUser)\n    const isTeacher = computed(() => store.getters.isTeacher)\n    const isStudent = computed(() => store.getters.isStudent)\n\n    // 加载我的团队\n    const loadMyTeams = async () => {\n      try {\n        if (isTeacher.value) {\n          // 首先获取教师发布的项目\n          const projectsResponse = await projectAPI.getMyProjects()\n          const myProjects = projectsResponse?.records || []\n\n          if (myProjects.length > 0) {\n            // 获取所有项目相关的团队\n            const allTeams = []\n\n            for (const project of myProjects) {\n              try {\n                const teamsResponse = await teamAPI.getProjectTeams(project.id)\n\n                if (teamsResponse?.records) {\n                  // 为每个团队添加项目信息\n                  const teamsWithProject = teamsResponse.records.map(team => ({\n                    ...team,\n                    projectId: project.id,\n                    projectName: project.name\n                  }))\n                  allTeams.push(...teamsWithProject)\n                }\n              } catch (err) {\n                console.warn(`获取项目 ${project.id} 的团队失败:`, err)\n              }\n            }\n\n            if (allTeams.length > 0) {\n              myTeams.value = allTeams\n              currentTeamId.value = allTeams[0].id\n              await loadDiscussions()\n            } else {\n              ElMessage.info('暂无团队申请您发布的项目')\n              myTeams.value = []\n            }\n          } else {\n            ElMessage.info('您还没有发布任何项目，无法查看团队讨论')\n            myTeams.value = []\n          }\n        } else {\n          // 使用真实API获取学生的团队\n          const response = await teamAPI.getMyTeam()\n\n          if (response) {\n            currentTeam.value = response\n            currentTeamId.value = response.id\n            myTeams.value = [response]  // 学生只有一个团队\n            await loadDiscussions()\n          } else {\n            ElMessage.warning('您还没有加入任何团队')\n            myTeams.value = []\n          }\n        }\n      } catch (error) {\n        console.error('加载团队信息失败:', error)\n        ElMessage.error(`加载团队信息失败: ${error.message || '未知错误'}`)\n        myTeams.value = []\n      }\n    }\n\n    // 加载讨论列表\n    const loadDiscussions = async () => {\n      if (!currentTeamId.value) {\n        return\n      }\n\n      try {\n        loading.value = true\n\n        // 使用真实API获取团队记录数据\n        const params = {\n          page: currentPage.value,\n          size: pageSize.value,\n          // 不指定type，获取所有类型，然后在前端过滤掉任务类型\n        }\n\n        const response = await recordAPI.getTeamRecords(currentTeamId.value, params)\n        console.log('团队记录原始响应:', response)\n\n        if (response && response.records) {\n          console.log('记录数据示例:', response.records[0])\n\n          // 处理返回的数据，过滤掉任务类型\n          const filteredRecords = response.records.filter(record =>\n            record.type !== 'TASK' &&\n            record.type !== 'TASK_SUBMIT' &&\n            record.type !== 'TASK_REVIEW' &&\n            record.type !== 'SUBMISSION'\n          )\n\n          // 处理用户信息显示\n          const recordsWithUserInfo = filteredRecords.map(record => {\n            // 从creator对象中获取用户信息\n            let userName = '未知用户'\n            let userRole = 'STUDENT'\n            let userId = null\n\n            if (record.creator) {\n              userName = record.creator.realName || record.creator.username || '未知用户'\n              userRole = record.creator.role || 'STUDENT'\n              userId = record.creator.id\n            }\n\n            console.log('处理记录用户信息:', {\n              recordId: record.id,\n              creator: record.creator,\n              最终结果: {\n                userName: userName,\n                userRole: userRole,\n                userId: userId\n              }\n            })\n\n            return {\n              ...record,\n              userName: userName,\n              userRole: userRole,\n              userId: userId,\n              userAvatar: record.creator?.avatar || null\n            }\n          })\n\n          discussions.value = recordsWithUserInfo.map(record => ({\n            id: record.id,\n            title: record.title || '无标题',\n            content: record.content || '',\n            type: record.type || 'DISCUSSION',\n            userName: record.userName,\n            userRole: record.userRole,\n            userId: record.userId,\n            userAvatar: record.userAvatar, // 确保头像数据被传递\n            createTime: record.createTime,\n            updateTime: record.updateTime,\n            attachments: record.attachments || []\n          }))\n\n          total.value = recordsWithUserInfo.length\n        } else {\n          discussions.value = []\n          total.value = 0\n        }\n\n        loading.value = false\n\n      } catch (error) {\n        console.error('加载讨论列表失败:', error)\n        ElMessage.error('加载讨论列表失败')\n        loading.value = false\n        discussions.value = []\n        total.value = 0\n      }\n    }\n    \n    // 提交讨论\n    const submitDiscussion = async () => {\n      if (!formRef.value) return\n\n      // 检查必要的数据\n      if (!currentTeamId.value) {\n        ElMessage.error('请先选择团队')\n        return\n      }\n\n      try {\n        await formRef.value.validate()\n        submitting.value = true\n\n        // 准备提交的数据\n        const recordData = {\n          title: discussionForm.title,\n          content: discussionForm.content,\n          type: 'DISCUSSION', // 主类型固定为DISCUSSION\n          subType: discussionForm.type, // 用户选择的类型作为子类型\n          teamId: currentTeamId.value,\n          projectId: currentTeam.value?.projectId || null\n        }\n\n        console.log('提交讨论数据:', recordData)\n        console.log('当前用户信息:', currentUser.value)\n        console.log('localStorage token:', localStorage.getItem('token'))\n\n        if (editingDiscussion.value) {\n          // 更新现有讨论\n          console.log('更新讨论，ID:', editingDiscussion.value.id)\n          const response = await recordAPI.updateRecord(editingDiscussion.value.id, recordData)\n          console.log('更新讨论响应:', response)\n          ElMessage.success('讨论更新成功')\n          // 重新加载讨论列表\n          await loadDiscussions()\n        } else {\n          // 创建新讨论\n          console.log('创建新讨论')\n          const response = await recordAPI.createRecord(recordData)\n          console.log('创建讨论响应:', response)\n          ElMessage.success('讨论发布成功')\n          // 重新加载讨论列表\n          await loadDiscussions()\n        }\n\n        showCreateDialog.value = false\n        resetForm()\n        submitting.value = false\n\n      } catch (error) {\n        console.error('提交讨论失败:', error)\n        ElMessage.error(`提交讨论失败: ${error.response?.data?.message || error.message || '未知错误'}`)\n        submitting.value = false\n      }\n    }\n    \n    // 重置表单\n    const resetForm = () => {\n      Object.assign(discussionForm, {\n        type: '',\n        title: '',\n        content: ''\n      })\n      editingDiscussion.value = null\n    }\n    \n    // 编辑讨论\n    const editDiscussion = (discussion) => {\n      editingDiscussion.value = discussion\n      Object.assign(discussionForm, {\n        type: discussion.subType || discussion.type, // 优先使用subType，兼容旧数据\n        title: discussion.title,\n        content: discussion.content\n      })\n      showCreateDialog.value = true\n    }\n    \n    // 删除讨论\n    const deleteDiscussion = async (id) => {\n      try {\n        await ElMessageBox.confirm('确定要删除这条讨论吗？', '确认删除', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        })\n\n        // 使用真实API删除讨论\n        const response = await recordAPI.deleteRecord(id)\n        if (response) {\n          ElMessage.success('讨论删除成功')\n          // 重新加载讨论列表\n          await loadDiscussions()\n        }\n\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('删除讨论失败:', error)\n          ElMessage.error('删除讨论失败')\n        }\n      }\n    }\n\n    // 回复讨论\n    const replyToDiscussion = (discussion) => {\n      replyingToDiscussion.value = discussion\n      replyForm.content = ''\n      showReplyDialog.value = true\n    }\n\n    // 提交回复\n    const submitReply = async () => {\n      if (!replyFormRef.value) return\n\n      // 检查必要的数据\n      if (!currentTeamId.value) {\n        ElMessage.error('请先选择团队')\n        return\n      }\n\n      if (!replyingToDiscussion.value) {\n        ElMessage.error('回复目标不存在')\n        return\n      }\n\n      try {\n        await replyFormRef.value.validate()\n      } catch (error) {\n        return\n      }\n\n      try {\n        submitting.value = true\n\n        // 准备回复数据\n        const replyData = {\n          title: `回复：${replyingToDiscussion.value.title}`,\n          content: replyForm.content,\n          type: 'DISCUSSION', // 使用DISCUSSION类型而不是REPLY\n          parentId: replyingToDiscussion.value.id, // 父讨论ID\n          // 不绑定teamId，这样回复只会在项目详情界面显示，不会在项目讨论界面显示\n          projectId: currentTeam.value?.projectId || null\n        }\n\n        console.log('提交回复数据:', replyData)\n        console.log('当前用户信息:', currentUser.value)\n        console.log('localStorage token:', localStorage.getItem('token'))\n\n        const response = await recordAPI.createRecord(replyData)\n        console.log('回复响应:', response)\n\n        ElMessage.success('回复发布成功')\n        showReplyDialog.value = false\n        replyForm.content = ''\n\n        // 如果详情对话框是打开的，重新加载回复\n        if (showDetailDialog.value && currentDiscussion.value) {\n          await loadDiscussionReplies(currentDiscussion.value.id)\n        }\n\n        replyingToDiscussion.value = null\n        // 重新加载讨论列表\n        await loadDiscussions()\n\n        submitting.value = false\n\n      } catch (error) {\n        console.error('提交回复失败:', error)\n        ElMessage.error(`提交回复失败: ${error.response?.data?.message || error.message || '未知错误'}`)\n        submitting.value = false\n      }\n    }\n\n    // 加载讨论回复\n    const loadDiscussionReplies = async (discussionId) => {\n      try {\n        loadingReplies.value = true\n        console.log('开始加载讨论回复，讨论ID:', discussionId)\n        console.log('请求URL将是:', `/api/records/${discussionId}/replies`)\n\n        const response = await recordAPI.getDiscussionReplies(discussionId)\n        console.log('回复API响应:', response)\n\n        // 检查响应结构\n        console.log('响应结构检查:', {\n          response: response,\n          responseType: typeof response,\n          isArray: Array.isArray(response),\n          responseLength: Array.isArray(response) ? response.length : 'not array'\n        })\n\n        if (response && Array.isArray(response)) {\n          // 处理回复数据，确保用户信息正确显示\n          discussionReplies.value = response.map(reply => ({\n            ...reply,\n            userName: reply.creator?.realName || reply.creator?.username || '未知用户',\n            userRole: reply.creator?.role || 'STUDENT',\n            userId: reply.creator?.id,\n            userAvatar: reply.creator?.avatar || null\n          }))\n          console.log('处理后的回复数据:', discussionReplies.value)\n        } else if (response === null || response === undefined) {\n          console.log('API返回null/undefined数据，可能没有回复')\n          discussionReplies.value = []\n        } else {\n          console.log('API返回空数据或格式不正确，响应:', response)\n          discussionReplies.value = []\n        }\n\n        loadingReplies.value = false\n      } catch (error) {\n        console.error('加载回复失败:', error)\n        console.error('错误详情:', {\n          message: error.message,\n          response: error.response?.data,\n          status: error.response?.status,\n          url: error.config?.url\n        })\n        ElMessage.error(`加载回复失败: ${error.response?.data?.message || error.message || '未知错误'}`)\n        loadingReplies.value = false\n        discussionReplies.value = []\n      }\n    }\n\n    // 查看讨论详情\n    const viewDiscussionDetail = async (discussion) => {\n      currentDiscussion.value = discussion\n      showDetailDialog.value = true\n\n      // 先清空之前的回复数据\n      discussionReplies.value = []\n\n      // 加载该讨论的回复\n      console.log('准备加载讨论回复，讨论对象:', {\n        id: discussion.id,\n        title: discussion.title,\n        type: discussion.type,\n        parentId: discussion.parentId\n      })\n      await loadDiscussionReplies(discussion.id)\n    }\n\n    // 权限检查\n    const canEdit = (discussion) => {\n      return discussion.userId === currentUser.value?.id\n    }\n    \n    const canDelete = (discussion) => {\n      return discussion.userId === currentUser.value?.id || currentUser.value?.role === 'TEACHER'\n    }\n    \n    // 工具方法\n    const formatDate = (date) => {\n      if (!date) return ''\n      return new Date(date).toLocaleString('zh-CN')\n    }\n    \n    const getTypeColor = (type) => {\n      const colorMap = {\n        'DISCUSSION': 'primary',\n        'PROGRESS': 'success',\n        'ISSUE': 'warning',\n        'QUESTION': 'info',\n        'ANNOUNCEMENT': 'danger',\n        'RESOURCE': 'info',\n        'OTHER': ''\n      }\n      return colorMap[type] || ''\n    }\n\n    const getTypeText = (type) => {\n      const textMap = {\n        'DISCUSSION': '一般讨论',\n        'PROGRESS': '进度汇报',\n        'ISSUE': '问题讨论',\n        'QUESTION': '技术提问',\n        'ANNOUNCEMENT': '公告通知',\n        'RESOURCE': '资源分享',\n        'OTHER': '其他'\n      }\n      return textMap[type] || type\n    }\n    \n    onMounted(() => {\n      console.log('组件挂载时的用户信息:', {\n        currentUser: currentUser.value,\n        token: localStorage.getItem('token'),\n        storeUser: store.state.user,\n        isAuthenticated: store.state.isAuthenticated\n      })\n      loadMyTeams()\n    })\n    \n    return {\n      loading,\n      submitting,\n      showCreateDialog,\n      showDetailDialog,\n      showReplyDialog,\n      editingDiscussion,\n      currentDiscussion,\n      replyingToDiscussion,\n      discussionReplies,\n      loadingReplies,\n      discussions,\n      myTeams,\n      currentTeam,\n      currentTeamId,\n      currentPage,\n      pageSize,\n      total,\n      discussionForm,\n      replyForm,\n      formRules,\n      replyRules,\n      formRef,\n      replyFormRef,\n      currentUser,\n      isTeacher,\n      isStudent,\n      store,\n      loadDiscussions,\n      loadDiscussionReplies,\n      submitDiscussion,\n      submitReply,\n      editDiscussion,\n      deleteDiscussion,\n      replyToDiscussion,\n      viewDiscussionDetail,\n      canEdit,\n      canDelete,\n      formatDate,\n      getTypeColor,\n      getTypeText,\n      // 头像工具函数\n      getAvatarUrl,\n      getInitial\n    }\n  }\n}\n</script>\n\n<style scoped>\n.discussion {\n  padding: 0;\n}\n\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.card-header h3 {\n  margin: 0;\n}\n\n.header-actions {\n  display: flex;\n  gap: 12px;\n  align-items: center;\n}\n\n.current-team-info {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.team-name {\n  font-weight: 500;\n  color: #303133;\n}\n\n.discussion-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\n  gap: 20px;\n  margin-top: 16px;\n}\n\n.discussion-card {\n  cursor: pointer;\n  transition: all 0.3s ease;\n  height: 100%;\n}\n\n.discussion-card:hover {\n  transform: translateY(-4px);\n}\n\n.discussion-card .el-card {\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n}\n\n.discussion-card .el-card__body {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  min-height: 200px; /* 设置最小高度，确保卡片一致性 */\n}\n\n.discussion-card h4 {\n  margin: 0 0 10px 0;\n  color: #303133;\n  cursor: pointer;\n}\n\n.discussion-card h4:hover {\n  color: #409eff;\n}\n\n.discussion-description {\n  color: #606266;\n  margin: 10px 0;\n  display: -webkit-box;\n  -webkit-line-clamp: 2; /* 只显示2行 */\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  line-height: 1.5;\n  height: 3em; /* 固定高度为2行 (1.5 * 2 = 3em) */\n  flex: none; /* 不允许弹性伸缩，保持固定高度 */\n}\n\n.discussion-meta {\n  display: flex;\n  gap: 8px;\n  align-items: center;\n  margin: 12px 0;\n  padding-top: 8px;\n  border-top: 1px solid #f0f0f0;\n}\n\n.discussion-author {\n  color: #909399;\n  font-size: 13px;\n}\n\n.discussion-info {\n  margin: 12px 0;\n}\n\n.info-item {\n  display: flex;\n  align-items: center;\n  gap: 6px;\n  margin-bottom: 6px;\n  color: #909399;\n  font-size: 13px;\n}\n\n.discussion-footer {\n  margin-top: auto;\n  text-align: right;\n}\n\n.pagination {\n  margin-top: var(--space-6);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  padding: var(--space-4) 0;\n}\n\n/* 讨论详情对话框样式 */\n.discussion-detail {\n  max-height: 70vh;\n  overflow-y: auto;\n}\n\n.main-discussion {\n  background: #fff;\n  border-radius: 8px;\n  margin-bottom: 24px;\n}\n\n.discussion-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  padding-bottom: 16px;\n  border-bottom: 2px solid #f0f0f0;\n  margin-bottom: 20px;\n}\n\n.header-left {\n  flex: 1;\n}\n\n.discussion-title {\n  margin: 0 0 12px 0;\n  color: #303133;\n  font-size: 20px;\n  font-weight: 600;\n  line-height: 1.4;\n}\n\n.discussion-tags {\n  display: flex;\n  gap: 8px;\n}\n\n.header-right {\n  margin-left: 20px;\n}\n\n.author-info {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n}\n\n.author-avatar {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  font-weight: 600;\n}\n\n.author-details {\n  text-align: right;\n}\n\n.author-name {\n  display: block;\n  font-weight: 600;\n  color: #303133;\n  margin-bottom: 4px;\n}\n\n.publish-time {\n  font-size: 13px;\n  color: #909399;\n}\n\n.discussion-content {\n  padding: 0;\n}\n\n.content-text {\n  color: #606266;\n  line-height: 1.8;\n  font-size: 15px;\n  margin-bottom: 20px;\n  white-space: pre-wrap;\n}\n\n.attachments-section {\n  background: #f8f9fa;\n  border-radius: 6px;\n  padding: 16px;\n  border-left: 4px solid #409eff;\n}\n\n.attachments-title {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-weight: 600;\n  color: #303133;\n  margin-bottom: 12px;\n}\n\n.attachments-list {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.attachment-item {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  padding: 8px 12px;\n  background: white;\n  border-radius: 4px;\n  border: 1px solid #e4e7ed;\n  transition: all 0.3s ease;\n}\n\n.attachment-item:hover {\n  border-color: #409eff;\n  background: #f0f9ff;\n}\n\n.file-name {\n  color: #606266;\n  font-size: 14px;\n}\n\n/* 回复列表样式 */\n.replies-section {\n  margin-top: 32px;\n  padding-top: 24px;\n  border-top: 2px solid #f0f0f0;\n}\n\n.replies-header {\n  margin-bottom: 20px;\n}\n\n.replies-title {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-size: 16px;\n  font-weight: 600;\n  color: #303133;\n  margin-bottom: 16px;\n}\n\n.loading-state {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 12px;\n  padding: 40px;\n  color: #909399;\n}\n\n.empty-state {\n  text-align: center;\n  padding: 40px 20px;\n}\n\n.replies-list {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n}\n\n.reply-item {\n  display: flex;\n  gap: 12px;\n  padding: 16px;\n  background: #fafbfc;\n  border-radius: 8px;\n  border: 1px solid #e4e7ed;\n  transition: all 0.3s ease;\n}\n\n.reply-item:hover {\n  background: #f5f7fa;\n  border-color: #c0c4cc;\n}\n\n.reply-number {\n  color: #909399;\n  font-size: 12px;\n  font-weight: 600;\n  min-width: 24px;\n  text-align: center;\n  padding-top: 4px;\n}\n\n.reply-content-wrapper {\n  flex: 1;\n}\n\n.reply-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 12px;\n}\n\n.reply-author {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n}\n\n.reply-avatar {\n  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);\n  color: #303133;\n  font-weight: 600;\n}\n\n.author-info {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.author-name {\n  font-weight: 600;\n  color: #303133;\n  font-size: 14px;\n}\n\n.role-tag {\n  font-size: 11px;\n}\n\n.reply-time {\n  font-size: 12px;\n  color: #909399;\n}\n\n.reply-content {\n  color: #606266;\n  line-height: 1.6;\n  font-size: 14px;\n  white-space: pre-wrap;\n}\n</style>\n\n\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAY;;EAGZA,KAAK,EAAC;AAAa;;EAEjBA,KAAK,EAAC;AAAgB;;;EAcIA,KAAK,EAAC;;;EAC3BA,KAAK,EAAC;AAAW;;;EAGbA,KAAK,EAAC;;;EAoBnBA,KAAK,EAAC;AAAiB;;;EACuBA,KAAK,EAAC;;;;EAQ3CA,KAAK,EAAC;;;EAITA,KAAK,EAAC;AAAwB;;EAE5BA,KAAK,EAAC;AAAiB;;EAIpBA,KAAK,EAAC;AAAmB;;EAG5BA,KAAK,EAAC;AAAiB;;EACrBA,KAAK,EAAC;AAAW;;;EAIjBA,KAAK,EAAC;;;EAMRA,KAAK,EAAC;AAAmB;;;EAoBhBA,KAAK,EAAC;;;;EAkEKA,KAAK,EAAC;;;EAEhCA,KAAK,EAAC;AAAkB;;;EAwCDA,KAAK,EAAC;;;EAE7BA,KAAK,EAAC;AAAiB;;EACrBA,KAAK,EAAC;AAAmB;;EACvBA,KAAK,EAAC;AAAa;;EAClBA,KAAK,EAAC;AAAkB;;EACvBA,KAAK,EAAC;AAAiB;;EAMzBA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAa;;EAIjBA,KAAK,EAAC;AAAgB;;EACpBA,KAAK,EAAC;AAAa;;EACnBA,KAAK,EAAC;AAAc;;EAM5BA,KAAK,EAAC;AAAoB;;EACxBA,KAAK,EAAC;AAAc;;;EAE6DA,KAAK,EAAC;;;EACrFA,KAAK,EAAC;AAAmB;;EAIzBA,KAAK,EAAC;AAAkB;;EAGnBA,KAAK,EAAC;AAAW;;EAQ5BA,KAAK,EAAC;AAAiB;;EACrBA,KAAK,EAAC;AAAgB;;EACpBA,KAAK,EAAC;AAAe;;;EAODA,KAAK,EAAC;;;;EAKeA,KAAK,EAAC;;;;EAQ1CA,KAAK,EAAC;;;EAETA,KAAK,EAAC;AAAc;;EACpBA,KAAK,EAAC;AAAuB;;EAC3BA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAc;;EAIlBA,KAAK,EAAC;AAAa;;EAChBA,KAAK,EAAC;AAAa;;EAMxBA,KAAK,EAAC;AAAY;;EAIpBA,KAAK,EAAC;AAAe;;;;;;;;;;;;;;;;;;;;;uBAlSxCC,mBAAA,CAkTM,OAlTNC,UAkTM,GAjTJC,YAAA,CA0GUC,kBAAA;IAzGGC,MAAM,EAAAC,QAAA,CACf,MAoCM,CApCNC,mBAAA,CAoCM,OApCNC,UAoCM,G,4BAnCJD,mBAAA,CAAa,YAAT,MAAI,qBACRA,mBAAA,CAiCM,OAjCNE,UAiCM,GA/BIC,MAAA,CAAAC,SAAS,I,cADjBC,YAAA,CAYYC,oBAAA;;kBAVDH,MAAA,CAAAI,aAAa;iEAAbJ,MAAA,CAAAI,aAAa,GAAAC,MAAA;MACtBC,WAAW,EAAC,MAAM;MACjBC,QAAM,EAAEP,MAAA,CAAAQ;;wBAGP,MAAuB,E,kBADzBjB,mBAAA,CAKEkB,SAAA,QAAAC,WAAA,CAJeV,MAAA,CAAAW,OAAO,EAAfC,IAAI;6BADbV,YAAA,CAKEW,oBAAA;UAHCC,GAAG,EAAEF,IAAI,CAACG,EAAE;UACZC,KAAK,KAAKJ,IAAI,CAACK,IAAI,KAAKL,IAAI,CAACM,WAAW;UACxCC,KAAK,EAAEP,IAAI,CAACG;;;;qDAGDf,MAAA,CAAAoB,WAAW,I,cAA3B7B,mBAAA,CAGM,OAHN8B,UAGM,GAFJxB,mBAAA,CAAqD,QAArDyB,UAAqD,EAAAC,gBAAA,CAA1BvB,MAAA,CAAAoB,WAAW,CAACH,IAAI,kBAC3CxB,YAAA,CAA8C+B,iBAAA;MAAtCC,IAAI,EAAC,OAAO;MAACC,IAAI,EAAC;;wBAAO,MAAIC,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;2BAEvCpC,mBAAA,CAEM,OAFNqC,UAEM,GADJnC,YAAA,CAAkD+B,iBAAA;MAA1CC,IAAI,EAAC,OAAO;MAACC,IAAI,EAAC;;wBAAU,MAAKC,MAAA,SAAAA,MAAA,Q,iBAAL,OAAK,E;;;WAInC3B,MAAA,CAAAI,aAAa,I,cADrBF,YAAA,CAOY2B,oBAAA;;MALVH,IAAI,EAAC,SAAS;MACbI,OAAK,EAAAH,MAAA,QAAAA,MAAA,MAAAtB,MAAA,IAAEL,MAAA,CAAA+B,gBAAgB;MACvBC,IAAI,EAAEC,IAAA,CAAAC;;wBACR,MAEDP,MAAA,SAAAA,MAAA,Q,iBAFC,QAED,E;;;sEACAlC,YAAA,CAEYoC,oBAAA;MAFAC,OAAK,EAAE9B,MAAA,CAAAQ,eAAe;MAAGwB,IAAI,EAAEC,IAAA,CAAAE;;wBAAS,MAEpDR,MAAA,SAAAA,MAAA,Q,iBAFoD,MAEpD,E;;;;sBAMN,MAkDM,C,+BAlDNpC,mBAAA,CAkDM,OAlDN6C,UAkDM,GAjDOpC,MAAA,CAAAqC,WAAW,CAACC,MAAM,WAAWtC,MAAA,CAAAuC,OAAO,I,cAA/ChD,mBAAA,CAMM,OANNiD,UAMM,GALJ/C,YAAA,CAIWgD,mBAAA;MAJDC,WAAW,EAAC;IAAQ;wBAC5B,MAEY,CAFZjD,YAAA,CAEYoC,oBAAA;QAFDH,IAAI,EAAC,SAAS;QAAEI,OAAK,EAAAH,MAAA,QAAAA,MAAA,MAAAtB,MAAA,IAAEL,MAAA,CAAA+B,gBAAgB;;0BAAS,MAE3DJ,MAAA,SAAAA,MAAA,Q,iBAF2D,QAE3D,E;;;;;2BAIJpC,mBAAA,CAwCM,OAxCNoD,UAwCM,I,kBAvCJpD,mBAAA,CAsCMkB,SAAA,QAAAC,WAAA,CAtCoBV,MAAA,CAAAqC,WAAW,EAAzBO,UAAU;2BAAtBrD,mBAAA,CAsCM;QAtCkCuB,GAAG,EAAE8B,UAAU,CAAC7B,EAAE;QAAEzB,KAAK,EAAC;UAChEG,YAAA,CAoCUC,kBAAA;QApCDmD,MAAM,EAAC,OAAO;QAAEf,OAAK,EAAAzB,MAAA,IAAEL,MAAA,CAAA8C,oBAAoB,CAACF,UAAU;;0BAC7D,MAA0C,CAA1C/C,mBAAA,CAA0C,YAAA0B,gBAAA,CAAnCqB,UAAU,CAACG,KAAK,6BACvBlD,mBAAA,CAA8D,KAA9DmD,WAA8D,EAAAzB,gBAAA,CAAzBqB,UAAU,CAACK,OAAO,kBAEvDpD,mBAAA,CAKM,OALNqD,WAKM,GAJUN,UAAU,CAACO,OAAO,I,cAAhCjD,YAAA,CAESsB,iBAAA;;UAF0BE,IAAI,EAAE1B,MAAA,CAAAoD,YAAY,CAACR,UAAU,CAACO,OAAO;UAAG1B,IAAI,EAAC;;4BAC9E,MAAqC,C,kCAAlCzB,MAAA,CAAAqD,WAAW,CAACT,UAAU,CAACO,OAAO,kB;;4FAEnCtD,mBAAA,CAAgE,QAAhEyD,WAAgE,EAAA/B,gBAAA,CAA7BqB,UAAU,CAACW,QAAQ,iB,GAGxD1D,mBAAA,CASM,OATN2D,WASM,GARJ3D,mBAAA,CAGM,OAHN4D,WAGM,GAFJhE,YAAA,CAA4BiE,kBAAA;4BAAnB,MAAS,CAATjE,YAAA,CAASkE,gBAAA,E;;YAClB9D,mBAAA,CAAoD,cAAA0B,gBAAA,CAA3CvB,MAAA,CAAA4D,UAAU,CAAChB,UAAU,CAACiB,UAAU,kB,GAEdjB,UAAU,CAACkB,WAAW,IAAIlB,UAAU,CAACkB,WAAW,CAACxB,MAAM,Q,cAApF/C,mBAAA,CAGM,OAHNwE,WAGM,GAFJtE,YAAA,CAA+BiE,kBAAA;4BAAtB,MAAY,CAAZjE,YAAA,CAAYuE,mBAAA,E;;YACrBnE,mBAAA,CAAmD,cAAA0B,gBAAA,CAA1CqB,UAAU,CAACkB,WAAW,CAACxB,MAAM,IAAG,KAAG,gB,0CAIhDzC,mBAAA,CAaM,OAbNoE,WAaM,GAZJxE,YAAA,CAEYoC,oBAAA;UAFDJ,IAAI,EAAC,OAAO;UAAEK,OAAK,EAAAoC,cAAA,CAAA7D,MAAA,IAAOL,MAAA,CAAA8C,oBAAoB,CAACF,UAAU;;4BAAG,MAEvE,KAAAjB,MAAA,SAAAA,MAAA,Q,iBAFuE,QAEvE,E;;;0DACAlC,YAAA,CAEYoC,oBAAA;UAFDJ,IAAI,EAAC,OAAO;UAAEK,OAAK,EAAAoC,cAAA,CAAA7D,MAAA,IAAOL,MAAA,CAAAmE,iBAAiB,CAACvB,UAAU;UAAIZ,IAAI,EAAEC,IAAA,CAAAmC;;4BAAc,MAEzF,KAAAzC,MAAA,SAAAA,MAAA,Q,iBAFyF,MAEzF,E;;;kEACiB3B,MAAA,CAAAqE,OAAO,CAACzB,UAAU,K,cAAnC1C,YAAA,CAEY2B,oBAAA;;UAF0BJ,IAAI,EAAC,OAAO;UAAEK,OAAK,EAAAoC,cAAA,CAAA7D,MAAA,IAAOL,MAAA,CAAAsE,cAAc,CAAC1B,UAAU;UAAIZ,IAAI,EAAEC,IAAA,CAAAsC;;4BAAM,MAEzG,KAAA5C,MAAA,SAAAA,MAAA,Q,iBAFyG,MAEzG,E;;;uGACiB3B,MAAA,CAAAwE,SAAS,CAAC5B,UAAU,K,cAArC1C,YAAA,CAEY2B,oBAAA;;UAF4BJ,IAAI,EAAC,OAAO;UAACC,IAAI,EAAC,QAAQ;UAAEI,OAAK,EAAAoC,cAAA,CAAA7D,MAAA,IAAOL,MAAA,CAAAyE,gBAAgB,CAAC7B,UAAU,CAAC7B,EAAE;UAAIiB,IAAI,EAAEC,IAAA,CAAAyC;;4BAAQ,MAEhI,KAAA/C,MAAA,SAAAA,MAAA,Q,iBAFgI,MAEhI,E;;;;;;gEA7C8B3B,MAAA,CAAAuC,OAAO,E,GAqDpCvC,MAAA,CAAA2E,KAAK,Q,cAAhBpF,mBAAA,CAUM,OAVNqF,WAUM,GATJnF,YAAA,CAQEoF,wBAAA;MAPQ,cAAY,EAAE7E,MAAA,CAAA8E,WAAW;kEAAX9E,MAAA,CAAA8E,WAAW,GAAAzE,MAAA;MACzB,WAAS,EAAEL,MAAA,CAAA+E,QAAQ;+DAAR/E,MAAA,CAAA+E,QAAQ,GAAA1E,MAAA;MAC1BsE,KAAK,EAAE3E,MAAA,CAAA2E,KAAK;MACZ,YAAU,EAAE,YAAY;MACzBK,MAAM,EAAC,yCAAyC;MAC/CC,YAAW,EAAEjF,MAAA,CAAAQ,eAAe;MAC5B0E,eAAc,EAAElF,MAAA,CAAAQ;;;MAKvB2E,mBAAA,gBAAmB,EACnB1F,YAAA,CA2CY2F,oBAAA;gBA1CDpF,MAAA,CAAA+B,gBAAgB;+DAAhB/B,MAAA,CAAA+B,gBAAgB,GAAA1B,MAAA;IACxB0C,KAAK,EAAE/C,MAAA,CAAAqF,iBAAiB;IACzBC,KAAK,EAAC;;IAkCKC,MAAM,EAAA3F,QAAA,CACf,MAA2D,CAA3DH,YAAA,CAA2DoC,oBAAA;MAA/CC,OAAK,EAAAH,MAAA,QAAAA,MAAA,MAAAtB,MAAA,IAAEL,MAAA,CAAA+B,gBAAgB;;wBAAU,MAAEJ,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;QAC/ClC,YAAA,CAEYoC,oBAAA;MAFDH,IAAI,EAAC,SAAS;MAAEI,OAAK,EAAE9B,MAAA,CAAAwF,gBAAgB;MAAGjD,OAAO,EAAEvC,MAAA,CAAAyF;;wBAC5D,MAAqC,C,kCAAlCzF,MAAA,CAAAqF,iBAAiB,+B;;;sBAnCxB,MA8BU,CA9BV5F,YAAA,CA8BUiG,kBAAA;MA7BRC,GAAG,EAAC,SAAS;MACZC,KAAK,EAAE5F,MAAA,CAAA6F,cAAc;MACrBC,KAAK,EAAE9F,MAAA,CAAA+F,SAAS;MACjB,aAAW,EAAC;;wBAEZ,MAUe,CAVftG,YAAA,CAUeuG,uBAAA;QAVDhF,KAAK,EAAC,MAAM;QAACiF,IAAI,EAAC;;0BAC9B,MAQY,CARZxG,YAAA,CAQYU,oBAAA;sBARQH,MAAA,CAAA6F,cAAc,CAACnE,IAAI;qEAAnB1B,MAAA,CAAA6F,cAAc,CAACnE,IAAI,GAAArB,MAAA;UAAEC,WAAW,EAAC;;4BACnD,MAA6C,CAA7Cb,YAAA,CAA6CoB,oBAAA;YAAlCG,KAAK,EAAC,MAAM;YAACG,KAAK,EAAC;cAC9B1B,YAAA,CAA2CoB,oBAAA;YAAhCG,KAAK,EAAC,MAAM;YAACG,KAAK,EAAC;cAC9B1B,YAAA,CAAwCoB,oBAAA;YAA7BG,KAAK,EAAC,MAAM;YAACG,KAAK,EAAC;cAC9B1B,YAAA,CAA2CoB,oBAAA;YAAhCG,KAAK,EAAC,MAAM;YAACG,KAAK,EAAC;cAC9B1B,YAAA,CAA+CoB,oBAAA;YAApCG,KAAK,EAAC,MAAM;YAACG,KAAK,EAAC;cAC9B1B,YAAA,CAA2CoB,oBAAA;YAAhCG,KAAK,EAAC,MAAM;YAACG,KAAK,EAAC;cAC9B1B,YAAA,CAAsCoB,oBAAA;YAA3BG,KAAK,EAAC,IAAI;YAACG,KAAK,EAAC;;;;;UAIhC1B,YAAA,CAEeuG,uBAAA;QAFDhF,KAAK,EAAC,MAAM;QAACiF,IAAI,EAAC;;0BAC9B,MAAiE,CAAjExG,YAAA,CAAiEyG,mBAAA;sBAA9ClG,MAAA,CAAA6F,cAAc,CAAC9C,KAAK;qEAApB/C,MAAA,CAAA6F,cAAc,CAAC9C,KAAK,GAAA1C,MAAA;UAAEC,WAAW,EAAC;;;UAGvDb,YAAA,CAOeuG,uBAAA;QAPDhF,KAAK,EAAC,MAAM;QAACiF,IAAI,EAAC;;0BAC9B,MAKE,CALFxG,YAAA,CAKEyG,mBAAA;sBAJSlG,MAAA,CAAA6F,cAAc,CAAC5C,OAAO;qEAAtBjD,MAAA,CAAA6F,cAAc,CAAC5C,OAAO,GAAA5C,MAAA;UAC/BqB,IAAI,EAAC,UAAU;UACdyE,IAAI,EAAE,CAAC;UACR7F,WAAW,EAAC;;;;;;;8CAapB6E,mBAAA,WAAc,EACd1F,YAAA,CAuCY2F,oBAAA;gBAtCDpF,MAAA,CAAAoG,eAAe;iEAAfpG,MAAA,CAAAoG,eAAe,GAAA/F,MAAA;IACxB0C,KAAK,EAAC,MAAM;IACZuC,KAAK,EAAC,OAAO;IACZ,sBAAoB,EAAE;;IA6BZC,MAAM,EAAA3F,QAAA,CACf,MAA0D,CAA1DH,YAAA,CAA0DoC,oBAAA;MAA9CC,OAAK,EAAAH,MAAA,SAAAA,MAAA,OAAAtB,MAAA,IAAEL,MAAA,CAAAoG,eAAe;;wBAAU,MAAEzE,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;QAC9ClC,YAAA,CAEYoC,oBAAA;MAFDH,IAAI,EAAC,SAAS;MAAEI,OAAK,EAAE9B,MAAA,CAAAqG,WAAW;MAAG9D,OAAO,EAAEvC,MAAA,CAAAyF;;wBAAY,MAErE9D,MAAA,SAAAA,MAAA,Q,iBAFqE,QAErE,E;;;;sBA/BF,MAMM,CANK3B,MAAA,CAAAsG,oBAAoB,I,cAA/B/G,mBAAA,CAMM,OANNgH,WAMM,GALJ1G,mBAAA,CAA4C,YAAxC,KAAG,GAAA0B,gBAAA,CAAGvB,MAAA,CAAAsG,oBAAoB,CAACvD,KAAK,kBACpClD,mBAAA,CAGM,OAHN2G,WAGM,GAFJ3G,mBAAA,CAA8D,YAA3DA,mBAAA,CAAoD,gBAAA0B,gBAAA,CAAzCvB,MAAA,CAAAsG,oBAAoB,CAAC/C,QAAQ,kB,6CAAY,KAAG,G,GAC1D1D,mBAAA,CAAyC,WAAA0B,gBAAA,CAAnCvB,MAAA,CAAAsG,oBAAoB,CAACrD,OAAO,iB,0CAItCxD,YAAA,CAiBUiG,kBAAA;MAhBRC,GAAG,EAAC,cAAc;MACjBC,KAAK,EAAE5F,MAAA,CAAAyG,SAAS;MAChBX,KAAK,EAAE9F,MAAA,CAAA0G,UAAU;MAClB,aAAW,EAAC,MAAM;MAClBC,KAAyB,EAAzB;QAAA;MAAA;;wBAEA,MASe,CATflH,YAAA,CASeuG,uBAAA;QATDhF,KAAK,EAAC,MAAM;QAACiF,IAAI,EAAC;;0BAC9B,MAOE,CAPFxG,YAAA,CAOEyG,mBAAA;sBANSlG,MAAA,CAAAyG,SAAS,CAACxD,OAAO;uEAAjBjD,MAAA,CAAAyG,SAAS,CAACxD,OAAO,GAAA5C,MAAA;UAC1BqB,IAAI,EAAC,UAAU;UACdyE,IAAI,EAAE,CAAC;UACR7F,WAAW,EAAC,YAAY;UACxBsG,SAAS,EAAC,MAAM;UAChB,iBAAe,EAAf;;;;;;;qCAaRzB,mBAAA,aAAgB,EAChB1F,YAAA,CA2GY2F,oBAAA;gBA1GDpF,MAAA,CAAA6G,gBAAgB;iEAAhB7G,MAAA,CAAA6G,gBAAgB,GAAAxG,MAAA;IACzB0C,KAAK,EAAC,MAAM;IACZuC,KAAK,EAAC,OAAO;IACZ,sBAAoB,EAAE;;IAiGZC,MAAM,EAAA3F,QAAA,CACf,MAA2D,CAA3DH,YAAA,CAA2DoC,oBAAA;MAA/CC,OAAK,EAAAH,MAAA,SAAAA,MAAA,OAAAtB,MAAA,IAAEL,MAAA,CAAA6G,gBAAgB;;wBAAU,MAAElF,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;QAC/ClC,YAAA,CAEYoC,oBAAA;MAFDH,IAAI,EAAC,SAAS;MAAEI,OAAK,EAAAH,MAAA,SAAAA,MAAA,OAAAtB,MAAA,IAAEL,MAAA,CAAAmE,iBAAiB,CAACnE,MAAA,CAAA8G,iBAAiB;;wBAAG,MAExEnF,MAAA,SAAAA,MAAA,Q,iBAFwE,MAExE,E;;;;sBAnGF,MA6FM,CA7FK3B,MAAA,CAAA8G,iBAAiB,I,cAA5BvH,mBAAA,CA6FM,OA7FNwH,WA6FM,GA5FJ5B,mBAAA,WAAc,EACdtF,mBAAA,CAuCM,OAvCNmH,WAuCM,GAtCJnH,mBAAA,CAoBM,OApBNoH,WAoBM,GAnBJpH,mBAAA,CAOM,OAPNqH,WAOM,GANJrH,mBAAA,CAA+D,MAA/DsH,WAA+D,EAAA5F,gBAAA,CAA/BvB,MAAA,CAAA8G,iBAAiB,CAAC/D,KAAK,kBACvDlD,mBAAA,CAIM,OAJNuH,WAIM,GAHJ3H,YAAA,CAES+B,iBAAA;MAFAE,IAAI,EAAE1B,MAAA,CAAAoD,YAAY,CAACpD,MAAA,CAAA8G,iBAAiB,CAACpF,IAAI;MAAGD,IAAI,EAAC;;wBACxD,MAAyC,C,kCAAtCzB,MAAA,CAAAqD,WAAW,CAACrD,MAAA,CAAA8G,iBAAiB,CAACpF,IAAI,kB;;qCAI3C7B,mBAAA,CAUM,OAVNwH,WAUM,GATJxH,mBAAA,CAQM,OARNyH,WAQM,GAPJ7H,YAAA,CAEY8H,oBAAA;MAFA9F,IAAI,EAAE,EAAE;MAAEnC,KAAK,EAAC,eAAe;MAAEkI,GAAG,EAAExH,MAAA,CAAAyH,YAAY,CAACzH,MAAA,CAAA8G,iBAAiB,CAACY,UAAU;;wBACzF,MAA4C,C,kCAAzC1H,MAAA,CAAA2H,UAAU,CAAC3H,MAAA,CAAA8G,iBAAiB,CAACvD,QAAQ,kB;;gCAE1C1D,mBAAA,CAGM,OAHN+H,WAGM,GAFJ/H,mBAAA,CAA+D,OAA/DgI,WAA+D,EAAAtG,gBAAA,CAAnCvB,MAAA,CAAA8G,iBAAiB,CAACvD,QAAQ,kBACtD1D,mBAAA,CAA8E,OAA9EiI,WAA8E,EAAAvG,gBAAA,CAAjDvB,MAAA,CAAA4D,UAAU,CAAC5D,MAAA,CAAA8G,iBAAiB,CAACjD,UAAU,kB,SAM5EhE,mBAAA,CAeM,OAfNkI,WAeM,GAdJlI,mBAAA,CAA+D,OAA/DmI,WAA+D,EAAAzG,gBAAA,CAAlCvB,MAAA,CAAA8G,iBAAiB,CAAC7D,OAAO,kBAE3CjD,MAAA,CAAA8G,iBAAiB,CAAChD,WAAW,IAAI9D,MAAA,CAAA8G,iBAAiB,CAAChD,WAAW,CAACxB,MAAM,Q,cAAhF/C,mBAAA,CAWM,OAXN0I,WAWM,GAVJpI,mBAAA,CAGM,OAHNqI,WAGM,GAFJzI,YAAA,CAA+BiE,kBAAA;wBAAtB,MAAY,CAAZjE,YAAA,CAAYuE,mBAAA,E;;QACrBnE,mBAAA,CAA4D,cAAtD,MAAI,GAAA0B,gBAAA,CAAGvB,MAAA,CAAA8G,iBAAiB,CAAChD,WAAW,CAACxB,MAAM,IAAG,GAAC,gB,GAEvDzC,mBAAA,CAKM,OALNsI,WAKM,I,kBAJJ5I,mBAAA,CAGMkB,SAAA,QAAAC,WAAA,CAHcV,MAAA,CAAA8G,iBAAiB,CAAChD,WAAW,EAArCsE,IAAI;2BAAhB7I,mBAAA,CAGM;QAH8CuB,GAAG,EAAEsH,IAAI,CAACrH,EAAE;QAAEzB,KAAK,EAAC;UACtEG,YAAA,CAA+BiE,kBAAA;0BAAtB,MAAY,CAAZjE,YAAA,CAAYuE,mBAAA,E;;UACrBnE,mBAAA,CAA8C,QAA9CwI,WAA8C,EAAA9G,gBAAA,CAAnB6G,IAAI,CAACnH,IAAI,iB;iFAO9CkE,mBAAA,UAAa,EACbtF,mBAAA,CAgDM,OAhDNyI,WAgDM,GA/CJzI,mBAAA,CAMM,OANN0I,WAMM,GALJ1I,mBAAA,CAGM,OAHN2I,WAGM,GAFJ/I,YAAA,CAAmCiE,kBAAA;wBAA1B,MAAgB,CAAhBjE,YAAA,CAAgBgJ,uBAAA,E;;QACzB5I,mBAAA,CAAkD,cAA5C,QAAM,GAAA0B,gBAAA,CAAGvB,MAAA,CAAA0I,iBAAiB,CAACpG,MAAM,IAAG,GAAC,gB,GAE7C7C,YAAA,CAAckJ,qBAAA,E,GAGL3I,MAAA,CAAA4I,cAAc,I,cAAzBrJ,mBAAA,CAGM,OAHNsJ,WAGM,GAFJpJ,YAAA,CAAiDiE,kBAAA;MAAxCpE,KAAK,EAAC;IAAY;wBAAC,MAAW,CAAXG,YAAA,CAAWqJ,kBAAA,E;;oCACvCjJ,mBAAA,CAAqB,cAAf,UAAQ,oB,KAGAG,MAAA,CAAA0I,iBAAiB,CAACpG,MAAM,U,cAAxC/C,mBAAA,CAMM,OANNwJ,WAMM,GALJtJ,YAAA,CAIWgD,mBAAA;MAJDC,WAAW,EAAC,MAAM;MAAE,YAAU,EAAE;;wBACxC,MAEY,CAFZjD,YAAA,CAEYoC,oBAAA;QAFDH,IAAI,EAAC,SAAS;QAAEI,OAAK,EAAAH,MAAA,SAAAA,MAAA,OAAAtB,MAAA,IAAEL,MAAA,CAAAmE,iBAAiB,CAACnE,MAAA,CAAA8G,iBAAiB;;0BAAG,MAExEnF,MAAA,SAAAA,MAAA,Q,iBAFwE,QAExE,E;;;;;2BAIJpC,mBAAA,CAyBM,OAzBNyJ,WAyBM,I,kBAxBJzJ,mBAAA,CAuBMkB,SAAA,QAAAC,WAAA,CAvBwBV,MAAA,CAAA0I,iBAAiB,GAAlCO,KAAK,EAAEC,KAAK;2BAAzB3J,mBAAA,CAuBM;QAvB4CuB,GAAG,EAAEmI,KAAK,CAAClI,EAAE;QAAEzB,KAAK,EAAC;UACrEO,mBAAA,CAAgD,OAAhDsJ,WAAgD,EAAtB,GAAC,GAAA5H,gBAAA,CAAG2H,KAAK,sBACnCrJ,mBAAA,CAoBM,OApBNuJ,WAoBM,GAnBJvJ,mBAAA,CAeM,OAfNwJ,WAeM,GAdJxJ,mBAAA,CAUM,OAVNyJ,WAUM,GATJ7J,YAAA,CAEY8H,oBAAA;QAFA9F,IAAI,EAAE,EAAE;QAAEnC,KAAK,EAAC,cAAc;QAAEkI,GAAG,EAAExH,MAAA,CAAAyH,YAAY,CAACwB,KAAK,CAACvB,UAAU;;0BAC5E,MAAgC,C,kCAA7B1H,MAAA,CAAA2H,UAAU,CAACsB,KAAK,CAAC1F,QAAQ,kB;;oDAE9B1D,mBAAA,CAKM,OALN0J,WAKM,GAJJ1J,mBAAA,CAAqD,QAArD2J,WAAqD,EAAAjI,gBAAA,CAAxB0H,KAAK,CAAC1F,QAAQ,kBAC3C9D,YAAA,CAES+B,iBAAA;QAFAE,IAAI,EAAEuH,KAAK,CAACQ,QAAQ;QAAqChI,IAAI,EAAC,OAAO;QAACnC,KAAK,EAAC;;0BACnF,MAAgD,C,kCAA7C2J,KAAK,CAACQ,QAAQ,6C;;yDAIvB5J,mBAAA,CAEM,OAFN6J,WAEM,EAAAnI,gBAAA,CADDvB,MAAA,CAAA4D,UAAU,CAACqF,KAAK,CAACpF,UAAU,kB,GAGlChE,mBAAA,CAEM,OAFN8J,WAEM,EAAApI,gBAAA,CADD0H,KAAK,CAAChG,OAAO,iB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}