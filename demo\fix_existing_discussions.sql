-- 手动修复现有讨论记录的子类型
-- 这个脚本可以直接在数据库中执行来立即修复显示问题

-- 首先检查records表是否已经有sub_type字段
-- 如果没有，先添加字段
ALTER TABLE records ADD COLUMN IF NOT EXISTS sub_type VARCHAR(50) NULL COMMENT '记录子类型（用于讨论分类）';

-- 为所有现有的讨论记录设置默认子类型
UPDATE records 
SET sub_type = 'DISCUSSION' 
WHERE type = 'DISCUSSION' 
AND (sub_type IS NULL OR sub_type = '' OR sub_type = 'null');

-- 为公告类型记录设置子类型
UPDATE records 
SET sub_type = 'ANNOUNCEMENT' 
WHERE type = 'ANNOUNCEMENT' 
AND (sub_type IS NULL OR sub_type = '' OR sub_type = 'null');

-- 检查更新结果
SELECT 
    id, 
    type, 
    sub_type, 
    title, 
    create_time 
FROM records 
WHERE type = 'DISCUSSION' 
ORDER BY create_time DESC 
LIMIT 10;

-- 添加索引（如果不存在）
CREATE INDEX IF NOT EXISTS idx_records_type_sub_type ON records(type, sub_type);
