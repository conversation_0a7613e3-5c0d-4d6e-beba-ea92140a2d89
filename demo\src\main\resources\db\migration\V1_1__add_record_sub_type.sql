-- 添加记录子类型字段
-- 用于支持讨论的详细分类

-- 添加sub_type字段到records表
ALTER TABLE records ADD COLUMN sub_type VARCHAR(50) NULL COMMENT '记录子类型（用于讨论分类）';

-- 为现有的讨论记录设置默认子类型
UPDATE records SET sub_type = 'DISCUSSION' WHERE type = 'DISCUSSION' AND (sub_type IS NULL OR sub_type = '');

-- 为其他类型的记录也设置合适的子类型（如果需要的话）
UPDATE records SET sub_type = 'ANNOUNCEMENT' WHERE type = 'ANNOUNCEMENT' AND (sub_type IS NULL OR sub_type = '');

-- 添加索引以提高查询性能
CREATE INDEX idx_records_type_sub_type ON records(type, sub_type);
