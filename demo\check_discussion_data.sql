-- 检查当前讨论数据的状态
-- 用于诊断类型标签不显示的问题

-- 1. 检查records表结构
DESCRIBE records;

-- 2. 检查是否有sub_type字段
SHOW COLUMNS FROM records LIKE 'sub_type';

-- 3. 查看所有讨论记录的类型信息
SELECT 
    id,
    type,
    sub_type,
    title,
    create_time,
    CASE 
        WHEN sub_type IS NULL THEN 'NULL'
        WHEN sub_type = '' THEN 'EMPTY'
        ELSE sub_type
    END as sub_type_status
FROM records 
WHERE type = 'DISCUSSION' 
ORDER BY create_time DESC 
LIMIT 10;

-- 4. 统计各种类型的记录数量
SELECT 
    type,
    sub_type,
    COUNT(*) as count
FROM records 
GROUP BY type, sub_type
ORDER BY type, sub_type;

-- 5. 检查是否有空的sub_type值
SELECT COUNT(*) as null_sub_type_count
FROM records 
WHERE type = 'DISCUSSION' 
AND (sub_type IS NULL OR sub_type = '');

-- 6. 如果需要，可以执行以下语句来修复数据
-- UPDATE records SET sub_type = 'DISCUSSION' WHERE type = 'DISCUSSION' AND (sub_type IS NULL OR sub_type = '');
