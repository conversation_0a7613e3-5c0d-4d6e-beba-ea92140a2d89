{"ast": null, "code": "import { createElementVNode as _createElementVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, resolveComponent as _resolveComponent, createBlock as _createBlock, withCtx as _withCtx, createCommentVNode as _createCommentVNode, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, createVNode as _createVNode, withModifiers as _withModifiers, resolveDirective as _resolveDirective, withDirectives as _withDirectives } from \"vue\";\nconst _hoisted_1 = {\n  class: \"submission-feedback\"\n};\nconst _hoisted_2 = {\n  class: \"card-header\"\n};\nconst _hoisted_3 = {\n  class: \"header-actions\"\n};\nconst _hoisted_4 = {\n  key: 1,\n  class: \"current-team-info\"\n};\nconst _hoisted_5 = {\n  class: \"team-name\"\n};\nconst _hoisted_6 = {\n  key: 2,\n  class: \"no-team-info\"\n};\nconst _hoisted_7 = {\n  class: \"record-list\"\n};\nconst _hoisted_8 = {\n  key: 0,\n  class: \"empty-state\"\n};\nconst _hoisted_9 = {\n  key: 1,\n  class: \"record-grid\"\n};\nconst _hoisted_10 = {\n  class: \"record-description\"\n};\nconst _hoisted_11 = {\n  class: \"record-meta\"\n};\nconst _hoisted_12 = {\n  class: \"record-author\"\n};\nconst _hoisted_13 = {\n  class: \"record-time\"\n};\nconst _hoisted_14 = {\n  class: \"record-footer\"\n};\nconst _hoisted_15 = {\n  class: \"record-actions\"\n};\nconst _hoisted_16 = {\n  key: 0,\n  class: \"record-detail\"\n};\nconst _hoisted_17 = {\n  class: \"main-record\"\n};\nconst _hoisted_18 = {\n  class: \"record-header\"\n};\nconst _hoisted_19 = {\n  class: \"header-left\"\n};\nconst _hoisted_20 = {\n  class: \"record-title\"\n};\nconst _hoisted_21 = {\n  class: \"record-tags\"\n};\nconst _hoisted_22 = {\n  class: \"header-right\"\n};\nconst _hoisted_23 = {\n  class: \"author-info\"\n};\nconst _hoisted_24 = {\n  class: \"author-details\"\n};\nconst _hoisted_25 = {\n  class: \"author-name\"\n};\nconst _hoisted_26 = {\n  class: \"author-time\"\n};\nconst _hoisted_27 = {\n  class: \"record-content\"\n};\nconst _hoisted_28 = {\n  key: 0,\n  class: \"score-info\"\n};\nconst _hoisted_29 = {\n  class: \"score-details\"\n};\nconst _hoisted_30 = {\n  key: 1,\n  class: \"attachments-info\"\n};\nconst _hoisted_31 = {\n  class: \"attachments-list\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_option = _resolveComponent(\"el-option\");\n  const _component_el_select = _resolveComponent(\"el-select\");\n  const _component_el_tag = _resolveComponent(\"el-tag\");\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_el_empty = _resolveComponent(\"el-empty\");\n  const _component_el_card = _resolveComponent(\"el-card\");\n  const _component_el_avatar = _resolveComponent(\"el-avatar\");\n  const _component_el_divider = _resolveComponent(\"el-divider\");\n  const _component_el_dialog = _resolveComponent(\"el-dialog\");\n  const _directive_loading = _resolveDirective(\"loading\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_el_card, null, {\n    header: _withCtx(() => [_createElementVNode(\"div\", _hoisted_2, [_cache[6] || (_cache[6] = _createElementVNode(\"h3\", null, \"提交与反馈\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_3, [$setup.isTeacher ? (_openBlock(), _createBlock(_component_el_select, {\n      key: 0,\n      modelValue: $setup.currentTeamId,\n      \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $setup.currentTeamId = $event),\n      placeholder: \"选择团队\",\n      onChange: $setup.loadRecords\n    }, {\n      default: _withCtx(() => [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.myTeams, team => {\n        return _openBlock(), _createBlock(_component_el_option, {\n          key: team.id,\n          label: `${team.name} (${team.projectName || '未知项目'})`,\n          value: team.id\n        }, null, 8 /* PROPS */, [\"label\", \"value\"]);\n      }), 128 /* KEYED_FRAGMENT */))]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\", \"onChange\"])) : $setup.currentTeam ? (_openBlock(), _createElementBlock(\"div\", _hoisted_4, [_createElementVNode(\"span\", _hoisted_5, _toDisplayString($setup.currentTeam.name), 1 /* TEXT */), _createVNode(_component_el_tag, {\n      size: \"small\",\n      type: \"info\"\n    }, {\n      default: _withCtx(() => _cache[3] || (_cache[3] = [_createTextVNode(\"我的团队\")])),\n      _: 1 /* STABLE */,\n      __: [3]\n    })])) : (_openBlock(), _createElementBlock(\"div\", _hoisted_6, [_createVNode(_component_el_tag, {\n      size: \"small\",\n      type: \"warning\"\n    }, {\n      default: _withCtx(() => _cache[4] || (_cache[4] = [_createTextVNode(\"未加入团队\")])),\n      _: 1 /* STABLE */,\n      __: [4]\n    })])), _createVNode(_component_el_button, {\n      onClick: $setup.loadRecords,\n      icon: _ctx.Refresh\n    }, {\n      default: _withCtx(() => _cache[5] || (_cache[5] = [_createTextVNode(\" 刷新 \")])),\n      _: 1 /* STABLE */,\n      __: [5]\n    }, 8 /* PROPS */, [\"onClick\", \"icon\"])])])]),\n    default: _withCtx(() => [_withDirectives((_openBlock(), _createElementBlock(\"div\", _hoisted_7, [$setup.records.length === 0 && !$setup.loading ? (_openBlock(), _createElementBlock(\"div\", _hoisted_8, [_createVNode(_component_el_empty, {\n      description: \"暂无提交或反馈记录\"\n    })])) : (_openBlock(), _createElementBlock(\"div\", _hoisted_9, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.records, record => {\n      return _openBlock(), _createElementBlock(\"div\", {\n        key: record.id,\n        class: \"record-card\"\n      }, [_createVNode(_component_el_card, {\n        shadow: \"hover\",\n        onClick: $event => $setup.viewRecordDetail(record)\n      }, {\n        default: _withCtx(() => [_createElementVNode(\"h4\", null, _toDisplayString(record.title || '无标题'), 1 /* TEXT */), _createElementVNode(\"p\", _hoisted_10, _toDisplayString(record.content), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_11, [record.type ? (_openBlock(), _createBlock(_component_el_tag, {\n          key: 0,\n          type: $setup.getTypeColor(record.type),\n          size: \"small\"\n        }, {\n          default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.getTypeText(record.type)), 1 /* TEXT */)]),\n          _: 2 /* DYNAMIC */\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"type\"])) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"span\", _hoisted_12, _toDisplayString(record.userName), 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_13, _toDisplayString($setup.formatDate(record.createTime)), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_14, [_createElementVNode(\"div\", _hoisted_15, [$setup.canEdit(record) ? (_openBlock(), _createBlock(_component_el_button, {\n          key: 0,\n          type: \"primary\",\n          size: \"small\",\n          onClick: _withModifiers($event => $setup.editRecord(record), [\"stop\"]),\n          icon: _ctx.Edit\n        }, {\n          default: _withCtx(() => [...(_cache[7] || (_cache[7] = [_createTextVNode(\" 编辑 \")]))]),\n          _: 2 /* DYNAMIC */,\n          __: [7]\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\", \"icon\"])) : _createCommentVNode(\"v-if\", true), $setup.canDelete(record) ? (_openBlock(), _createBlock(_component_el_button, {\n          key: 1,\n          type: \"danger\",\n          size: \"small\",\n          onClick: _withModifiers($event => $setup.deleteRecord(record.id), [\"stop\"]),\n          icon: _ctx.Delete\n        }, {\n          default: _withCtx(() => [...(_cache[8] || (_cache[8] = [_createTextVNode(\" 删除 \")]))]),\n          _: 2 /* DYNAMIC */,\n          __: [8]\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\", \"icon\"])) : _createCommentVNode(\"v-if\", true)])])]),\n        _: 2 /* DYNAMIC */\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"])]);\n    }), 128 /* KEYED_FRAGMENT */))]))])), [[_directive_loading, $setup.loading]])]),\n    _: 1 /* STABLE */\n  }), _createCommentVNode(\" 记录详情对话框 \"), _createVNode(_component_el_dialog, {\n    modelValue: $setup.showDetailDialog,\n    \"onUpdate:modelValue\": _cache[2] || (_cache[2] = $event => $setup.showDetailDialog = $event),\n    title: \"记录详情\",\n    width: \"800px\",\n    \"close-on-click-modal\": false\n  }, {\n    footer: _withCtx(() => [_createVNode(_component_el_button, {\n      onClick: _cache[1] || (_cache[1] = $event => $setup.showDetailDialog = false)\n    }, {\n      default: _withCtx(() => _cache[11] || (_cache[11] = [_createTextVNode(\"关闭\")])),\n      _: 1 /* STABLE */,\n      __: [11]\n    })]),\n    default: _withCtx(() => [$setup.currentRecord ? (_openBlock(), _createElementBlock(\"div\", _hoisted_16, [_createElementVNode(\"div\", _hoisted_17, [_createElementVNode(\"div\", _hoisted_18, [_createElementVNode(\"div\", _hoisted_19, [_createElementVNode(\"h2\", _hoisted_20, _toDisplayString($setup.currentRecord.title), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_21, [_createVNode(_component_el_tag, {\n      type: $setup.getTypeColor($setup.currentRecord.type),\n      size: \"small\"\n    }, {\n      default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.getTypeText($setup.currentRecord.type)), 1 /* TEXT */)]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"type\"])])]), _createElementVNode(\"div\", _hoisted_22, [_createElementVNode(\"div\", _hoisted_23, [_createVNode(_component_el_avatar, {\n      size: 40,\n      class: \"author-avatar\",\n      src: $setup.getAvatarUrl($setup.currentRecord.userAvatar)\n    }, {\n      default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.getInitial($setup.currentRecord.userName)), 1 /* TEXT */)]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"src\"]), _createElementVNode(\"div\", _hoisted_24, [_createElementVNode(\"span\", _hoisted_25, _toDisplayString($setup.currentRecord.userName), 1 /* TEXT */), _createVNode(_component_el_tag, {\n      type: $setup.currentRecord.userRole === 'TEACHER' ? 'warning' : 'info',\n      size: \"small\"\n    }, {\n      default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.currentRecord.userRole === 'TEACHER' ? '教师' : '学生'), 1 /* TEXT */)]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"type\"]), _createElementVNode(\"div\", _hoisted_26, _toDisplayString($setup.formatDate($setup.currentRecord.createTime)), 1 /* TEXT */)])])])]), _createElementVNode(\"div\", _hoisted_27, [_createElementVNode(\"p\", null, _toDisplayString($setup.currentRecord.content), 1 /* TEXT */)]), _createCommentVNode(\" 显示评分信息（如果有） \"), $setup.currentRecord.score ? (_openBlock(), _createElementBlock(\"div\", _hoisted_28, [_createVNode(_component_el_divider, {\n      \"content-position\": \"left\"\n    }, {\n      default: _withCtx(() => _cache[9] || (_cache[9] = [_createTextVNode(\"评分信息\")])),\n      _: 1 /* STABLE */,\n      __: [9]\n    }), _createElementVNode(\"div\", _hoisted_29, [_createVNode(_component_el_tag, {\n      type: \"success\",\n      size: \"large\"\n    }, {\n      default: _withCtx(() => [_createTextVNode(\" 评分: \" + _toDisplayString($setup.currentRecord.score), 1 /* TEXT */)]),\n      _: 1 /* STABLE */\n    }), $setup.currentRecord.weight ? (_openBlock(), _createBlock(_component_el_tag, {\n      key: 0,\n      type: \"info\",\n      size: \"small\",\n      style: {\n        \"margin-left\": \"10px\"\n      }\n    }, {\n      default: _withCtx(() => [_createTextVNode(\" 权重: \" + _toDisplayString($setup.currentRecord.weight), 1 /* TEXT */)]),\n      _: 1 /* STABLE */\n    })) : _createCommentVNode(\"v-if\", true)])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 显示附件信息（如果有） \"), $setup.currentRecord.attachments ? (_openBlock(), _createElementBlock(\"div\", _hoisted_30, [_createVNode(_component_el_divider, {\n      \"content-position\": \"left\"\n    }, {\n      default: _withCtx(() => _cache[10] || (_cache[10] = [_createTextVNode(\"附件\")])),\n      _: 1 /* STABLE */,\n      __: [10]\n    }), _createElementVNode(\"div\", _hoisted_31, [_createVNode(_component_el_tag, {\n      type: \"info\"\n    }, {\n      default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.currentRecord.attachments), 1 /* TEXT */)]),\n      _: 1 /* STABLE */\n    })])])) : _createCommentVNode(\"v-if\", true)])])) : _createCommentVNode(\"v-if\", true)]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_card", "header", "_withCtx", "_createElementVNode", "_hoisted_2", "_hoisted_3", "$setup", "<PERSON><PERSON><PERSON>er", "_createBlock", "_component_el_select", "currentTeamId", "$event", "placeholder", "onChange", "loadRecords", "_Fragment", "_renderList", "myTeams", "team", "_component_el_option", "key", "id", "label", "name", "projectName", "value", "currentTeam", "_hoisted_4", "_hoisted_5", "_toDisplayString", "_component_el_tag", "size", "type", "_cache", "_hoisted_6", "_component_el_button", "onClick", "icon", "_ctx", "Refresh", "_hoisted_7", "records", "length", "loading", "_hoisted_8", "_component_el_empty", "description", "_hoisted_9", "record", "shadow", "viewRecordDetail", "title", "_hoisted_10", "content", "_hoisted_11", "getTypeColor", "getTypeText", "_hoisted_12", "userName", "_hoisted_13", "formatDate", "createTime", "_hoisted_14", "_hoisted_15", "canEdit", "_withModifiers", "editRecord", "Edit", "canDelete", "deleteRecord", "Delete", "_createCommentVNode", "_component_el_dialog", "showDetailDialog", "width", "footer", "currentRecord", "_hoisted_16", "_hoisted_17", "_hoisted_18", "_hoisted_19", "_hoisted_20", "_hoisted_21", "_hoisted_22", "_hoisted_23", "_component_el_avatar", "src", "getAvatarUrl", "userAvatar", "getInitial", "_hoisted_24", "_hoisted_25", "userRole", "_hoisted_26", "_hoisted_27", "score", "_hoisted_28", "_component_el_divider", "_hoisted_29", "weight", "style", "attachments", "_hoisted_30", "_hoisted_31"], "sources": ["D:\\workspace\\idea\\worker\\work_cli\\src\\views\\collaboration\\SubmissionFeedbackView.vue"], "sourcesContent": ["<template>\n  <div class=\"submission-feedback\">\n    <el-card>\n      <template #header>\n        <div class=\"card-header\">\n          <h3>提交与反馈</h3>\n          <div class=\"header-actions\">\n            <el-select\n              v-if=\"isTeacher\"\n              v-model=\"currentTeamId\"\n              placeholder=\"选择团队\"\n              @change=\"loadRecords\"\n            >\n              <el-option\n                v-for=\"team in myTeams\"\n                :key=\"team.id\"\n                :label=\"`${team.name} (${team.projectName || '未知项目'})`\"\n                :value=\"team.id\"\n              />\n            </el-select>\n            <div v-else-if=\"currentTeam\" class=\"current-team-info\">\n              <span class=\"team-name\">{{ currentTeam.name }}</span>\n              <el-tag size=\"small\" type=\"info\">我的团队</el-tag>\n            </div>\n            <div v-else class=\"no-team-info\">\n              <el-tag size=\"small\" type=\"warning\">未加入团队</el-tag>\n            </div>\n\n            <el-button @click=\"loadRecords\" :icon=\"Refresh\">\n              刷新\n            </el-button>\n          </div>\n        </div>\n      </template>\n      \n      <!-- 记录列表 -->\n      <div class=\"record-list\" v-loading=\"loading\">\n        <div v-if=\"records.length === 0 && !loading\" class=\"empty-state\">\n          <el-empty description=\"暂无提交或反馈记录\" />\n        </div>\n        \n        <div v-else class=\"record-grid\">\n          <div v-for=\"record in records\" :key=\"record.id\" class=\"record-card\">\n            <el-card shadow=\"hover\" @click=\"viewRecordDetail(record)\">\n              <h4>{{ record.title || '无标题' }}</h4>\n              <p class=\"record-description\">{{ record.content }}</p>\n\n              <div class=\"record-meta\">\n                <el-tag v-if=\"record.type\" :type=\"getTypeColor(record.type)\" size=\"small\">\n                  {{ getTypeText(record.type) }}\n                </el-tag>\n                <span class=\"record-author\">{{ record.userName }}</span>\n                <span class=\"record-time\">{{ formatDate(record.createTime) }}</span>\n              </div>\n\n              <div class=\"record-footer\">\n                <div class=\"record-actions\">\n                  <el-button\n                    v-if=\"canEdit(record)\"\n                    type=\"primary\"\n                    size=\"small\"\n                    @click.stop=\"editRecord(record)\"\n                    :icon=\"Edit\"\n                  >\n                    编辑\n                  </el-button>\n                  <el-button\n                    v-if=\"canDelete(record)\"\n                    type=\"danger\"\n                    size=\"small\"\n                    @click.stop=\"deleteRecord(record.id)\"\n                    :icon=\"Delete\"\n                  >\n                    删除\n                  </el-button>\n                </div>\n              </div>\n            </el-card>\n          </div>\n        </div>\n      </div>\n    </el-card>\n\n    <!-- 记录详情对话框 -->\n    <el-dialog\n      v-model=\"showDetailDialog\"\n      title=\"记录详情\"\n      width=\"800px\"\n      :close-on-click-modal=\"false\"\n    >\n      <div v-if=\"currentRecord\" class=\"record-detail\">\n        <div class=\"main-record\">\n          <div class=\"record-header\">\n            <div class=\"header-left\">\n              <h2 class=\"record-title\">{{ currentRecord.title }}</h2>\n              <div class=\"record-tags\">\n                <el-tag :type=\"getTypeColor(currentRecord.type)\" size=\"small\">\n                  {{ getTypeText(currentRecord.type) }}\n                </el-tag>\n              </div>\n            </div>\n            <div class=\"header-right\">\n              <div class=\"author-info\">\n                <el-avatar :size=\"40\" class=\"author-avatar\" :src=\"getAvatarUrl(currentRecord.userAvatar)\">\n                  {{ getInitial(currentRecord.userName) }}\n                </el-avatar>\n                <div class=\"author-details\">\n                  <span class=\"author-name\">{{ currentRecord.userName }}</span>\n                  <el-tag :type=\"currentRecord.userRole === 'TEACHER' ? 'warning' : 'info'\" size=\"small\">\n                    {{ currentRecord.userRole === 'TEACHER' ? '教师' : '学生' }}\n                  </el-tag>\n                  <div class=\"author-time\">{{ formatDate(currentRecord.createTime) }}</div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div class=\"record-content\">\n            <p>{{ currentRecord.content }}</p>\n          </div>\n\n          <!-- 显示评分信息（如果有） -->\n          <div v-if=\"currentRecord.score\" class=\"score-info\">\n            <el-divider content-position=\"left\">评分信息</el-divider>\n            <div class=\"score-details\">\n              <el-tag type=\"success\" size=\"large\">\n                评分: {{ currentRecord.score }}\n              </el-tag>\n              <el-tag v-if=\"currentRecord.weight\" type=\"info\" size=\"small\" style=\"margin-left: 10px\">\n                权重: {{ currentRecord.weight }}\n              </el-tag>\n            </div>\n          </div>\n\n          <!-- 显示附件信息（如果有） -->\n          <div v-if=\"currentRecord.attachments\" class=\"attachments-info\">\n            <el-divider content-position=\"left\">附件</el-divider>\n            <div class=\"attachments-list\">\n              <el-tag type=\"info\">{{ currentRecord.attachments }}</el-tag>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <template #footer>\n        <el-button @click=\"showDetailDialog = false\">关闭</el-button>\n      </template>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { ref, reactive, onMounted, computed } from 'vue'\nimport { useStore } from 'vuex'\nimport { recordAPI, teamAPI, projectAPI } from '@/api'\nimport { ElMessage, ElMessageBox } from 'element-plus'\nimport { getAvatarUrl, getInitial } from '@/utils/avatar'\nimport {\n  Edit,\n  Delete,\n  Refresh\n} from '@element-plus/icons-vue'\n\nexport default {\n  name: 'SubmissionFeedbackView',\n  components: {\n    Edit,\n    Delete,\n    Refresh\n  },\n  setup() {\n    const store = useStore()\n    \n    const loading = ref(false)\n    const showDetailDialog = ref(false)\n    const currentRecord = ref(null)\n    \n    const records = ref([])\n    const myTeams = ref([])\n    const currentTeam = ref(null)\n    const currentTeamId = ref(null)\n    \n    const currentUser = computed(() => store.state.user)\n    const isTeacher = computed(() => currentUser.value?.role === 'TEACHER')\n    const isStudent = computed(() => currentUser.value?.role === 'STUDENT')\n\n    // 加载我的团队\n    const loadMyTeams = async () => {\n      try {\n        if (isTeacher.value) {\n          // 教师获取所有相关团队\n          const response = await projectAPI.getMyProjects()\n          const myProjects = response?.records || []\n\n          if (myProjects.length > 0) {\n            const allTeams = []\n\n            for (const project of myProjects) {\n              try {\n                const teamsResponse = await teamAPI.getProjectTeams(project.id)\n\n                if (teamsResponse?.records) {\n                  const teamsWithProject = teamsResponse.records.map(team => ({\n                    ...team,\n                    projectId: project.id,\n                    projectName: project.name\n                  }))\n                  allTeams.push(...teamsWithProject)\n                }\n              } catch (err) {\n                console.warn(`获取项目 ${project.id} 的团队失败:`, err)\n              }\n            }\n\n            if (allTeams.length > 0) {\n              myTeams.value = allTeams\n              currentTeamId.value = allTeams[0].id\n              await loadRecords()\n            } else {\n              ElMessage.info('暂无团队申请您发布的项目')\n              myTeams.value = []\n            }\n          } else {\n            ElMessage.info('您还没有发布任何项目，无法查看团队记录')\n            myTeams.value = []\n          }\n        } else {\n          // 学生获取自己的团队\n          const response = await teamAPI.getMyTeam()\n\n          if (response) {\n            currentTeam.value = response\n            currentTeamId.value = response.id\n            myTeams.value = [response]\n            await loadRecords()\n          } else {\n            ElMessage.warning('您还没有加入任何团队')\n            myTeams.value = []\n          }\n        }\n      } catch (error) {\n        console.error('加载团队信息失败:', error)\n        ElMessage.error(`加载团队信息失败: ${error.message || '未知错误'}`)\n        myTeams.value = []\n      }\n    }\n\n    // 加载记录列表\n    const loadRecords = async () => {\n      if (!currentTeamId.value) {\n        return\n      }\n\n      try {\n        loading.value = true\n\n        const params = {\n          page: 1,\n          size: 50\n        }\n\n        const response = await recordAPI.getTeamRecords(currentTeamId.value, params)\n        console.log('团队记录原始响应:', response)\n\n        if (response && response.records) {\n          // 只显示SUBMISSION和FEEDBACK类型的记录\n          const filteredRecords = response.records.filter(record => {\n            const allowedTypes = ['SUBMISSION', 'FEEDBACK', 'EVALUATION_ITEM']\n            return allowedTypes.includes(record.type)\n          })\n\n          // 处理用户信息显示\n          const recordsWithUserInfo = filteredRecords.map(record => {\n            let userName = '未知用户'\n            let userRole = 'STUDENT'\n            let userId = null\n\n            if (record.creator) {\n              userName = record.creator.realName || record.creator.username || '未知用户'\n              userRole = record.creator.role || 'STUDENT'\n              userId = record.creator.id\n            }\n\n            return {\n              ...record,\n              userName: userName,\n              userRole: userRole,\n              userId: userId,\n              userAvatar: record.creator?.avatar || null\n            }\n          })\n\n          records.value = recordsWithUserInfo\n        } else {\n          records.value = []\n        }\n\n        loading.value = false\n\n      } catch (error) {\n        console.error('加载记录列表失败:', error)\n        ElMessage.error('加载记录列表失败')\n        loading.value = false\n        records.value = []\n      }\n    }\n\n    // 查看记录详情\n    const viewRecordDetail = async (record) => {\n      currentRecord.value = record\n      showDetailDialog.value = true\n    }\n\n    // 权限检查\n    const canEdit = (record) => {\n      return record.userId === currentUser.value?.id\n    }\n    \n    const canDelete = (record) => {\n      return record.userId === currentUser.value?.id || currentUser.value?.role === 'TEACHER'\n    }\n\n    // 编辑记录\n    const editRecord = (record) => {\n      // TODO: 实现编辑功能\n      ElMessage.info('编辑功能待实现')\n    }\n\n    // 删除记录\n    const deleteRecord = async (id) => {\n      try {\n        await ElMessageBox.confirm('确定要删除这条记录吗？', '确认删除', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        })\n\n        const response = await recordAPI.deleteRecord(id)\n        if (response) {\n          ElMessage.success('记录删除成功')\n          await loadRecords()\n        }\n\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('删除记录失败:', error)\n          ElMessage.error('删除记录失败')\n        }\n      }\n    }\n    \n    // 工具方法\n    const formatDate = (date) => {\n      if (!date) return ''\n      return new Date(date).toLocaleString('zh-CN')\n    }\n    \n    const getTypeColor = (type) => {\n      const colorMap = {\n        'SUBMISSION': 'success',\n        'FEEDBACK': 'info',\n        'EVALUATION_ITEM': 'warning'\n      }\n      return colorMap[type] || 'info'\n    }\n\n    const getTypeText = (type) => {\n      const textMap = {\n        'SUBMISSION': '提交记录',\n        'FEEDBACK': '反馈',\n        'EVALUATION_ITEM': '评价项'\n      }\n      return textMap[type] || type\n    }\n    \n    onMounted(() => {\n      loadMyTeams()\n    })\n    \n    return {\n      loading,\n      showDetailDialog,\n      currentRecord,\n      records,\n      myTeams,\n      currentTeam,\n      currentTeamId,\n      currentUser,\n      isTeacher,\n      isStudent,\n      loadRecords,\n      viewRecordDetail,\n      editRecord,\n      deleteRecord,\n      canEdit,\n      canDelete,\n      formatDate,\n      getTypeColor,\n      getTypeText,\n      getAvatarUrl,\n      getInitial\n    }\n  }\n}\n</script>\n\n<style scoped>\n.submission-feedback {\n  padding: 0;\n}\n\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.header-actions {\n  display: flex;\n  align-items: center;\n  gap: var(--space-3);\n}\n\n.current-team-info {\n  display: flex;\n  align-items: center;\n  gap: var(--space-2);\n}\n\n.team-name {\n  font-weight: 600;\n  color: var(--primary-color);\n}\n\n.record-list {\n  min-height: 400px;\n}\n\n.record-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));\n  gap: var(--space-4);\n  margin-top: var(--space-4);\n}\n\n.record-card {\n  height: 100%;\n}\n\n.record-card .el-card {\n  height: 100%;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.record-card .el-card:hover {\n  transform: translateY(-2px);\n  box-shadow: var(--shadow-lg);\n}\n\n.record-description {\n  color: var(--text-secondary);\n  margin: var(--space-3) 0;\n  display: -webkit-box;\n  -webkit-line-clamp: 3;\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n}\n\n.record-meta {\n  display: flex;\n  align-items: center;\n  gap: var(--space-2);\n  margin: var(--space-3) 0;\n  font-size: var(--font-size-sm);\n  color: var(--text-secondary);\n}\n\n.record-author {\n  font-weight: 500;\n}\n\n.record-footer {\n  margin-top: auto;\n  text-align: right;\n}\n\n.empty-state {\n  text-align: center;\n  padding: 40px 20px;\n}\n\n/* 记录详情对话框样式 */\n.record-detail {\n  max-height: 70vh;\n  overflow-y: auto;\n}\n\n.main-record {\n  background: #fff;\n  border-radius: 8px;\n  margin-bottom: 24px;\n}\n\n.record-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  padding-bottom: 16px;\n  border-bottom: 2px solid #f0f0f0;\n  margin-bottom: 20px;\n}\n\n.header-left {\n  flex: 1;\n}\n\n.record-title {\n  margin: 0 0 12px 0;\n  color: #303133;\n  font-size: 20px;\n  font-weight: 600;\n  line-height: 1.4;\n}\n\n.record-tags {\n  display: flex;\n  gap: 8px;\n}\n\n.header-right {\n  margin-left: 20px;\n}\n\n.author-info {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n}\n\n.author-details {\n  display: flex;\n  flex-direction: column;\n  gap: 4px;\n}\n\n.author-name {\n  font-weight: 600;\n  color: #303133;\n}\n\n.author-time {\n  font-size: 12px;\n  color: #909399;\n}\n\n.record-content {\n  margin: 20px 0;\n  line-height: 1.6;\n  color: #606266;\n}\n\n.score-info, .attachments-info {\n  margin-top: 20px;\n}\n\n.score-details, .attachments-list {\n  margin-top: 10px;\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAqB;;EAGrBA,KAAK,EAAC;AAAa;;EAEjBA,KAAK,EAAC;AAAgB;;;EAcIA,KAAK,EAAC;;;EAC3BA,KAAK,EAAC;AAAW;;;EAGbA,KAAK,EAAC;;;EAYnBA,KAAK,EAAC;AAAa;;;EACuBA,KAAK,EAAC;;;;EAIvCA,KAAK,EAAC;;;EAITA,KAAK,EAAC;AAAoB;;EAExBA,KAAK,EAAC;AAAa;;EAIhBA,KAAK,EAAC;AAAe;;EACrBA,KAAK,EAAC;AAAa;;EAGtBA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAAgB;;;EAkCXA,KAAK,EAAC;;;EACzBA,KAAK,EAAC;AAAa;;EACjBA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAAa;;EAClBA,KAAK,EAAC;AAAc;;EACnBA,KAAK,EAAC;AAAa;;EAMrBA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAa;;EAIjBA,KAAK,EAAC;AAAgB;;EACnBA,KAAK,EAAC;AAAa;;EAIpBA,KAAK,EAAC;AAAa;;EAM3BA,KAAK,EAAC;AAAgB;;;EAKKA,KAAK,EAAC;;;EAE/BA,KAAK,EAAC;AAAe;;;EAWUA,KAAK,EAAC;;;EAErCA,KAAK,EAAC;AAAkB;;;;;;;;;;;;uBAxIvCC,mBAAA,CAmJM,OAnJNC,UAmJM,GAlJJC,YAAA,CA+EUC,kBAAA;IA9EGC,MAAM,EAAAC,QAAA,CACf,MA4BM,CA5BNC,mBAAA,CA4BM,OA5BNC,UA4BM,G,0BA3BJD,mBAAA,CAAc,YAAV,OAAK,qBACTA,mBAAA,CAyBM,OAzBNE,UAyBM,GAvBIC,MAAA,CAAAC,SAAS,I,cADjBC,YAAA,CAYYC,oBAAA;;kBAVDH,MAAA,CAAAI,aAAa;iEAAbJ,MAAA,CAAAI,aAAa,GAAAC,MAAA;MACtBC,WAAW,EAAC,MAAM;MACjBC,QAAM,EAAEP,MAAA,CAAAQ;;wBAGP,MAAuB,E,kBADzBjB,mBAAA,CAKEkB,SAAA,QAAAC,WAAA,CAJeV,MAAA,CAAAW,OAAO,EAAfC,IAAI;6BADbV,YAAA,CAKEW,oBAAA;UAHCC,GAAG,EAAEF,IAAI,CAACG,EAAE;UACZC,KAAK,KAAKJ,IAAI,CAACK,IAAI,KAAKL,IAAI,CAACM,WAAW;UACxCC,KAAK,EAAEP,IAAI,CAACG;;;;qDAGDf,MAAA,CAAAoB,WAAW,I,cAA3B7B,mBAAA,CAGM,OAHN8B,UAGM,GAFJxB,mBAAA,CAAqD,QAArDyB,UAAqD,EAAAC,gBAAA,CAA1BvB,MAAA,CAAAoB,WAAW,CAACH,IAAI,kBAC3CxB,YAAA,CAA8C+B,iBAAA;MAAtCC,IAAI,EAAC,OAAO;MAACC,IAAI,EAAC;;wBAAO,MAAIC,MAAA,QAAAA,MAAA,O,iBAAJ,MAAI,E;;;2BAEvCpC,mBAAA,CAEM,OAFNqC,UAEM,GADJnC,YAAA,CAAkD+B,iBAAA;MAA1CC,IAAI,EAAC,OAAO;MAACC,IAAI,EAAC;;wBAAU,MAAKC,MAAA,QAAAA,MAAA,O,iBAAL,OAAK,E;;;WAG3ClC,YAAA,CAEYoC,oBAAA;MAFAC,OAAK,EAAE9B,MAAA,CAAAQ,WAAW;MAAGuB,IAAI,EAAEC,IAAA,CAAAC;;wBAAS,MAEhDN,MAAA,QAAAA,MAAA,O,iBAFgD,MAEhD,E;;;;sBAMN,MA4CM,C,+BA5CNpC,mBAAA,CA4CM,OA5CN2C,UA4CM,GA3COlC,MAAA,CAAAmC,OAAO,CAACC,MAAM,WAAWpC,MAAA,CAAAqC,OAAO,I,cAA3C9C,mBAAA,CAEM,OAFN+C,UAEM,GADJ7C,YAAA,CAAoC8C,mBAAA;MAA1BC,WAAW,EAAC;IAAW,G,oBAGnCjD,mBAAA,CAsCM,OAtCNkD,UAsCM,I,kBArCJlD,mBAAA,CAoCMkB,SAAA,QAAAC,WAAA,CApCgBV,MAAA,CAAAmC,OAAO,EAAjBO,MAAM;2BAAlBnD,mBAAA,CAoCM;QApC0BuB,GAAG,EAAE4B,MAAM,CAAC3B,EAAE;QAAEzB,KAAK,EAAC;UACpDG,YAAA,CAkCUC,kBAAA;QAlCDiD,MAAM,EAAC,OAAO;QAAEb,OAAK,EAAAzB,MAAA,IAAEL,MAAA,CAAA4C,gBAAgB,CAACF,MAAM;;0BACrD,MAAoC,CAApC7C,mBAAA,CAAoC,YAAA0B,gBAAA,CAA7BmB,MAAM,CAACG,KAAK,2BACnBhD,mBAAA,CAAsD,KAAtDiD,WAAsD,EAAAvB,gBAAA,CAArBmB,MAAM,CAACK,OAAO,kBAE/ClD,mBAAA,CAMM,OANNmD,WAMM,GALUN,MAAM,CAAChB,IAAI,I,cAAzBxB,YAAA,CAESsB,iBAAA;;UAFmBE,IAAI,EAAE1B,MAAA,CAAAiD,YAAY,CAACP,MAAM,CAAChB,IAAI;UAAGD,IAAI,EAAC;;4BAChE,MAA8B,C,kCAA3BzB,MAAA,CAAAkD,WAAW,CAACR,MAAM,CAAChB,IAAI,kB;;4FAE5B7B,mBAAA,CAAwD,QAAxDsD,WAAwD,EAAA5B,gBAAA,CAAzBmB,MAAM,CAACU,QAAQ,kBAC9CvD,mBAAA,CAAoE,QAApEwD,WAAoE,EAAA9B,gBAAA,CAAvCvB,MAAA,CAAAsD,UAAU,CAACZ,MAAM,CAACa,UAAU,kB,GAG3D1D,mBAAA,CAqBM,OArBN2D,WAqBM,GApBJ3D,mBAAA,CAmBM,OAnBN4D,WAmBM,GAjBIzD,MAAA,CAAA0D,OAAO,CAAChB,MAAM,K,cADtBxC,YAAA,CAQY2B,oBAAA;;UANVH,IAAI,EAAC,SAAS;UACdD,IAAI,EAAC,OAAO;UACXK,OAAK,EAAA6B,cAAA,CAAAtD,MAAA,IAAOL,MAAA,CAAA4D,UAAU,CAAClB,MAAM;UAC7BX,IAAI,EAAEC,IAAA,CAAA6B;;4BACR,MAED,KAAAlC,MAAA,QAAAA,MAAA,O,iBAFC,MAED,E;;;uGAEQ3B,MAAA,CAAA8D,SAAS,CAACpB,MAAM,K,cADxBxC,YAAA,CAQY2B,oBAAA;;UANVH,IAAI,EAAC,QAAQ;UACbD,IAAI,EAAC,OAAO;UACXK,OAAK,EAAA6B,cAAA,CAAAtD,MAAA,IAAOL,MAAA,CAAA+D,YAAY,CAACrB,MAAM,CAAC3B,EAAE;UAClCgB,IAAI,EAAEC,IAAA,CAAAgC;;4BACR,MAED,KAAArC,MAAA,QAAAA,MAAA,O,iBAFC,MAED,E;;;;;;gEAtCwB3B,MAAA,CAAAqC,OAAO,E;;MA+C7C4B,mBAAA,aAAgB,EAChBxE,YAAA,CA+DYyE,oBAAA;gBA9DDlE,MAAA,CAAAmE,gBAAgB;+DAAhBnE,MAAA,CAAAmE,gBAAgB,GAAA9D,MAAA;IACzBwC,KAAK,EAAC,MAAM;IACZuB,KAAK,EAAC,OAAO;IACZ,sBAAoB,EAAE;;IAwDZC,MAAM,EAAAzE,QAAA,CACf,MAA2D,CAA3DH,YAAA,CAA2DoC,oBAAA;MAA/CC,OAAK,EAAAH,MAAA,QAAAA,MAAA,MAAAtB,MAAA,IAAEL,MAAA,CAAAmE,gBAAgB;;wBAAU,MAAExC,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;;sBAvDjD,MAoDM,CApDK3B,MAAA,CAAAsE,aAAa,I,cAAxB/E,mBAAA,CAoDM,OApDNgF,WAoDM,GAnDJ1E,mBAAA,CAkDM,OAlDN2E,WAkDM,GAjDJ3E,mBAAA,CAuBM,OAvBN4E,WAuBM,GAtBJ5E,mBAAA,CAOM,OAPN6E,WAOM,GANJ7E,mBAAA,CAAuD,MAAvD8E,WAAuD,EAAApD,gBAAA,CAA3BvB,MAAA,CAAAsE,aAAa,CAACzB,KAAK,kBAC/ChD,mBAAA,CAIM,OAJN+E,WAIM,GAHJnF,YAAA,CAES+B,iBAAA;MAFAE,IAAI,EAAE1B,MAAA,CAAAiD,YAAY,CAACjD,MAAA,CAAAsE,aAAa,CAAC5C,IAAI;MAAGD,IAAI,EAAC;;wBACpD,MAAqC,C,kCAAlCzB,MAAA,CAAAkD,WAAW,CAAClD,MAAA,CAAAsE,aAAa,CAAC5C,IAAI,kB;;qCAIvC7B,mBAAA,CAaM,OAbNgF,WAaM,GAZJhF,mBAAA,CAWM,OAXNiF,WAWM,GAVJrF,YAAA,CAEYsF,oBAAA;MAFAtD,IAAI,EAAE,EAAE;MAAEnC,KAAK,EAAC,eAAe;MAAE0F,GAAG,EAAEhF,MAAA,CAAAiF,YAAY,CAACjF,MAAA,CAAAsE,aAAa,CAACY,UAAU;;wBACrF,MAAwC,C,kCAArClF,MAAA,CAAAmF,UAAU,CAACnF,MAAA,CAAAsE,aAAa,CAAClB,QAAQ,kB;;gCAEtCvD,mBAAA,CAMM,OANNuF,WAMM,GALJvF,mBAAA,CAA6D,QAA7DwF,WAA6D,EAAA9D,gBAAA,CAAhCvB,MAAA,CAAAsE,aAAa,CAAClB,QAAQ,kBACnD3D,YAAA,CAES+B,iBAAA;MAFAE,IAAI,EAAE1B,MAAA,CAAAsE,aAAa,CAACgB,QAAQ;MAAqC7D,IAAI,EAAC;;wBAC7E,MAAwD,C,kCAArDzB,MAAA,CAAAsE,aAAa,CAACgB,QAAQ,6C;;iCAE3BzF,mBAAA,CAAyE,OAAzE0F,WAAyE,EAAAhE,gBAAA,CAA7CvB,MAAA,CAAAsD,UAAU,CAACtD,MAAA,CAAAsE,aAAa,CAACf,UAAU,kB,SAMvE1D,mBAAA,CAEM,OAFN2F,WAEM,GADJ3F,mBAAA,CAAkC,WAAA0B,gBAAA,CAA5BvB,MAAA,CAAAsE,aAAa,CAACvB,OAAO,iB,GAG7BkB,mBAAA,iBAAoB,EACTjE,MAAA,CAAAsE,aAAa,CAACmB,KAAK,I,cAA9BlG,mBAAA,CAUM,OAVNmG,WAUM,GATJjG,YAAA,CAAqDkG,qBAAA;MAAzC,kBAAgB,EAAC;IAAM;wBAAC,MAAIhE,MAAA,QAAAA,MAAA,O,iBAAJ,MAAI,E;;;QACxC9B,mBAAA,CAOM,OAPN+F,WAOM,GANJnG,YAAA,CAES+B,iBAAA;MAFDE,IAAI,EAAC,SAAS;MAACD,IAAI,EAAC;;wBAAQ,MAC9B,C,iBAD8B,OAC9B,GAAAF,gBAAA,CAAGvB,MAAA,CAAAsE,aAAa,CAACmB,KAAK,iB;;QAEdzF,MAAA,CAAAsE,aAAa,CAACuB,MAAM,I,cAAlC3F,YAAA,CAESsB,iBAAA;;MAF2BE,IAAI,EAAC,MAAM;MAACD,IAAI,EAAC,OAAO;MAACqE,KAAyB,EAAzB;QAAA;MAAA;;wBAA0B,MACjF,C,iBADiF,OACjF,GAAAvE,gBAAA,CAAGvB,MAAA,CAAAsE,aAAa,CAACuB,MAAM,iB;;sFAKjC5B,mBAAA,iBAAoB,EACTjE,MAAA,CAAAsE,aAAa,CAACyB,WAAW,I,cAApCxG,mBAAA,CAKM,OALNyG,WAKM,GAJJvG,YAAA,CAAmDkG,qBAAA;MAAvC,kBAAgB,EAAC;IAAM;wBAAC,MAAEhE,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;QACtC9B,mBAAA,CAEM,OAFNoG,WAEM,GADJxG,YAAA,CAA4D+B,iBAAA;MAApDE,IAAI,EAAC;IAAM;wBAAC,MAA+B,C,kCAA5B1B,MAAA,CAAAsE,aAAa,CAACyB,WAAW,iB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}