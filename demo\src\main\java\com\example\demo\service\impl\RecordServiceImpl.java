package com.example.demo.service.impl;

import com.example.demo.common.PageResult;
import com.example.demo.dto.RecordCreateRequest;
import com.example.demo.dto.RecordDTO;
import com.example.demo.dto.RecordUpdateRequest;
import com.example.demo.entity.Project;
import com.example.demo.entity.Record;
import com.example.demo.entity.Team;
import com.example.demo.entity.User;
import com.example.demo.repository.RecordRepository;
import com.example.demo.service.ProjectService;
import com.example.demo.service.RecordService;
import com.example.demo.service.TeamService;
import com.example.demo.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 记录服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RecordServiceImpl implements RecordService {
    
    private final RecordRepository recordRepository;
    private final UserService userService;
    private final ProjectService projectService;
    private final TeamService teamService;
    private final JdbcTemplate jdbcTemplate;
    
    @Override
    @Transactional
    public Record createRecord(Long userId, RecordCreateRequest request) {
        log.info("创建记录: {} by user {}", request.getTitle(), userId);
        log.info("记录详情: type={}, projectId={}, teamId={}, priority={}",
                request.getType(), request.getProjectId(), request.getTeamId(), request.getPriority());

        // 验证请求
        if (!request.isValid()) {
            throw new RuntimeException("记录信息不完整或不正确");
        }
        
        if (!request.isDueDateValid()) {
            throw new RuntimeException("截止时间必须晚于当前时间");
        }
        
        // 获取用户信息
        User user = userService.findById(userId);
        
        // 创建记录
        Record record = new Record();
        record.setType(request.getType());
        record.setSubType(request.getSubType());
        record.setTitle(request.getTitle());
        record.setContent(request.getContent());
        record.setPriority(convertIntegerToPriority(request.getPriority()));
        record.setDueDate(request.getDueDate());
        record.setAttachments(request.getAttachments());
        record.setUser(user);
        record.setUserId(userId);  // 显式设置userId
        record.setParentId(request.getParentId());

        // 根据记录类型设置默认状态
        if (Record.RecordType.TASK.equals(request.getType())) {
            record.setStatus(Record.RecordStatus.PUBLISHED);
        } else if (Record.RecordType.DISCUSSION.equals(request.getType())) {
            record.setStatus(Record.RecordStatus.ACTIVE);
        } else if (Record.RecordType.SUBMISSION.equals(request.getType())) {
            record.setStatus(Record.RecordStatus.SUBMITTED);
        } else if (Record.RecordType.EVALUATION.equals(request.getType())) {
            record.setStatus(Record.RecordStatus.ACTIVE);
        } else if (Record.RecordType.ANNOUNCEMENT.equals(request.getType())) {
            record.setStatus(Record.RecordStatus.ACTIVE);
        } else {
            record.setStatus(Record.RecordStatus.ACTIVE);
        }
        
        // 设置关联项目或团队
        if (request.getProjectId() != null) {
            Project project = projectService.findById(request.getProjectId());
            record.setProject(project);
            record.setProjectId(request.getProjectId());  // 设置独立的projectId字段
        }

        if (request.getTeamId() != null) {
            Team team = teamService.findById(request.getTeamId());
            record.setTeam(team);
            record.setTeamId(request.getTeamId());        // 设置独立的teamId字段
        }
        
        Record savedRecord = recordRepository.save(record);
        log.info("记录创建成功: {}", savedRecord.getTitle());
        log.info("保存后的记录: id={}, projectId={}, teamId={}, status={}",
                savedRecord.getId(), savedRecord.getProjectId(), savedRecord.getTeamId(), savedRecord.getStatus());

        return savedRecord;
    }

    @Override
    public Record findSubmissionByTaskAndUser(Long taskId, Long userId) {
        return recordRepository.findByParentIdAndUserIdAndType(taskId, userId, Record.RecordType.SUBMISSION);
    }

    @Transactional
    public Record updateTaskStatus(Long taskId, String status) {
        log.info("更新任务状态: taskId={}, status={}", taskId, status);

        Record task = findById(taskId);
        if (task == null) {
            throw new RuntimeException("任务不存在");
        }

        Record.RecordStatus newStatus = Record.RecordStatus.valueOf(status);
        task.setStatus(newStatus);

        Record savedTask = recordRepository.save(task);
        log.info("任务状态更新成功: taskId={}, newStatus={}", taskId, newStatus);

        return savedTask;
    }

    @Override
    @Transactional
    public Record updateRecord(Long recordId, Long userId, RecordUpdateRequest request) {
        log.info("更新记录: {} by user {}", recordId, userId);
        
        Record record = findById(recordId);
        
        // 检查权限
        if (!canEditRecord(recordId, userId)) {
            throw new RuntimeException("没有权限编辑此记录");
        }
        
        // 验证截止时间
        if (!request.isDueDateValid()) {
            throw new RuntimeException("截止时间必须晚于当前时间");
        }
        
        // 更新记录信息
        if (StringUtils.hasText(request.getTitle())) {
            record.setTitle(request.getTitle());
        }
        if (StringUtils.hasText(request.getContent())) {
            record.setContent(request.getContent());
        }
        if (request.getPriority() != null) {
            record.setPriority(convertIntegerToPriority(request.getPriority()));
        }
        if (request.getDueDate() != null) {
            record.setDueDate(request.getDueDate());
        }
        if (request.getAttachments() != null) {
            record.setAttachments(request.getAttachments());
        }
        if (request.getStatus() != null) {
            record.setStatus(request.getStatus());
            log.info("更新记录状态: {} -> {}", recordId, request.getStatus());
        }
        
        Record updatedRecord = recordRepository.save(record);
        log.info("记录更新成功: {}", updatedRecord.getTitle());
        
        return updatedRecord;
    }
    
    @Override
    public Record findById(Long id) {
        return recordRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("记录不存在"));
    }
    
    @Override
    public RecordDTO getRecordDTO(Long recordId) {
        Record record = findById(recordId);
        
        // 如果是讨论，获取回复
        if (Record.RecordType.DISCUSSION.equals(record.getType()) && record.getParentId() == null) {
            List<RecordDTO> replies = getDiscussionReplies(recordId);
            return RecordDTO.fromRecord(record, replies);
        }
        
        return RecordDTO.fromRecord(record);
    }
    
    @Override
    @Transactional
    public void deleteRecord(Long recordId, Long userId) {
        log.info("删除记录: {} by user {}", recordId, userId);

        Record record = findById(recordId);

        // 检查权限
        if (!canDeleteRecord(recordId, userId)) {
            throw new RuntimeException("没有权限删除此记录");
        }

        // 删除关联的文件（软删除并清除记录关联）
        try {
            int updatedFiles = jdbcTemplate.update(
                "UPDATE file_info SET status = 'DELETED', record_id = NULL WHERE record_id = ?",
                recordId);
            if (updatedFiles > 0) {
                log.info("已软删除并清除 {} 个关联文件的记录关联", updatedFiles);
            }
        } catch (Exception e) {
            log.warn("删除关联文件时出现问题: {}", e.getMessage());
        }

        // 如果是讨论，删除所有回复
        if (Record.RecordType.DISCUSSION.equals(record.getType()) && record.getParentId() == null) {
            recordRepository.deleteByParentId(recordId);
        }

        recordRepository.delete(record);
        log.info("记录删除成功: {}", record.getTitle());
    }
    
    @Override
    @Transactional
    public Record createTask(Long userId, Long projectId, Long teamId, String title, String content,
                            Integer priority, LocalDateTime dueDate) {
        // 验证项目状态
        if (projectId != null) {
            Project project = projectService.findById(projectId);
            if (project.getStatus() == Project.ProjectStatus.COMPLETED) {
                throw new RuntimeException("不能为已完成的项目创建任务");
            }
            if (project.getStatus() == Project.ProjectStatus.CANCELLED) {
                throw new RuntimeException("不能为已取消的项目创建任务");
            }
        }

        RecordCreateRequest request = new RecordCreateRequest();
        request.setType(Record.RecordType.TASK);
        request.setTitle(title);
        request.setContent(content);
        request.setProjectId(projectId);
        request.setTeamId(teamId);
        request.setPriority(priority);
        request.setDueDate(dueDate);

        return createRecord(userId, request);
    }
    
    @Override
    @Transactional
    public Record createDiscussion(Long userId, Long projectId, Long teamId, String title, String content) {
        return createDiscussion(userId, projectId, teamId, title, content, null);
    }

    @Override
    @Transactional
    public Record createDiscussion(Long userId, Long projectId, Long teamId, String title, String content, String subType) {
        RecordCreateRequest request = new RecordCreateRequest();
        request.setType(Record.RecordType.DISCUSSION);
        request.setSubType(subType);
        request.setTitle(title);
        request.setContent(content);
        request.setProjectId(projectId);
        request.setTeamId(teamId);

        return createRecord(userId, request);
    }
    
    @Override
    @Transactional
    public Record replyDiscussion(Long userId, Long parentId, String content) {
        // 验证父讨论存在
        Record parentRecord = findById(parentId);
        if (!Record.RecordType.DISCUSSION.equals(parentRecord.getType())) {
            throw new RuntimeException("只能回复讨论类型的记录");
        }
        
        RecordCreateRequest request = new RecordCreateRequest();
        request.setType(Record.RecordType.DISCUSSION);
        request.setTitle("回复: " + parentRecord.getTitle());
        request.setContent(content);
        request.setProjectId(parentRecord.getProject() != null ? parentRecord.getProject().getId() : null);
        request.setTeamId(parentRecord.getTeam() != null ? parentRecord.getTeam().getId() : null);
        request.setParentId(parentId);
        
        return createRecord(userId, request);
    }
    
    @Override
    @Transactional
    public Record createSubmission(Long userId, Long projectId, Long teamId, String title, String content, 
                                  String attachments) {
        RecordCreateRequest request = new RecordCreateRequest();
        request.setType(Record.RecordType.SUBMISSION);
        request.setTitle(title);
        request.setContent(content);
        request.setProjectId(projectId);
        request.setTeamId(teamId);
        request.setAttachments(attachments);
        
        return createRecord(userId, request);
    }
    
    @Override
    @Transactional
    public Record createEvaluation(Long userId, Long projectId, Long teamId, String title, String content, 
                                  Integer priority) {
        RecordCreateRequest request = new RecordCreateRequest();
        request.setType(Record.RecordType.EVALUATION);
        request.setTitle(title);
        request.setContent(content);
        request.setProjectId(projectId);
        request.setTeamId(teamId);
        request.setPriority(priority);
        
        return createRecord(userId, request);
    }
    
    @Override
    @Transactional
    public Record createAnnouncement(Long userId, Long projectId, String title, String content, 
                                    Integer priority) {
        RecordCreateRequest request = new RecordCreateRequest();
        request.setType(Record.RecordType.ANNOUNCEMENT);
        request.setTitle(title);
        request.setContent(content);
        request.setProjectId(projectId);
        request.setPriority(priority);
        
        return createRecord(userId, request);
    }
    
    @Override
    public PageResult<RecordDTO> findAllRecords(Pageable pageable) {
        Page<Record> page = recordRepository.findAll(pageable);
        return convertToRecordDTOPage(page);
    }
    
    @Override
    public PageResult<RecordDTO> findProjectRecords(Long projectId, Record.RecordType type, Pageable pageable) {
        Page<Record> page;
        if (type != null) {
            page = recordRepository.findByProjectIdAndType(projectId, type, pageable);
        } else {
            page = recordRepository.findByProjectId(projectId, pageable);
        }
        return convertToRecordDTOPage(page);
    }
    
    @Override
    public PageResult<RecordDTO> findTeamRecords(Long teamId, Record.RecordType type, Pageable pageable) {
        Page<Record> page;
        if (type != null) {
            page = recordRepository.findByTeamIdAndType(teamId, type, pageable);
        } else {
            page = recordRepository.findByTeamId(teamId, pageable);
        }
        return convertToRecordDTOPage(page);
    }
    
    @Override
    public PageResult<RecordDTO> findUserRecords(Long userId, Record.RecordType type, Pageable pageable) {
        Page<Record> page;
        if (type != null) {
            page = recordRepository.findByUserIdAndType(userId, type, pageable);
        } else {
            page = recordRepository.findByUserId(userId, pageable);
        }
        return convertToRecordDTOPage(page);
    }
    
    @Override
    public PageResult<RecordDTO> findProjectTasks(Long projectId, Pageable pageable) {
        Page<Record> page = recordRepository.findProjectTasks(projectId, pageable);
        return convertToRecordDTOPage(page);
    }
    
    @Override
    public PageResult<RecordDTO> findTeamTasks(Long teamId, Pageable pageable) {
        Page<Record> page = recordRepository.findTeamTasks(teamId, pageable);
        return convertToRecordDTOPage(page);
    }
    
    @Override
    public PageResult<RecordDTO> findUserTasks(Long userId, Pageable pageable) {
        Page<Record> page = recordRepository.findUserTasks(userId, pageable);
        return convertToRecordDTOPage(page);
    }
    
    @Override
    public PageResult<RecordDTO> findProjectDiscussions(Long projectId, Pageable pageable) {
        Page<Record> page = recordRepository.findProjectDiscussions(projectId, pageable);
        return convertToRecordDTOPage(page);
    }
    
    @Override
    public PageResult<RecordDTO> findTeamDiscussions(Long teamId, Pageable pageable) {
        Page<Record> page = recordRepository.findTeamDiscussions(teamId, pageable);
        return convertToRecordDTOPage(page);
    }
    
    @Override
    public PageResult<RecordDTO> findProjectSubmissions(Long projectId, Pageable pageable) {
        Page<Record> page = recordRepository.findProjectSubmissions(projectId, pageable);
        return convertToRecordDTOPage(page);
    }
    
    @Override
    public PageResult<RecordDTO> findTeamSubmissions(Long teamId, Pageable pageable) {
        Page<Record> page = recordRepository.findTeamSubmissions(teamId, pageable);
        return convertToRecordDTOPage(page);
    }
    
    @Override
    public PageResult<RecordDTO> findProjectEvaluations(Long projectId, Pageable pageable) {
        Page<Record> page = recordRepository.findProjectEvaluations(projectId, pageable);
        return convertToRecordDTOPage(page);
    }
    
    @Override
    public List<RecordDTO> getDiscussionReplies(Long discussionId) {
        log.info("查询讨论回复 - 讨论ID: {}", discussionId);
        List<Record> replies = recordRepository.findByParentIdOrderByCreateTimeAsc(discussionId);
        log.info("找到 {} 条回复记录", replies.size());

        if (!replies.isEmpty()) {
            log.info("回复记录详情:");
            for (Record reply : replies) {
                log.info("- 回复ID: {}, 标题: {}, 父ID: {}, 用户ID: {}",
                    reply.getId(), reply.getTitle(), reply.getParentId(), reply.getUserId());
            }
        }

        return replies.stream()
                .map(RecordDTO::fromRecord)
                .collect(Collectors.toList());
    }
    
    @Override
    public PageResult<RecordDTO> searchRecords(String keyword, Record.RecordType type, Pageable pageable) {
        Page<Record> page = recordRepository.searchRecords(keyword, type, pageable);
        return convertToRecordDTOPage(page);
    }
    
    @Override
    public List<RecordDTO> getTasksDueSoon(Long userId, int days) {
        LocalDateTime startTime = LocalDateTime.now();
        LocalDateTime endTime = startTime.plusDays(days);
        
        List<Record> tasks = recordRepository.findTasksDueSoon(startTime, endTime);
        return tasks.stream()
                .filter(task -> task.getUser().getId().equals(userId) || 
                               isUserInvolvedInRecord(userId, task))
                .map(RecordDTO::fromRecord)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<RecordDTO> getOverdueTasks(Long userId) {
        List<Record> tasks = recordRepository.findOverdueTasks(LocalDateTime.now());
        return tasks.stream()
                .filter(task -> task.getUser().getId().equals(userId) || 
                               isUserInvolvedInRecord(userId, task))
                .map(RecordDTO::fromRecord)
                .collect(Collectors.toList());
    }
    
    @Override
    @Transactional
    public void markTaskCompleted(Long taskId, Long userId) {
        // 这里需要实现任务完成状态的管理
        // 可以通过添加状态字段或者创建任务状态记录来实现
        log.info("标记任务完成: {} by user {}", taskId, userId);
        // 实现待补充
    }
    
    @Override
    @Transactional
    public void markTaskIncomplete(Long taskId, Long userId) {
        // 这里需要实现任务未完成状态的管理
        log.info("标记任务未完成: {} by user {}", taskId, userId);
        // 实现待补充
    }
    
    @Override
    public boolean canEditRecord(Long recordId, Long userId) {
        try {
            Record record = findById(recordId);
            // 创建者可以编辑
            if (record.getUser().getId().equals(userId)) {
                return true;
            }
            
            // 项目教师可以编辑项目记录
            if (record.getProject() != null && 
                record.getProject().getTeacher().getId().equals(userId)) {
                return true;
            }
            
            // 团队队长可以编辑团队记录
            if (record.getTeam() != null && 
                teamService.isTeamLeader(record.getTeam().getId(), userId)) {
                return true;
            }
            
            return false;
        } catch (Exception e) {
            return false;
        }
    }
    
    @Override
    public boolean canDeleteRecord(Long recordId, Long userId) {
        // 删除权限与编辑权限相同
        return canEditRecord(recordId, userId);
    }
    
    @Override
    public long countRecords() {
        return recordRepository.count();
    }
    
    @Override
    public List<Object[]> countRecordsByType() {
        return recordRepository.countRecordsByType();
    }

    @Override
    public long countRecordsByType(String type) {
        try {
            Record.RecordType recordType = Record.RecordType.valueOf(type.toUpperCase());
            return recordRepository.countByType(recordType);
        } catch (IllegalArgumentException e) {
            log.warn("无效的记录类型: {}", type);
            return 0;
        }
    }

    @Override
    public long countRecordsByTypeAndStatus(String type, String status) {
        try {
            Record.RecordType recordType = Record.RecordType.valueOf(type.toUpperCase());
            Record.RecordStatus recordStatus = Record.RecordStatus.valueOf(status.toUpperCase());
            return recordRepository.countByTypeAndStatus(recordType, recordStatus);
        } catch (IllegalArgumentException e) {
            log.warn("无效的记录类型或状态: type={}, status={}", type, status);
            return 0;
        }
    }

    @Override
    public long countTasksByProjects(List<Long> projectIds) {
        if (projectIds == null || projectIds.isEmpty()) {
            return 0;
        }
        return recordRepository.countByTypeAndProjectIdIn(Record.RecordType.TASK, projectIds);
    }

    @Override
    public long countCompletedTasksByProjects(List<Long> projectIds) {
        if (projectIds == null || projectIds.isEmpty()) {
            return 0;
        }
        return recordRepository.countByTypeAndStatusAndProjectIdIn(
            Record.RecordType.TASK, Record.RecordStatus.COMPLETED, projectIds);
    }

    @Override
    public long countTasksByCreator(Long userId) {
        return recordRepository.countByTypeAndUserId(Record.RecordType.TASK, userId);
    }

    @Override
    public long countPendingTaskSubmissionsByTeacher(Long teacherId) {
        // 查找教师项目的待审核任务提交
        return recordRepository.countPendingTaskSubmissionsByTeacher(teacherId);
    }

    @Override
    public long countTasksByProject(Long projectId) {
        return recordRepository.countByTypeAndProjectId(Record.RecordType.TASK, projectId);
    }

    @Override
    public long countCompletedTasksByProject(Long projectId) {
        return recordRepository.countByTypeAndStatusAndProjectId(Record.RecordType.TASK, Record.RecordStatus.COMPLETED, projectId);
    }

    @Override
    public long countTasksByTeam(Long teamId) {
        return recordRepository.countByTypeAndTeamId(Record.RecordType.TASK, teamId);
    }

    @Override
    public long countCompletedTasksByTeam(Long teamId) {
        return recordRepository.countByTypeAndStatusAndTeamId(Record.RecordType.TASK, Record.RecordStatus.COMPLETED, teamId);
    }

    @Override
    public long countCompletedTasksByUserAndProject(Long userId, Long projectId) {
        return recordRepository.countByTypeAndStatusAndUserIdAndProjectId(
            Record.RecordType.TASK, Record.RecordStatus.COMPLETED, userId, projectId);
    }

    @Override
    public long countSubmissionsByUser(Long userId) {
        return recordRepository.countByTypeAndUserId(Record.RecordType.SUBMISSION, userId);
    }

    @Override
    public long countSubmissionsByUserAndProject(Long userId, Long projectId) {
        return recordRepository.countByTypeAndUserIdAndProjectId(Record.RecordType.SUBMISSION, userId, projectId);
    }

    @Override
    public long countDiscussionsByUser(Long userId) {
        return recordRepository.countByTypeAndUserId(Record.RecordType.DISCUSSION, userId);
    }

    @Override
    public Object[] getUserRecordStats(Long userId) {
        return recordRepository.getUserRecordStats(userId);
    }
    
    @Override
    public Object[] getProjectRecordStats(Long projectId) {
        return recordRepository.getProjectRecordStats(projectId);
    }
    
    @Override
    public Object[] getTeamRecordStats(Long teamId) {
        return recordRepository.getTeamRecordStats(teamId);
    }
    
    /**
     * 转换Record分页为RecordDTO分页
     */
    private PageResult<RecordDTO> convertToRecordDTOPage(Page<Record> page) {
        List<RecordDTO> dtoList = page.getContent().stream()
                .map(RecordDTO::fromRecord)
                .collect(Collectors.toList());
        
        return PageResult.of(dtoList, page.getTotalElements(),
                           page.getNumber() + 1, page.getSize());
    }
    
    /**
     * 检查用户是否参与了记录相关的项目或团队
     */
    private boolean isUserInvolvedInRecord(Long userId, Record record) {
        if (record.getProject() != null) {
            // 检查用户是否在项目相关的团队中
            return teamService.hasTeamInProject(userId, record.getProject().getId());
        }
        
        if (record.getTeam() != null) {
            // 检查用户是否是团队成员
            return teamService.isTeamMember(record.getTeam().getId(), userId);
        }
        
        return false;
    }

    /**
     * 将Integer类型的优先级转换为Priority枚举
     */
    private Record.Priority convertIntegerToPriority(Integer priority) {
        if (priority == null) {
            return Record.Priority.MEDIUM;
        }

        switch (priority) {
            case 1:
                return Record.Priority.LOW;
            case 2:
                return Record.Priority.MEDIUM;
            case 3:
                return Record.Priority.HIGH;
            case 4:
            case 5:
                return Record.Priority.URGENT;
            default:
                return Record.Priority.MEDIUM;
        }
    }
}
