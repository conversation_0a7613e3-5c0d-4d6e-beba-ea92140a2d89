{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, Transition as _Transition, withCtx as _withCtx, vShow as _vShow, withDirectives as _withDirectives, openBlock as _openBlock, createBlock as _createBlock, normalizeClass as _normalizeClass, Fragment as _Fragment, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, withModifiers as _withModifiers } from \"vue\";\nconst _hoisted_1 = {\n  class: \"dashboard dashboard-layout\"\n};\nconst _hoisted_2 = {\n  class: \"dashboard-container\"\n};\nconst _hoisted_3 = {\n  class: \"sidebar-content\"\n};\nconst _hoisted_4 = {\n  class: \"logo-section\"\n};\nconst _hoisted_5 = {\n  class: \"collapsed-toggle\"\n};\nconst _hoisted_6 = {\n  class: \"nav-menu\"\n};\nconst _hoisted_7 = {\n  class: \"nav-section\"\n};\nconst _hoisted_8 = {\n  class: \"nav-section\"\n};\nconst _hoisted_9 = {\n  class: \"nav-submenu-items\"\n};\nconst _hoisted_10 = {\n  class: \"collapsed-menu-icons\"\n};\nconst _hoisted_11 = {\n  class: \"nav-section\"\n};\nconst _hoisted_12 = {\n  class: \"nav-submenu-items\"\n};\nconst _hoisted_13 = {\n  class: \"collapsed-menu-icons\"\n};\nconst _hoisted_14 = {\n  class: \"nav-section\"\n};\nconst _hoisted_15 = {\n  class: \"nav-submenu-items\"\n};\nconst _hoisted_16 = {\n  class: \"collapsed-menu-icons\"\n};\nconst _hoisted_17 = {\n  class: \"nav-section\"\n};\nconst _hoisted_18 = {\n  class: \"nav-submenu-items\"\n};\nconst _hoisted_19 = {\n  class: \"collapsed-menu-icons\"\n};\nconst _hoisted_20 = {\n  class: \"main-content\"\n};\nconst _hoisted_21 = {\n  class: \"top-header\"\n};\nconst _hoisted_22 = {\n  class: \"header-content\"\n};\nconst _hoisted_23 = {\n  class: \"header-left\"\n};\nconst _hoisted_24 = {\n  class: \"breadcrumb\"\n};\nconst _hoisted_25 = {\n  class: \"breadcrumb-text\"\n};\nconst _hoisted_26 = {\n  class: \"page-title\"\n};\nconst _hoisted_27 = {\n  class: \"page-subtitle\"\n};\nconst _hoisted_28 = {\n  class: \"header-right\"\n};\nconst _hoisted_29 = {\n  class: \"user-info\"\n};\nconst _hoisted_30 = {\n  class: \"avatar\"\n};\nconst _hoisted_31 = [\"src\", \"alt\"];\nconst _hoisted_32 = {\n  key: 1\n};\nconst _hoisted_33 = {\n  class: \"user-details\"\n};\nconst _hoisted_34 = {\n  class: \"user-name\"\n};\nconst _hoisted_35 = {\n  class: \"user-role\"\n};\nconst _hoisted_36 = {\n  class: \"page-content\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_Fold = _resolveComponent(\"Fold\");\n  const _component_el_icon = _resolveComponent(\"el-icon\");\n  const _component_Expand = _resolveComponent(\"Expand\");\n  const _component_AdminNavigation = _resolveComponent(\"AdminNavigation\");\n  const _component_House = _resolveComponent(\"House\");\n  const _component_router_link = _resolveComponent(\"router-link\");\n  const _component_ArrowDown = _resolveComponent(\"ArrowDown\");\n  const _component_ArrowRight = _resolveComponent(\"ArrowRight\");\n  const _component_Folder = _resolveComponent(\"Folder\");\n  const _component_FolderOpened = _resolveComponent(\"FolderOpened\");\n  const _component_Plus = _resolveComponent(\"Plus\");\n  const _component_Star = _resolveComponent(\"Star\");\n  const _component_User = _resolveComponent(\"User\");\n  const _component_UserFilled = _resolveComponent(\"UserFilled\");\n  const _component_DocumentChecked = _resolveComponent(\"DocumentChecked\");\n  const _component_ChatLineRound = _resolveComponent(\"ChatLineRound\");\n  const _component_List = _resolveComponent(\"List\");\n  const _component_Setting = _resolveComponent(\"Setting\");\n  const _component_Location = _resolveComponent(\"Location\");\n  const _component_SwitchButton = _resolveComponent(\"SwitchButton\");\n  const _component_router_view = _resolveComponent(\"router-view\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createCommentVNode(\" 侧边栏 \"), _createElementVNode(\"aside\", {\n    class: _normalizeClass([\"sidebar\", {\n      collapsed: $setup.sidebarCollapsed\n    }])\n  }, [_createElementVNode(\"div\", _hoisted_3, [_createCommentVNode(\" Logo区域 \"), _withDirectives(_createElementVNode(\"div\", _hoisted_4, [_cache[11] || (_cache[11] = _createElementVNode(\"div\", {\n    class: \"logo-content\"\n  }, [_createElementVNode(\"h3\", {\n    class: \"logo-text\"\n  }, \"项目协作平台\")], -1 /* CACHED */)), _createCommentVNode(\" 收起按钮 \"), _createElementVNode(\"button\", {\n    class: \"toggle-btn\",\n    onClick: _cache[0] || (_cache[0] = (...args) => $setup.toggleSidebar && $setup.toggleSidebar(...args))\n  }, [_createVNode(_component_el_icon, null, {\n    default: _withCtx(() => [_createVNode(_Transition, {\n      name: \"icon-fade\",\n      mode: \"out-in\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_Fold, {\n        key: \"fold\"\n      })]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  })])], 512 /* NEED_PATCH */), [[_vShow, !$setup.sidebarCollapsed]]), _createCommentVNode(\" 收起状态的logo和展开按钮 \"), _withDirectives(_createElementVNode(\"div\", _hoisted_5, [_cache[12] || (_cache[12] = _createElementVNode(\"div\", {\n    class: \"collapsed-logo\"\n  }, [_createElementVNode(\"div\", {\n    class: \"collapsed-logo-text\"\n  }, \"项\")], -1 /* CACHED */)), _createElementVNode(\"button\", {\n    class: \"toggle-btn collapsed nav-item-style\",\n    onClick: _cache[1] || (_cache[1] = (...args) => $setup.toggleSidebar && $setup.toggleSidebar(...args)),\n    title: \"展开导航栏\"\n  }, [_createVNode(_component_el_icon, null, {\n    default: _withCtx(() => [_createVNode(_Transition, {\n      name: \"icon-fade\",\n      mode: \"out-in\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_Expand, {\n        key: \"expand\"\n      })]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  })])], 512 /* NEED_PATCH */), [[_vShow, $setup.sidebarCollapsed]]), _createCommentVNode(\" 导航菜单 \"), _createCommentVNode(\" 管理员专用导航 \"), $setup.isAdmin ? (_openBlock(), _createBlock(_component_AdminNavigation, {\n    key: 0,\n    \"sidebar-collapsed\": $setup.sidebarCollapsed\n  }, null, 8 /* PROPS */, [\"sidebar-collapsed\"])) : (_openBlock(), _createElementBlock(_Fragment, {\n    key: 1\n  }, [_createCommentVNode(\" 普通用户导航 \"), _createElementVNode(\"nav\", _hoisted_6, [_createCommentVNode(\" 仪表板 \"), _createElementVNode(\"div\", _hoisted_7, [_createVNode(_component_router_link, {\n    to: \"/dashboard\",\n    class: _normalizeClass([\"nav-item\", {\n      active: _ctx.$route.path === '/dashboard'\n    }]),\n    title: $setup.sidebarCollapsed ? '仪表板' : ''\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [_createVNode(_component_House)]),\n      _: 1 /* STABLE */\n    }), _createVNode(_Transition, {\n      name: \"fade\",\n      persisted: \"\"\n    }, {\n      default: _withCtx(() => [_withDirectives(_createElementVNode(\"span\", null, \"仪表板\", 512 /* NEED_PATCH */), [[_vShow, !$setup.sidebarCollapsed]])]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"class\", \"title\"])]), _createCommentVNode(\" 项目管理 \"), _createElementVNode(\"div\", _hoisted_8, [_withDirectives(_createElementVNode(\"div\", {\n    class: _normalizeClass([\"nav-section-title expandable\", {\n      expanded: $setup.projectMenuExpanded\n    }]),\n    onClick: _cache[2] || (_cache[2] = (...args) => $setup.toggleProjectMenu && $setup.toggleProjectMenu(...args))\n  }, [_cache[13] || (_cache[13] = _createElementVNode(\"span\", null, \"项目管理\", -1 /* CACHED */)), _createVNode(_component_el_icon, {\n    class: \"expand-icon\"\n  }, {\n    default: _withCtx(() => [$setup.projectMenuExpanded ? (_openBlock(), _createBlock(_component_ArrowDown, {\n      key: 0\n    })) : (_openBlock(), _createBlock(_component_ArrowRight, {\n      key: 1\n    }))]),\n    _: 1 /* STABLE */\n  })], 2 /* CLASS */), [[_vShow, !$setup.sidebarCollapsed]]), _createCommentVNode(\" 项目管理子菜单 \"), _withDirectives(_createElementVNode(\"div\", _hoisted_9, [$setup.isStudent ? (_openBlock(), _createBlock(_component_router_link, {\n    key: 0,\n    to: \"/dashboard/projects\",\n    class: _normalizeClass([\"nav-subitem\", {\n      active: _ctx.$route.path === '/dashboard/projects'\n    }])\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [_createVNode(_component_Folder)]),\n      _: 1 /* STABLE */\n    }), _cache[14] || (_cache[14] = _createElementVNode(\"span\", null, \"项目浏览\", -1 /* CACHED */))]),\n    _: 1 /* STABLE */,\n    __: [14]\n  }, 8 /* PROPS */, [\"class\"])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 我的项目（教师和学生都有） \"), $setup.isTeacher ? (_openBlock(), _createBlock(_component_router_link, {\n    key: 1,\n    to: \"/dashboard/my-projects\",\n    class: _normalizeClass([\"nav-subitem\", {\n      active: _ctx.$route.path === '/dashboard/my-projects'\n    }])\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [_createVNode(_component_FolderOpened)]),\n      _: 1 /* STABLE */\n    }), _cache[15] || (_cache[15] = _createElementVNode(\"span\", null, \"我的项目\", -1 /* CACHED */))]),\n    _: 1 /* STABLE */,\n    __: [15]\n  }, 8 /* PROPS */, [\"class\"])) : _createCommentVNode(\"v-if\", true), $setup.isStudent ? (_openBlock(), _createBlock(_component_router_link, {\n    key: 2,\n    to: \"/dashboard/student-projects\",\n    class: _normalizeClass([\"nav-subitem\", {\n      active: _ctx.$route.path === '/dashboard/student-projects'\n    }])\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [_createVNode(_component_FolderOpened)]),\n      _: 1 /* STABLE */\n    }), _cache[16] || (_cache[16] = _createElementVNode(\"span\", null, \"我的项目\", -1 /* CACHED */))]),\n    _: 1 /* STABLE */,\n    __: [16]\n  }, 8 /* PROPS */, [\"class\"])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 教师专用功能 \"), $setup.isTeacher ? (_openBlock(), _createBlock(_component_router_link, {\n    key: 3,\n    to: \"/dashboard/projects/create\",\n    class: _normalizeClass([\"nav-subitem\", {\n      active: _ctx.$route.path === '/dashboard/projects/create'\n    }])\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [_createVNode(_component_Plus)]),\n      _: 1 /* STABLE */\n    }), _cache[17] || (_cache[17] = _createElementVNode(\"span\", null, \"创建项目\", -1 /* CACHED */))]),\n    _: 1 /* STABLE */,\n    __: [17]\n  }, 8 /* PROPS */, [\"class\"])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 评价中心（教师和学生都有） \"), _createVNode(_component_router_link, {\n    to: \"/dashboard/evaluation-center\",\n    class: _normalizeClass([\"nav-subitem\", {\n      active: _ctx.$route.path === '/dashboard/evaluation-center'\n    }])\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [_createVNode(_component_Star)]),\n      _: 1 /* STABLE */\n    }), _cache[18] || (_cache[18] = _createElementVNode(\"span\", null, \"评价中心\", -1 /* CACHED */))]),\n    _: 1 /* STABLE */,\n    __: [18]\n  }, 8 /* PROPS */, [\"class\"])], 512 /* NEED_PATCH */), [[_vShow, !$setup.sidebarCollapsed && $setup.projectMenuExpanded]]), _createCommentVNode(\" 收起状态下的项目管理图标 \"), _withDirectives(_createElementVNode(\"div\", _hoisted_10, [$setup.isStudent ? (_openBlock(), _createBlock(_component_router_link, {\n    key: 0,\n    to: \"/dashboard/projects\",\n    class: _normalizeClass([\"nav-item\", {\n      active: _ctx.$route.path === '/dashboard/projects'\n    }]),\n    title: '项目浏览'\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [_createVNode(_component_Folder)]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"class\"])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 我的项目（教师和学生都有） \"), $setup.isTeacher ? (_openBlock(), _createBlock(_component_router_link, {\n    key: 1,\n    to: \"/dashboard/my-projects\",\n    class: _normalizeClass([\"nav-item\", {\n      active: _ctx.$route.path === '/dashboard/my-projects'\n    }]),\n    title: '我的项目'\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [_createVNode(_component_FolderOpened)]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"class\"])) : _createCommentVNode(\"v-if\", true), $setup.isStudent ? (_openBlock(), _createBlock(_component_router_link, {\n    key: 2,\n    to: \"/dashboard/student-projects\",\n    class: _normalizeClass([\"nav-item\", {\n      active: _ctx.$route.path === '/dashboard/student-projects'\n    }]),\n    title: '我的项目'\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [_createVNode(_component_FolderOpened)]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"class\"])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 教师专用功能 \"), $setup.isTeacher ? (_openBlock(), _createBlock(_component_router_link, {\n    key: 3,\n    to: \"/dashboard/projects/create\",\n    class: _normalizeClass([\"nav-item\", {\n      active: _ctx.$route.path === '/dashboard/projects/create'\n    }]),\n    title: '创建项目'\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [_createVNode(_component_Plus)]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"class\"])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 评价中心（教师和学生都有） \"), _createVNode(_component_router_link, {\n    to: \"/dashboard/evaluation-center\",\n    class: _normalizeClass([\"nav-item\", {\n      active: _ctx.$route.path === '/dashboard/evaluation-center'\n    }]),\n    title: '评价中心'\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [_createVNode(_component_Star)]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"class\"])], 512 /* NEED_PATCH */), [[_vShow, $setup.sidebarCollapsed]])]), _createCommentVNode(\" 团队管理 \"), _createElementVNode(\"div\", _hoisted_11, [_withDirectives(_createElementVNode(\"div\", {\n    class: _normalizeClass([\"nav-section-title expandable\", {\n      expanded: $setup.teamMenuExpanded\n    }]),\n    onClick: _cache[3] || (_cache[3] = (...args) => $setup.toggleTeamMenu && $setup.toggleTeamMenu(...args))\n  }, [_cache[19] || (_cache[19] = _createElementVNode(\"span\", null, \"团队管理\", -1 /* CACHED */)), _createVNode(_component_el_icon, {\n    class: \"expand-icon\"\n  }, {\n    default: _withCtx(() => [$setup.teamMenuExpanded ? (_openBlock(), _createBlock(_component_ArrowDown, {\n      key: 0\n    })) : (_openBlock(), _createBlock(_component_ArrowRight, {\n      key: 1\n    }))]),\n    _: 1 /* STABLE */\n  })], 2 /* CLASS */), [[_vShow, !$setup.sidebarCollapsed]]), _createCommentVNode(\" 团队管理子菜单 \"), _withDirectives(_createElementVNode(\"div\", _hoisted_12, [_createCommentVNode(\" 学生团队功能 \"), $setup.isStudent ? (_openBlock(), _createElementBlock(_Fragment, {\n    key: 0\n  }, [_createVNode(_component_router_link, {\n    to: \"/dashboard/my-teams\",\n    class: _normalizeClass([\"nav-subitem\", {\n      active: _ctx.$route.path === '/dashboard/my-teams'\n    }])\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [_createVNode(_component_User)]),\n      _: 1 /* STABLE */\n    }), _cache[20] || (_cache[20] = _createElementVNode(\"span\", null, \"我的团队\", -1 /* CACHED */))]),\n    _: 1 /* STABLE */,\n    __: [20]\n  }, 8 /* PROPS */, [\"class\"]), _createVNode(_component_router_link, {\n    to: \"/dashboard/teams\",\n    class: _normalizeClass([\"nav-subitem\", {\n      active: _ctx.$route.path === '/dashboard/teams'\n    }])\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [_createVNode(_component_UserFilled)]),\n      _: 1 /* STABLE */\n    }), _cache[21] || (_cache[21] = _createElementVNode(\"span\", null, \"浏览团队\", -1 /* CACHED */))]),\n    _: 1 /* STABLE */,\n    __: [21]\n  }, 8 /* PROPS */, [\"class\"]), _createVNode(_component_router_link, {\n    to: \"/dashboard/teams/create\",\n    class: _normalizeClass([\"nav-subitem\", {\n      active: _ctx.$route.path === '/dashboard/teams/create'\n    }])\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [_createVNode(_component_Plus)]),\n      _: 1 /* STABLE */\n    }), _cache[22] || (_cache[22] = _createElementVNode(\"span\", null, \"创建团队\", -1 /* CACHED */))]),\n    _: 1 /* STABLE */,\n    __: [22]\n  }, 8 /* PROPS */, [\"class\"])], 64 /* STABLE_FRAGMENT */)) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 教师查看团队 \"), $setup.isTeacher ? (_openBlock(), _createElementBlock(_Fragment, {\n    key: 1\n  }, [_createVNode(_component_router_link, {\n    to: \"/dashboard/teams\",\n    class: _normalizeClass([\"nav-subitem\", {\n      active: _ctx.$route.path === '/dashboard/teams'\n    }])\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [_createVNode(_component_UserFilled)]),\n      _: 1 /* STABLE */\n    }), _cache[23] || (_cache[23] = _createElementVNode(\"span\", null, \"团队管理\", -1 /* CACHED */))]),\n    _: 1 /* STABLE */,\n    __: [23]\n  }, 8 /* PROPS */, [\"class\"]), _createVNode(_component_router_link, {\n    to: \"/dashboard/review\",\n    class: _normalizeClass([\"nav-subitem\", {\n      active: _ctx.$route.path === '/dashboard/review'\n    }])\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [_createVNode(_component_DocumentChecked)]),\n      _: 1 /* STABLE */\n    }), _cache[24] || (_cache[24] = _createElementVNode(\"span\", null, \"申请审核\", -1 /* CACHED */))]),\n    _: 1 /* STABLE */,\n    __: [24]\n  }, 8 /* PROPS */, [\"class\"])], 64 /* STABLE_FRAGMENT */)) : _createCommentVNode(\"v-if\", true)], 512 /* NEED_PATCH */), [[_vShow, !$setup.sidebarCollapsed && $setup.teamMenuExpanded]]), _createCommentVNode(\" 收起状态下的团队管理图标 \"), _withDirectives(_createElementVNode(\"div\", _hoisted_13, [_createCommentVNode(\" 学生团队功能 \"), $setup.isStudent ? (_openBlock(), _createElementBlock(_Fragment, {\n    key: 0\n  }, [_createVNode(_component_router_link, {\n    to: \"/dashboard/my-teams\",\n    class: _normalizeClass([\"nav-item\", {\n      active: _ctx.$route.path === '/dashboard/my-teams'\n    }]),\n    title: '我的团队'\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [_createVNode(_component_User)]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"class\"]), _createVNode(_component_router_link, {\n    to: \"/dashboard/teams\",\n    class: _normalizeClass([\"nav-item\", {\n      active: _ctx.$route.path === '/dashboard/teams'\n    }]),\n    title: '浏览团队'\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [_createVNode(_component_UserFilled)]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"class\"]), _createVNode(_component_router_link, {\n    to: \"/dashboard/teams/create\",\n    class: _normalizeClass([\"nav-item\", {\n      active: _ctx.$route.path === '/dashboard/teams/create'\n    }]),\n    title: '创建团队'\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [_createVNode(_component_Plus)]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"class\"])], 64 /* STABLE_FRAGMENT */)) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 教师查看团队 \"), $setup.isTeacher ? (_openBlock(), _createElementBlock(_Fragment, {\n    key: 1\n  }, [_createVNode(_component_router_link, {\n    to: \"/dashboard/teams\",\n    class: _normalizeClass([\"nav-item\", {\n      active: _ctx.$route.path === '/dashboard/teams'\n    }]),\n    title: '团队管理'\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [_createVNode(_component_UserFilled)]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"class\"]), _createVNode(_component_router_link, {\n    to: \"/dashboard/review\",\n    class: _normalizeClass([\"nav-item\", {\n      active: _ctx.$route.path === '/dashboard/review'\n    }]),\n    title: '申请审核'\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [_createVNode(_component_DocumentChecked)]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"class\"])], 64 /* STABLE_FRAGMENT */)) : _createCommentVNode(\"v-if\", true)], 512 /* NEED_PATCH */), [[_vShow, $setup.sidebarCollapsed]])]), _createCommentVNode(\" 协作功能 \"), _createElementVNode(\"div\", _hoisted_14, [_withDirectives(_createElementVNode(\"div\", {\n    class: _normalizeClass([\"nav-section-title expandable\", {\n      expanded: $setup.collaborationMenuExpanded\n    }]),\n    onClick: _cache[4] || (_cache[4] = (...args) => $setup.toggleCollaborationMenu && $setup.toggleCollaborationMenu(...args))\n  }, [_cache[25] || (_cache[25] = _createElementVNode(\"span\", null, \"协作功能\", -1 /* CACHED */)), _createVNode(_component_el_icon, {\n    class: \"expand-icon\"\n  }, {\n    default: _withCtx(() => [$setup.collaborationMenuExpanded ? (_openBlock(), _createBlock(_component_ArrowDown, {\n      key: 0\n    })) : (_openBlock(), _createBlock(_component_ArrowRight, {\n      key: 1\n    }))]),\n    _: 1 /* STABLE */\n  })], 2 /* CLASS */), [[_vShow, !$setup.sidebarCollapsed]]), _createCommentVNode(\" 协作功能子菜单 \"), _withDirectives(_createElementVNode(\"div\", _hoisted_15, [_createVNode(_component_router_link, {\n    to: \"/dashboard/collaboration/discussion\",\n    class: _normalizeClass([\"nav-subitem\", {\n      active: _ctx.$route.path === '/dashboard/collaboration/discussion'\n    }])\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [_createVNode(_component_ChatLineRound)]),\n      _: 1 /* STABLE */\n    }), _cache[26] || (_cache[26] = _createElementVNode(\"span\", null, \"项目讨论\", -1 /* CACHED */))]),\n    _: 1 /* STABLE */,\n    __: [26]\n  }, 8 /* PROPS */, [\"class\"]), _createVNode(_component_router_link, {\n    to: \"/dashboard/collaboration/tasks\",\n    class: _normalizeClass([\"nav-subitem\", {\n      active: _ctx.$route.path === '/dashboard/collaboration/tasks'\n    }])\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [_createVNode(_component_List)]),\n      _: 1 /* STABLE */\n    }), _cache[27] || (_cache[27] = _createElementVNode(\"span\", null, \"任务管理\", -1 /* CACHED */))]),\n    _: 1 /* STABLE */,\n    __: [27]\n  }, 8 /* PROPS */, [\"class\"]), _createVNode(_component_router_link, {\n    to: \"/dashboard/collaboration/space\",\n    class: _normalizeClass([\"nav-subitem\", {\n      active: _ctx.$route.path.includes('/dashboard/collaboration/space')\n    }])\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [_createVNode(_component_FolderOpened)]),\n      _: 1 /* STABLE */\n    }), _cache[28] || (_cache[28] = _createElementVNode(\"span\", null, \"协作空间\", -1 /* CACHED */))]),\n    _: 1 /* STABLE */,\n    __: [28]\n  }, 8 /* PROPS */, [\"class\"]), _createCommentVNode(\" 教师任务发布 \"), $setup.isTeacher ? (_openBlock(), _createBlock(_component_router_link, {\n    key: 0,\n    to: \"/dashboard/task-publish\",\n    class: _normalizeClass([\"nav-subitem\", {\n      active: _ctx.$route.path === '/dashboard/task-publish'\n    }])\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [_createVNode(_component_Plus)]),\n      _: 1 /* STABLE */\n    }), _cache[29] || (_cache[29] = _createElementVNode(\"span\", null, \"发布任务\", -1 /* CACHED */))]),\n    _: 1 /* STABLE */,\n    __: [29]\n  }, 8 /* PROPS */, [\"class\"])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 教师任务审核 \"), $setup.isTeacher ? (_openBlock(), _createBlock(_component_router_link, {\n    key: 1,\n    to: \"/dashboard/task-review\",\n    class: _normalizeClass([\"nav-subitem\", {\n      active: _ctx.$route.path === '/dashboard/task-review'\n    }])\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [_createVNode(_component_DocumentChecked)]),\n      _: 1 /* STABLE */\n    }), _cache[30] || (_cache[30] = _createElementVNode(\"span\", null, \"任务审核\", -1 /* CACHED */))]),\n    _: 1 /* STABLE */,\n    __: [30]\n  }, 8 /* PROPS */, [\"class\"])) : _createCommentVNode(\"v-if\", true), false ? (_openBlock(), _createBlock(_component_router_link, {\n    key: 2,\n    to: \"/dashboard/file-management\",\n    class: _normalizeClass([\"nav-subitem\", {\n      active: _ctx.$route.path === '/dashboard/file-management'\n    }])\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [_createVNode(_component_FolderOpened)]),\n      _: 1 /* STABLE */\n    }), _cache[31] || (_cache[31] = _createElementVNode(\"span\", null, \"文件管理\", -1 /* CACHED */))]),\n    _: 1 /* STABLE */,\n    __: [31]\n  }, 8 /* PROPS */, [\"class\"])) : _createCommentVNode(\"v-if\", true)], 512 /* NEED_PATCH */), [[_vShow, !$setup.sidebarCollapsed && $setup.collaborationMenuExpanded]]), _createCommentVNode(\" 收起状态下的协作功能图标 \"), _withDirectives(_createElementVNode(\"div\", _hoisted_16, [_createVNode(_component_router_link, {\n    to: \"/dashboard/collaboration/discussion\",\n    class: _normalizeClass([\"nav-item\", {\n      active: _ctx.$route.path === '/dashboard/collaboration/discussion'\n    }]),\n    title: '项目讨论'\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [_createVNode(_component_ChatLineRound)]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"class\"]), _createVNode(_component_router_link, {\n    to: \"/dashboard/collaboration/tasks\",\n    class: _normalizeClass([\"nav-item\", {\n      active: _ctx.$route.path === '/dashboard/collaboration/tasks'\n    }]),\n    title: '任务管理'\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [_createVNode(_component_List)]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"class\"]), _createVNode(_component_router_link, {\n    to: \"/dashboard/collaboration/space\",\n    class: _normalizeClass([\"nav-item\", {\n      active: _ctx.$route.path.includes('/dashboard/collaboration/space')\n    }]),\n    title: '协作空间'\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [_createVNode(_component_FolderOpened)]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"class\"]), _createCommentVNode(\" 教师任务发布 \"), $setup.isTeacher ? (_openBlock(), _createBlock(_component_router_link, {\n    key: 0,\n    to: \"/dashboard/task-publish\",\n    class: _normalizeClass([\"nav-item\", {\n      active: _ctx.$route.path === '/dashboard/task-publish'\n    }]),\n    title: '发布任务'\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [_createVNode(_component_Plus)]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"class\"])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 教师任务审核 \"), $setup.isTeacher ? (_openBlock(), _createBlock(_component_router_link, {\n    key: 1,\n    to: \"/dashboard/task-review\",\n    class: _normalizeClass([\"nav-item\", {\n      active: _ctx.$route.path === '/dashboard/task-review'\n    }]),\n    title: '任务审核'\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [_createVNode(_component_DocumentChecked)]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"class\"])) : _createCommentVNode(\"v-if\", true), false ? (_openBlock(), _createBlock(_component_router_link, {\n    key: 2,\n    to: \"/dashboard/file-management\",\n    class: _normalizeClass([\"nav-item\", {\n      active: _ctx.$route.path === '/dashboard/file-management'\n    }]),\n    title: '文件管理'\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [_createVNode(_component_FolderOpened)]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"class\"])) : _createCommentVNode(\"v-if\", true)], 512 /* NEED_PATCH */), [[_vShow, $setup.sidebarCollapsed]])]), _createCommentVNode(\" 系统设置 \"), _createElementVNode(\"div\", _hoisted_17, [_withDirectives(_createElementVNode(\"div\", {\n    class: _normalizeClass([\"nav-section-title expandable\", {\n      expanded: $setup.settingsMenuExpanded\n    }]),\n    onClick: _cache[5] || (_cache[5] = (...args) => $setup.toggleSettingsMenu && $setup.toggleSettingsMenu(...args))\n  }, [_cache[32] || (_cache[32] = _createElementVNode(\"span\", null, \"系统设置\", -1 /* CACHED */)), _createVNode(_component_el_icon, {\n    class: \"expand-icon\"\n  }, {\n    default: _withCtx(() => [$setup.settingsMenuExpanded ? (_openBlock(), _createBlock(_component_ArrowDown, {\n      key: 0\n    })) : (_openBlock(), _createBlock(_component_ArrowRight, {\n      key: 1\n    }))]),\n    _: 1 /* STABLE */\n  })], 2 /* CLASS */), [[_vShow, !$setup.sidebarCollapsed]]), _createCommentVNode(\" 系统设置子菜单 \"), _withDirectives(_createElementVNode(\"div\", _hoisted_18, [_createVNode(_component_router_link, {\n    to: \"/dashboard/profile\",\n    class: _normalizeClass([\"nav-subitem\", {\n      active: _ctx.$route.path === '/dashboard/profile'\n    }])\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [_createVNode(_component_Setting)]),\n      _: 1 /* STABLE */\n    }), _cache[33] || (_cache[33] = _createElementVNode(\"span\", null, \"个人设置\", -1 /* CACHED */))]),\n    _: 1 /* STABLE */,\n    __: [33]\n  }, 8 /* PROPS */, [\"class\"])], 512 /* NEED_PATCH */), [[_vShow, !$setup.sidebarCollapsed && $setup.settingsMenuExpanded]]), _createCommentVNode(\" 收起状态下的系统设置图标 \"), _withDirectives(_createElementVNode(\"div\", _hoisted_19, [_createVNode(_component_router_link, {\n    to: \"/dashboard/profile\",\n    class: _normalizeClass([\"nav-item\", {\n      active: _ctx.$route.path === '/dashboard/profile'\n    }]),\n    title: '个人设置'\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [_createVNode(_component_Setting)]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"class\"])], 512 /* NEED_PATCH */), [[_vShow, $setup.sidebarCollapsed]])])])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */))])], 2 /* CLASS */), _createCommentVNode(\" 主内容区 \"), _createElementVNode(\"main\", _hoisted_20, [_createCommentVNode(\" 顶部导航栏 \"), _createElementVNode(\"header\", _hoisted_21, [_createElementVNode(\"div\", _hoisted_22, [_createElementVNode(\"div\", _hoisted_23, [_createElementVNode(\"div\", _hoisted_24, [_createVNode(_component_el_icon, {\n    class: \"breadcrumb-icon\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_Location)]),\n    _: 1 /* STABLE */\n  }), _createElementVNode(\"span\", _hoisted_25, _toDisplayString($setup.getBreadcrumb()), 1 /* TEXT */)]), _createElementVNode(\"h1\", _hoisted_26, _toDisplayString($setup.getPageTitle()), 1 /* TEXT */), _createElementVNode(\"p\", _hoisted_27, _toDisplayString($setup.getPageSubtitle()), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_28, [_createCommentVNode(\" 通知中心 \"), _createCommentVNode(\" <NotificationCenter /> \"), _createCommentVNode(\" 用户下拉菜单 \"), _createElementVNode(\"div\", {\n    class: \"user-menu\",\n    onClick: _cache[10] || (_cache[10] = (...args) => $setup.toggleUserDropdown && $setup.toggleUserDropdown(...args))\n  }, [_createElementVNode(\"div\", _hoisted_29, [_createElementVNode(\"div\", _hoisted_30, [$setup.getAvatarUrl($setup.currentUser?.avatar) ? (_openBlock(), _createElementBlock(\"img\", {\n    key: 0,\n    src: $setup.getAvatarUrl($setup.currentUser?.avatar),\n    alt: $setup.currentUser?.realName,\n    onError: _cache[6] || (_cache[6] = (...args) => $setup.handleAvatarError && $setup.handleAvatarError(...args)),\n    onLoad: _cache[7] || (_cache[7] = (...args) => $setup.handleAvatarLoad && $setup.handleAvatarLoad(...args))\n  }, null, 40 /* PROPS, NEED_HYDRATION */, _hoisted_31)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_32, _toDisplayString($setup.getInitial($setup.currentUser?.realName)), 1 /* TEXT */))]), _createElementVNode(\"div\", _hoisted_33, [_createElementVNode(\"span\", _hoisted_34, _toDisplayString($setup.currentUser?.realName), 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_35, _toDisplayString($setup.getUserRoleText()), 1 /* TEXT */)]), _createVNode(_component_el_icon, {\n    class: _normalizeClass([\"dropdown-arrow\", {\n      rotated: $setup.showUserDropdown\n    }])\n  }, {\n    default: _withCtx(() => [_createVNode(_component_ArrowDown)]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"class\"])]), _createCommentVNode(\" 下拉菜单 \"), _createElementVNode(\"div\", {\n    class: _normalizeClass([\"dropdown-content\", {\n      active: $setup.showUserDropdown\n    }])\n  }, [_createElementVNode(\"div\", {\n    class: \"dropdown-item\",\n    onClick: _cache[8] || (_cache[8] = _withModifiers($event => $setup.handleCommand('profile'), [\"prevent\"]))\n  }, [_createVNode(_component_el_icon, null, {\n    default: _withCtx(() => [_createVNode(_component_User)]),\n    _: 1 /* STABLE */\n  }), _cache[34] || (_cache[34] = _createElementVNode(\"span\", null, \"个人资料\", -1 /* CACHED */))]), _cache[36] || (_cache[36] = _createElementVNode(\"div\", {\n    class: \"dropdown-divider\"\n  }, null, -1 /* CACHED */)), _createElementVNode(\"div\", {\n    class: \"dropdown-item logout\",\n    onClick: _cache[9] || (_cache[9] = _withModifiers($event => $setup.handleCommand('logout'), [\"prevent\"]))\n  }, [_createVNode(_component_el_icon, null, {\n    default: _withCtx(() => [_createVNode(_component_SwitchButton)]),\n    _: 1 /* STABLE */\n  }), _cache[35] || (_cache[35] = _createElementVNode(\"span\", null, \"退出登录\", -1 /* CACHED */))])], 2 /* CLASS */)])])])]), _createCommentVNode(\" 页面内容 \"), _createElementVNode(\"div\", _hoisted_36, [_createVNode(_component_router_view)])])])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_createCommentVNode", "_normalizeClass", "collapsed", "$setup", "sidebarCollapsed", "_hoisted_3", "_hoisted_4", "onClick", "_cache", "args", "toggleSidebar", "_createVNode", "_component_el_icon", "_Transition", "name", "mode", "_component_Fold", "key", "_hoisted_5", "title", "_component_Expand", "isAdmin", "_createBlock", "_component_AdminNavigation", "_Fragment", "_hoisted_6", "_hoisted_7", "_component_router_link", "to", "active", "_ctx", "$route", "path", "_component_House", "persisted", "_hoisted_8", "expanded", "projectMenuExpanded", "toggleProjectMenu", "_component_ArrowDown", "_component_ArrowRight", "_hoisted_9", "isStudent", "_component_Folder", "<PERSON><PERSON><PERSON>er", "_component_FolderOpened", "_component_Plus", "_component_Star", "_hoisted_10", "_hoisted_11", "teamMenuExpanded", "toggleTeamMenu", "_hoisted_12", "_component_User", "_component_UserFilled", "_component_DocumentChecked", "_hoisted_13", "_hoisted_14", "collaborationMenuExpanded", "toggleCollaborationMenu", "_hoisted_15", "_component_ChatLineRound", "_component_List", "includes", "_hoisted_16", "_hoisted_17", "settingsMenuExpanded", "toggleSettingsMenu", "_hoisted_18", "_component_Setting", "_hoisted_19", "_hoisted_20", "_hoisted_21", "_hoisted_22", "_hoisted_23", "_hoisted_24", "_component_Location", "_hoisted_25", "_toDisplayString", "getBreadcrumb", "_hoisted_26", "getPageTitle", "_hoisted_27", "getPageSubtitle", "_hoisted_28", "toggleUserDropdown", "_hoisted_29", "_hoisted_30", "getAvatarUrl", "currentUser", "avatar", "src", "alt", "realName", "onError", "handleAvatarError", "onLoad", "handleAvatarLoad", "_hoisted_32", "getInitial", "_hoisted_33", "_hoisted_34", "_hoisted_35", "getUserRoleText", "rotated", "showUserDropdown", "_withModifiers", "$event", "handleCommand", "_component_SwitchButton", "_hoisted_36", "_component_router_view"], "sources": ["D:\\workspace\\idea\\worker\\work_cli\\src\\views\\DashboardView.vue"], "sourcesContent": ["<template>\n  <div class=\"dashboard dashboard-layout\">\n    <div class=\"dashboard-container\">\n      <!-- 侧边栏 -->\n      <aside class=\"sidebar\" :class=\"{ collapsed: sidebarCollapsed }\">\n        <div class=\"sidebar-content\">\n          <!-- Logo区域 -->\n          <div class=\"logo-section\" v-show=\"!sidebarCollapsed\">\n            <div class=\"logo-content\">\n              <h3 class=\"logo-text\">项目协作平台</h3>\n            </div>\n            <!-- 收起按钮 -->\n            <button class=\"toggle-btn\" @click=\"toggleSidebar\">\n              <el-icon>\n                <transition name=\"icon-fade\" mode=\"out-in\">\n                  <Fold key=\"fold\" />\n                </transition>\n              </el-icon>\n            </button>\n          </div>\n\n          <!-- 收起状态的logo和展开按钮 -->\n          <div class=\"collapsed-toggle\" v-show=\"sidebarCollapsed\">\n            <div class=\"collapsed-logo\">\n              <div class=\"collapsed-logo-text\">项</div>\n            </div>\n            <button class=\"toggle-btn collapsed nav-item-style\" @click=\"toggleSidebar\" title=\"展开导航栏\">\n              <el-icon>\n                <transition name=\"icon-fade\" mode=\"out-in\">\n                  <Expand key=\"expand\" />\n                </transition>\n              </el-icon>\n            </button>\n          </div>\n\n          <!-- 导航菜单 -->\n          <!-- 管理员专用导航 -->\n          <AdminNavigation\n            v-if=\"isAdmin\"\n            :sidebar-collapsed=\"sidebarCollapsed\"\n          />\n\n          <!-- 普通用户导航 -->\n          <nav v-else class=\"nav-menu\">\n            <!-- 仪表板 -->\n            <div class=\"nav-section\">\n              <router-link\n                to=\"/dashboard\"\n                class=\"nav-item\"\n                :class=\"{ active: $route.path === '/dashboard' }\"\n                :title=\"sidebarCollapsed ? '仪表板' : ''\"\n              >\n                <el-icon><House /></el-icon>\n                <transition name=\"fade\">\n                  <span v-show=\"!sidebarCollapsed\">仪表板</span>\n                </transition>\n              </router-link>\n            </div>\n\n            <!-- 项目管理 -->\n            <div class=\"nav-section\">\n              <div\n                v-show=\"!sidebarCollapsed\"\n                class=\"nav-section-title expandable\"\n                @click=\"toggleProjectMenu\"\n                :class=\"{ expanded: projectMenuExpanded }\"\n              >\n                <span>项目管理</span>\n                <el-icon class=\"expand-icon\">\n                  <ArrowDown v-if=\"projectMenuExpanded\" />\n                  <ArrowRight v-else />\n                </el-icon>\n              </div>\n\n              <!-- 项目管理子菜单 -->\n              <div v-show=\"!sidebarCollapsed && projectMenuExpanded\" class=\"nav-submenu-items\">\n                <router-link\n                  v-if=\"isStudent\"\n                  to=\"/dashboard/projects\"\n                  class=\"nav-subitem\"\n                  :class=\"{ active: $route.path === '/dashboard/projects' }\"\n                >\n                  <el-icon><Folder /></el-icon>\n                  <span>项目浏览</span>\n                </router-link>\n\n                <!-- 我的项目（教师和学生都有） -->\n                <router-link\n                  v-if=\"isTeacher\"\n                  to=\"/dashboard/my-projects\"\n                  class=\"nav-subitem\"\n                  :class=\"{ active: $route.path === '/dashboard/my-projects' }\"\n                >\n                  <el-icon><FolderOpened /></el-icon>\n                  <span>我的项目</span>\n                </router-link>\n\n                <router-link\n                  v-if=\"isStudent\"\n                  to=\"/dashboard/student-projects\"\n                  class=\"nav-subitem\"\n                  :class=\"{ active: $route.path === '/dashboard/student-projects' }\"\n                >\n                  <el-icon><FolderOpened /></el-icon>\n                  <span>我的项目</span>\n                </router-link>\n\n                <!-- 教师专用功能 -->\n                <template v-if=\"isTeacher\">\n                  <router-link\n                    to=\"/dashboard/projects/create\"\n                    class=\"nav-subitem\"\n                    :class=\"{ active: $route.path === '/dashboard/projects/create' }\"\n                  >\n                    <el-icon><Plus /></el-icon>\n                    <span>创建项目</span>\n                  </router-link>\n                </template>\n\n                <!-- 评价中心（教师和学生都有） -->\n                <router-link\n                  to=\"/dashboard/evaluation-center\"\n                  class=\"nav-subitem\"\n                  :class=\"{ active: $route.path === '/dashboard/evaluation-center' }\"\n                >\n                  <el-icon><Star /></el-icon>\n                  <span>评价中心</span>\n                </router-link>\n              </div>\n\n              <!-- 收起状态下的项目管理图标 -->\n              <div v-show=\"sidebarCollapsed\" class=\"collapsed-menu-icons\">\n                <router-link\n                  v-if=\"isStudent\"\n                  to=\"/dashboard/projects\"\n                  class=\"nav-item\"\n                  :class=\"{ active: $route.path === '/dashboard/projects' }\"\n                  :title=\"'项目浏览'\"\n                >\n                  <el-icon><Folder /></el-icon>\n                </router-link>\n\n                <!-- 我的项目（教师和学生都有） -->\n                <router-link\n                  v-if=\"isTeacher\"\n                  to=\"/dashboard/my-projects\"\n                  class=\"nav-item\"\n                  :class=\"{ active: $route.path === '/dashboard/my-projects' }\"\n                  :title=\"'我的项目'\"\n                >\n                  <el-icon><FolderOpened /></el-icon>\n                </router-link>\n\n                <router-link\n                  v-if=\"isStudent\"\n                  to=\"/dashboard/student-projects\"\n                  class=\"nav-item\"\n                  :class=\"{ active: $route.path === '/dashboard/student-projects' }\"\n                  :title=\"'我的项目'\"\n                >\n                  <el-icon><FolderOpened /></el-icon>\n                </router-link>\n\n                <!-- 教师专用功能 -->\n                <template v-if=\"isTeacher\">\n                  <router-link\n                    to=\"/dashboard/projects/create\"\n                    class=\"nav-item\"\n                    :class=\"{ active: $route.path === '/dashboard/projects/create' }\"\n                    :title=\"'创建项目'\"\n                  >\n                    <el-icon><Plus /></el-icon>\n                  </router-link>\n                </template>\n\n                <!-- 评价中心（教师和学生都有） -->\n                <router-link\n                  to=\"/dashboard/evaluation-center\"\n                  class=\"nav-item\"\n                  :class=\"{ active: $route.path === '/dashboard/evaluation-center' }\"\n                  :title=\"'评价中心'\"\n                >\n                  <el-icon><Star /></el-icon>\n                </router-link>\n              </div>\n            </div>\n\n            <!-- 团队管理 -->\n            <div class=\"nav-section\">\n              <div\n                v-show=\"!sidebarCollapsed\"\n                class=\"nav-section-title expandable\"\n                @click=\"toggleTeamMenu\"\n                :class=\"{ expanded: teamMenuExpanded }\"\n              >\n                <span>团队管理</span>\n                <el-icon class=\"expand-icon\">\n                  <ArrowDown v-if=\"teamMenuExpanded\" />\n                  <ArrowRight v-else />\n                </el-icon>\n              </div>\n\n              <!-- 团队管理子菜单 -->\n              <div v-show=\"!sidebarCollapsed && teamMenuExpanded\" class=\"nav-submenu-items\">\n                <!-- 学生团队功能 -->\n                <template v-if=\"isStudent\">\n                  <router-link\n                    to=\"/dashboard/my-teams\"\n                    class=\"nav-subitem\"\n                    :class=\"{ active: $route.path === '/dashboard/my-teams' }\"\n                  >\n                    <el-icon><User /></el-icon>\n                    <span>我的团队</span>\n                  </router-link>\n\n                  <router-link\n                    to=\"/dashboard/teams\"\n                    class=\"nav-subitem\"\n                    :class=\"{ active: $route.path === '/dashboard/teams' }\"\n                  >\n                    <el-icon><UserFilled /></el-icon>\n                    <span>浏览团队</span>\n                  </router-link>\n\n                  <router-link\n                    to=\"/dashboard/teams/create\"\n                    class=\"nav-subitem\"\n                    :class=\"{ active: $route.path === '/dashboard/teams/create' }\"\n                  >\n                    <el-icon><Plus /></el-icon>\n                    <span>创建团队</span>\n                  </router-link>\n                </template>\n\n                <!-- 教师查看团队 -->\n                <template v-if=\"isTeacher\">\n                  <router-link\n                    to=\"/dashboard/teams\"\n                    class=\"nav-subitem\"\n                    :class=\"{ active: $route.path === '/dashboard/teams' }\"\n                  >\n                    <el-icon><UserFilled /></el-icon>\n                    <span>团队管理</span>\n                  </router-link>\n\n                  <router-link\n                    to=\"/dashboard/review\"\n                    class=\"nav-subitem\"\n                    :class=\"{ active: $route.path === '/dashboard/review' }\"\n                  >\n                    <el-icon><DocumentChecked /></el-icon>\n                    <span>申请审核</span>\n                  </router-link>\n                </template>\n              </div>\n\n              <!-- 收起状态下的团队管理图标 -->\n              <div v-show=\"sidebarCollapsed\" class=\"collapsed-menu-icons\">\n                <!-- 学生团队功能 -->\n                <template v-if=\"isStudent\">\n                  <router-link\n                    to=\"/dashboard/my-teams\"\n                    class=\"nav-item\"\n                    :class=\"{ active: $route.path === '/dashboard/my-teams' }\"\n                    :title=\"'我的团队'\"\n                  >\n                    <el-icon><User /></el-icon>\n                  </router-link>\n\n                  <router-link\n                    to=\"/dashboard/teams\"\n                    class=\"nav-item\"\n                    :class=\"{ active: $route.path === '/dashboard/teams' }\"\n                    :title=\"'浏览团队'\"\n                  >\n                    <el-icon><UserFilled /></el-icon>\n                  </router-link>\n\n                  <router-link\n                    to=\"/dashboard/teams/create\"\n                    class=\"nav-item\"\n                    :class=\"{ active: $route.path === '/dashboard/teams/create' }\"\n                    :title=\"'创建团队'\"\n                  >\n                    <el-icon><Plus /></el-icon>\n                  </router-link>\n                </template>\n\n                <!-- 教师查看团队 -->\n                <template v-if=\"isTeacher\">\n                  <router-link\n                    to=\"/dashboard/teams\"\n                    class=\"nav-item\"\n                    :class=\"{ active: $route.path === '/dashboard/teams' }\"\n                    :title=\"'团队管理'\"\n                  >\n                    <el-icon><UserFilled /></el-icon>\n                  </router-link>\n\n                  <router-link\n                    to=\"/dashboard/review\"\n                    class=\"nav-item\"\n                    :class=\"{ active: $route.path === '/dashboard/review' }\"\n                    :title=\"'申请审核'\"\n                  >\n                    <el-icon><DocumentChecked /></el-icon>\n                  </router-link>\n                </template>\n              </div>\n            </div>\n\n            <!-- 协作功能 -->\n            <div class=\"nav-section\">\n              <div\n                v-show=\"!sidebarCollapsed\"\n                class=\"nav-section-title expandable\"\n                @click=\"toggleCollaborationMenu\"\n                :class=\"{ expanded: collaborationMenuExpanded }\"\n              >\n                <span>协作功能</span>\n                <el-icon class=\"expand-icon\">\n                  <ArrowDown v-if=\"collaborationMenuExpanded\" />\n                  <ArrowRight v-else />\n                </el-icon>\n              </div>\n\n              <!-- 协作功能子菜单 -->\n              <div v-show=\"!sidebarCollapsed && collaborationMenuExpanded\" class=\"nav-submenu-items\">\n                <router-link\n                  to=\"/dashboard/collaboration/discussion\"\n                  class=\"nav-subitem\"\n                  :class=\"{ active: $route.path === '/dashboard/collaboration/discussion' }\"\n                >\n                  <el-icon><ChatLineRound /></el-icon>\n                  <span>项目讨论</span>\n                </router-link>\n\n                <router-link\n                  to=\"/dashboard/collaboration/tasks\"\n                  class=\"nav-subitem\"\n                  :class=\"{ active: $route.path === '/dashboard/collaboration/tasks' }\"\n                >\n                  <el-icon><List /></el-icon>\n                  <span>任务管理</span>\n                </router-link>\n\n                <router-link\n                  to=\"/dashboard/collaboration/space\"\n                  class=\"nav-subitem\"\n                  :class=\"{ active: $route.path.includes('/dashboard/collaboration/space') }\"\n                >\n                  <el-icon><FolderOpened /></el-icon>\n                  <span>协作空间</span>\n                </router-link>\n\n                <!-- 教师任务发布 -->\n                <router-link\n                  v-if=\"isTeacher\"\n                  to=\"/dashboard/task-publish\"\n                  class=\"nav-subitem\"\n                  :class=\"{ active: $route.path === '/dashboard/task-publish' }\"\n                >\n                  <el-icon><Plus /></el-icon>\n                  <span>发布任务</span>\n                </router-link>\n\n                <!-- 教师任务审核 -->\n                <router-link\n                  v-if=\"isTeacher\"\n                  to=\"/dashboard/task-review\"\n                  class=\"nav-subitem\"\n                  :class=\"{ active: $route.path === '/dashboard/task-review' }\"\n                >\n                  <el-icon><DocumentChecked /></el-icon>\n                  <span>任务审核</span>\n                </router-link>\n\n\n\n                <router-link\n                  to=\"/dashboard/file-management\"\n                  class=\"nav-subitem\"\n                  :class=\"{ active: $route.path === '/dashboard/file-management' }\"\n                  v-if=\"false\"\n                >\n                  <el-icon><FolderOpened /></el-icon>\n                  <span>文件管理</span>\n                </router-link>\n\n\n              </div>\n\n              <!-- 收起状态下的协作功能图标 -->\n              <div v-show=\"sidebarCollapsed\" class=\"collapsed-menu-icons\">\n                <router-link\n                  to=\"/dashboard/collaboration/discussion\"\n                  class=\"nav-item\"\n                  :class=\"{ active: $route.path === '/dashboard/collaboration/discussion' }\"\n                  :title=\"'项目讨论'\"\n                >\n                  <el-icon><ChatLineRound /></el-icon>\n                </router-link>\n\n                <router-link\n                  to=\"/dashboard/collaboration/tasks\"\n                  class=\"nav-item\"\n                  :class=\"{ active: $route.path === '/dashboard/collaboration/tasks' }\"\n                  :title=\"'任务管理'\"\n                >\n                  <el-icon><List /></el-icon>\n                </router-link>\n\n                <router-link\n                  to=\"/dashboard/collaboration/space\"\n                  class=\"nav-item\"\n                  :class=\"{ active: $route.path.includes('/dashboard/collaboration/space') }\"\n                  :title=\"'协作空间'\"\n                >\n                  <el-icon><FolderOpened /></el-icon>\n                </router-link>\n\n                <!-- 教师任务发布 -->\n                <router-link\n                  v-if=\"isTeacher\"\n                  to=\"/dashboard/task-publish\"\n                  class=\"nav-item\"\n                  :class=\"{ active: $route.path === '/dashboard/task-publish' }\"\n                  :title=\"'发布任务'\"\n                >\n                  <el-icon><Plus /></el-icon>\n                </router-link>\n\n                <!-- 教师任务审核 -->\n                <router-link\n                  v-if=\"isTeacher\"\n                  to=\"/dashboard/task-review\"\n                  class=\"nav-item\"\n                  :class=\"{ active: $route.path === '/dashboard/task-review' }\"\n                  :title=\"'任务审核'\"\n                >\n                  <el-icon><DocumentChecked /></el-icon>\n                </router-link>\n\n\n\n                <router-link\n                  to=\"/dashboard/file-management\"\n                  class=\"nav-item\"\n                  :class=\"{ active: $route.path === '/dashboard/file-management' }\"\n                  :title=\"'文件管理'\"\n                  v-if=\"false\"\n                >\n                  <el-icon><FolderOpened /></el-icon>\n                </router-link>\n\n\n              </div>\n            </div>\n\n\n\n\n\n\n\n            <!-- 系统设置 -->\n            <div class=\"nav-section\">\n              <div\n                v-show=\"!sidebarCollapsed\"\n                class=\"nav-section-title expandable\"\n                @click=\"toggleSettingsMenu\"\n                :class=\"{ expanded: settingsMenuExpanded }\"\n              >\n                <span>系统设置</span>\n                <el-icon class=\"expand-icon\">\n                  <ArrowDown v-if=\"settingsMenuExpanded\" />\n                  <ArrowRight v-else />\n                </el-icon>\n              </div>\n\n              <!-- 系统设置子菜单 -->\n              <div v-show=\"!sidebarCollapsed && settingsMenuExpanded\" class=\"nav-submenu-items\">\n                <router-link\n                  to=\"/dashboard/profile\"\n                  class=\"nav-subitem\"\n                  :class=\"{ active: $route.path === '/dashboard/profile' }\"\n                >\n                  <el-icon><Setting /></el-icon>\n                  <span>个人设置</span>\n                </router-link>\n              </div>\n\n              <!-- 收起状态下的系统设置图标 -->\n              <div v-show=\"sidebarCollapsed\" class=\"collapsed-menu-icons\">\n                <router-link\n                  to=\"/dashboard/profile\"\n                  class=\"nav-item\"\n                  :class=\"{ active: $route.path === '/dashboard/profile' }\"\n                  :title=\"'个人设置'\"\n                >\n                  <el-icon><Setting /></el-icon>\n                </router-link>\n              </div>\n            </div>\n          </nav>\n        </div>\n      </aside>\n\n      <!-- 主内容区 -->\n      <main class=\"main-content\">\n        <!-- 顶部导航栏 -->\n        <header class=\"top-header\">\n          <div class=\"header-content\">\n            <div class=\"header-left\">\n              <div class=\"breadcrumb\">\n                <el-icon class=\"breadcrumb-icon\"><Location /></el-icon>\n                <span class=\"breadcrumb-text\">{{ getBreadcrumb() }}</span>\n              </div>\n              <h1 class=\"page-title\">{{ getPageTitle() }}</h1>\n              <p class=\"page-subtitle\">{{ getPageSubtitle() }}</p>\n            </div>\n\n            <div class=\"header-right\">\n              <!-- 通知中心 -->\n              <!-- <NotificationCenter /> -->\n\n              <!-- 用户下拉菜单 -->\n              <div class=\"user-menu\" @click=\"toggleUserDropdown\">\n                <div class=\"user-info\">\n                  <div class=\"avatar\">\n                    <img v-if=\"getAvatarUrl(currentUser?.avatar)\" :src=\"getAvatarUrl(currentUser?.avatar)\" :alt=\"currentUser?.realName\" @error=\"handleAvatarError\" @load=\"handleAvatarLoad\" />\n                    <span v-else>{{ getInitial(currentUser?.realName) }}</span>\n                  </div>\n                  <div class=\"user-details\">\n                    <span class=\"user-name\">{{ currentUser?.realName }}</span>\n                    <span class=\"user-role\">{{ getUserRoleText() }}</span>\n                  </div>\n                  <el-icon class=\"dropdown-arrow\" :class=\"{ rotated: showUserDropdown }\">\n                    <ArrowDown />\n                  </el-icon>\n                </div>\n\n                <!-- 下拉菜单 -->\n                <div class=\"dropdown-content\" :class=\"{ active: showUserDropdown }\">\n                  <div class=\"dropdown-item\" @click.prevent=\"handleCommand('profile')\">\n                    <el-icon><User /></el-icon>\n                    <span>个人资料</span>\n                  </div>\n                  <div class=\"dropdown-divider\"></div>\n                  <div class=\"dropdown-item logout\" @click.prevent=\"handleCommand('logout')\">\n                    <el-icon><SwitchButton /></el-icon>\n                    <span>退出登录</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </header>\n\n        <!-- 页面内容 -->\n        <div class=\"page-content\">\n          <router-view />\n        </div>\n      </main>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { computed, ref, onMounted, onUnmounted } from 'vue'\nimport { useStore } from 'vuex'\nimport { useRouter, useRoute } from 'vue-router'\nimport { ElMessage, ElMessageBox } from 'element-plus'\nimport {\n  House, Folder, FolderOpened, Plus, DocumentChecked,\n  UserFilled, User, Setting, ArrowDown, ArrowRight, SwitchButton,\n  ChatDotRound, ChatLineRound, List, Document,\n  Briefcase, Star, Expand, Fold, Location, Monitor\n} from '@element-plus/icons-vue'\nimport { getAvatarUrl, getInitial } from '@/utils/avatar'\nimport AdminNavigation from '@/components/AdminNavigation.vue'\n// import NotificationCenter from '@/components/NotificationCenter.vue'\n\nexport default {\n  name: 'DashboardView',\n  components: {\n    AdminNavigation,\n    House,\n    Folder,\n    FolderOpened,\n    Plus,\n    DocumentChecked,\n    UserFilled,\n    User,\n    Setting,\n    ArrowDown,\n    ArrowRight,\n    SwitchButton,\n    ChatDotRound,\n    ChatLineRound,\n    List,\n    Document,\n    Briefcase,\n    Star,\n    Expand,\n    Fold,\n    Location,\n    Monitor\n    // NotificationCenter\n  },\n  setup() {\n    const store = useStore()\n    const router = useRouter()\n    const route = useRoute()\n\n    const showUserDropdown = ref(false)\n    const sidebarCollapsed = ref(false)\n\n    // 各个菜单的展开状态，默认都展开\n    const projectMenuExpanded = ref(true)\n    const teamMenuExpanded = ref(true)\n    const collaborationMenuExpanded = ref(true)\n    const settingsMenuExpanded = ref(true)\n\n    const currentUser = computed(() => store.getters.currentUser)\n    const isAdmin = computed(() => store.getters.isAdmin)\n    const isTeacher = computed(() => store.getters.isTeacher)\n    const isStudent = computed(() => store.getters.isStudent)\n\n    const toggleSidebar = () => {\n      sidebarCollapsed.value = !sidebarCollapsed.value\n    }\n\n    // 各个菜单的切换方法\n    const toggleProjectMenu = () => {\n      projectMenuExpanded.value = !projectMenuExpanded.value\n    }\n\n    const toggleTeamMenu = () => {\n      teamMenuExpanded.value = !teamMenuExpanded.value\n    }\n\n    const toggleCollaborationMenu = () => {\n      collaborationMenuExpanded.value = !collaborationMenuExpanded.value\n    }\n\n    const toggleSettingsMenu = () => {\n      settingsMenuExpanded.value = !settingsMenuExpanded.value\n    }\n\n    const getPageTitle = () => {\n      const routeMap = {\n        '/dashboard': '仪表板',\n        '/dashboard/projects': '项目列表',\n        '/dashboard/my-projects': '我的项目',\n        '/dashboard/projects/create': '创建项目',\n        '/dashboard/teams': '团队列表',\n        '/dashboard/my-teams': '我的团队',\n        '/dashboard/teams/create': '创建团队',\n        '/dashboard/review': '申请审核',\n        '/dashboard/profile': '个人设置',\n\n        '/dashboard/files': '文件管理',\n        '/dashboard/evaluation-center': '评价中心',\n        '/dashboard/collaboration/discussion': '项目讨论',\n        '/dashboard/collaboration/tasks': '任务管理',\n        '/dashboard/collaboration/space': '协作空间',\n        '/dashboard/task-publish': '发布任务',\n        '/dashboard/task-review': '任务审核',\n\n        // 管理员页面\n        '/dashboard/admin': '管理仪表板',\n        '/dashboard/admin/users': '用户管理',\n        '/dashboard/admin/projects': '项目管理',\n        '/dashboard/admin/teams': '团队管理',\n        '/dashboard/admin/settings': '系统设置',\n        '/dashboard/admin/logs': '系统日志'\n      }\n      return routeMap[route.path] || '项目协作平台'\n    }\n\n    const getPageSubtitle = () => {\n      const subtitleMap = {\n        '/dashboard': '欢迎回来，查看您的项目概览',\n        '/dashboard/projects': '浏览所有可用的项目',\n        '/dashboard/my-projects': '管理您创建的项目',\n        '/dashboard/projects/create': '创建新的协作项目',\n        '/dashboard/teams': '查看所有团队信息',\n        '/dashboard/my-teams': '管理您的团队',\n        '/dashboard/teams/create': '组建新的项目团队',\n        '/dashboard/review': '审核学生的申请',\n        '/dashboard/profile': '管理您的个人信息',\n\n        '/dashboard/files': '管理和下载项目文件',\n        '/dashboard/test-data': '测试系统数据连接',\n        '/dashboard/collaboration/discussion': '与团队成员讨论项目',\n        '/dashboard/collaboration/tasks': '管理项目任务和进度',\n        '/dashboard/task-publish': '为项目团队发布新任务',\n        '/dashboard/task-review': '审核学生提交的任务',\n\n        // 管理员页面副标题\n        '/dashboard/admin': '查看系统整体运行状态和统计信息',\n        '/dashboard/admin/users': '管理系统中的所有用户账户',\n        '/dashboard/admin/projects': '管理系统中的所有项目',\n        '/dashboard/admin/teams': '管理系统中的所有团队',\n        '/dashboard/admin/settings': '配置系统参数和业务规则',\n        '/dashboard/admin/logs': '查看系统操作日志和错误记录'\n      }\n      return subtitleMap[route.path] || ''\n    }\n\n    const getBreadcrumb = () => {\n      const breadcrumbMap = {\n        '/dashboard': '首页',\n        '/dashboard/projects': '首页 / 项目列表',\n        '/dashboard/my-projects': '首页 / 我的项目',\n        '/dashboard/projects/create': '首页 / 创建项目',\n        '/dashboard/teams': '首页 / 团队列表',\n        '/dashboard/my-teams': '首页 / 我的团队',\n        '/dashboard/teams/create': '首页 / 创建团队',\n        '/dashboard/review': '首页 / 申请审核',\n        '/dashboard/profile': '首页 / 个人设置',\n\n        '/dashboard/files': '首页 / 文件管理',\n        '/dashboard/evaluation-center': '首页 / 评价中心',\n        '/dashboard/collaboration/discussion': '首页 / 项目讨论',\n        '/dashboard/collaboration/tasks': '首页 / 任务管理',\n        '/dashboard/task-publish': '首页 / 发布任务',\n        '/dashboard/task-review': '首页 / 任务审核',\n\n        // 管理员面包屑\n        '/dashboard/admin': '首页 / 系统管理',\n        '/dashboard/admin/users': '首页 / 系统管理 / 用户管理',\n        '/dashboard/admin/projects': '首页 / 系统管理 / 项目管理',\n        '/dashboard/admin/teams': '首页 / 系统管理 / 团队管理',\n        '/dashboard/admin/settings': '首页 / 系统管理 / 系统设置',\n        '/dashboard/admin/logs': '首页 / 系统管理 / 系统日志'\n      }\n      return breadcrumbMap[route.path] || '首页'\n    }\n\n    const getUserRoleText = () => {\n      if (isAdmin.value) return '管理员'\n      if (isTeacher.value) return '教师'\n      if (isStudent.value) return '学生'\n      return '用户'\n    }\n\n    const toggleUserDropdown = () => {\n      showUserDropdown.value = !showUserDropdown.value\n    }\n\n\n\n    const handleCommand = async (command) => {\n      showUserDropdown.value = false\n\n      switch (command) {\n        case 'profile':\n          router.push('/dashboard/profile')\n          break\n        case 'logout':\n          try {\n            await ElMessageBox.confirm(\n              '确定要退出登录吗？',\n              '退出确认',\n              {\n                confirmButtonText: '确定退出',\n                cancelButtonText: '取消',\n                type: 'warning',\n                customClass: 'soft-message-box'\n              }\n            )\n\n            await store.dispatch('logout')\n            ElMessage.success('已安全退出登录')\n            router.push('/auth')\n          } catch (error) {\n            if (error !== 'cancel') {\n              console.error('Logout error:', error)\n            }\n          }\n          break\n      }\n    }\n\n    // 头像加载成功处理\n    const handleAvatarLoad = (event) => {\n      console.log('头像加载成功')\n      event.target.style.display = 'block'\n    }\n\n    // 头像加载错误处理\n    const handleAvatarError = (event) => {\n      console.warn('头像加载失败，使用默认头像')\n      // 隐藏图片元素，显示默认字母头像\n      event.target.style.display = 'none'\n\n      // 清理无效的头像URL，避免重复请求\n      if (store.state.user && store.state.user.avatar) {\n        console.warn('清理无效头像URL:', store.state.user.avatar)\n        // 可以考虑清理store中的无效头像URL\n        // store.commit('SET_USER', { ...store.state.user, avatar: null })\n      }\n    }\n\n    // 点击外部关闭下拉菜单\n    const handleClickOutside = (event) => {\n      if (!event.target.closest('.user-menu')) {\n        showUserDropdown.value = false\n      }\n    }\n\n    // 监听点击事件\n    onMounted(() => {\n      document.addEventListener('click', handleClickOutside)\n    })\n\n    // 组件卸载时移除事件监听器\n    onUnmounted(() => {\n      document.removeEventListener('click', handleClickOutside)\n    })\n\n    return {\n      currentUser,\n      isAdmin,\n      isTeacher,\n      isStudent,\n      showUserDropdown,\n      sidebarCollapsed,\n      // 菜单展开状态\n      projectMenuExpanded,\n      teamMenuExpanded,\n      collaborationMenuExpanded,\n      settingsMenuExpanded,\n      // 方法\n      getPageTitle,\n      getPageSubtitle,\n      getBreadcrumb,\n      getUserRoleText,\n      toggleUserDropdown,\n      toggleSidebar,\n      // 菜单切换方法\n      toggleProjectMenu,\n      toggleTeamMenu,\n      toggleCollaborationMenu,\n      toggleSettingsMenu,\n      handleCommand,\n      // 头像相关方法\n      getAvatarUrl,\n      getInitial,\n      handleAvatarLoad,\n      handleAvatarError\n    }\n  }\n}\n</script>\n\n<style scoped>\n.dashboard {\n  height: 100vh;\n  background: var(--background-color);\n}\n\n.dashboard-container {\n  display: flex;\n  height: 100vh;\n}\n\n/* 侧边栏样式 */\n.sidebar {\n  width: 280px;\n  background: #ffffff;\n  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.1);\n  border-radius: 0;\n  overflow: hidden;\n  position: relative;\n  z-index: 10;\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n  border-right: 1px solid rgba(0, 0, 0, 0.08);\n}\n\n.sidebar.collapsed {\n  width: 80px;\n}\n\n.sidebar-content {\n  height: 100%;\n  padding: var(--space-6);\n  overflow-y: auto;\n  overflow-x: hidden;\n}\n\n/* Logo区域 */\n.logo-section {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin: 0 0 var(--space-8) 0;\n  padding: var(--space-5) var(--space-6);\n  background: transparent;\n  border-radius: 0;\n  border: none;\n  position: relative;\n  box-shadow: none;\n  border-bottom: 1px solid rgba(0, 0, 0, 0.06);\n}\n\n.sidebar.collapsed .logo-section {\n  justify-content: center;\n  padding: var(--space-3);\n  margin: 0 var(--space-2) var(--space-6) var(--space-2);\n}\n\n.logo-content {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  flex: 1;\n}\n\n.sidebar.collapsed .logo-content {\n  justify-content: center;\n}\n\n.logo-icon {\n  width: 48px;\n  height: 48px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: 12px;\n  background: linear-gradient(135deg, #6366f1 0%, #a855f7 100%);\n  color: white;\n  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);\n  transition: all 0.3s ease;\n}\n\n.logo-icon:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 6px 20px rgba(99, 102, 241, 0.4);\n}\n\n.logo-text {\n  font-size: 20px;\n  font-weight: 700;\n  color: #6366f1;\n  margin: 0;\n  letter-spacing: -0.025em;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\n}\n\n.logo-icon-only {\n  width: 40px;\n  height: 40px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: 10px;\n  background: linear-gradient(135deg, #6366f1 0%, #a855f7 100%);\n  color: white;\n  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);\n  transition: all 0.3s ease;\n  margin: 0 auto;\n}\n\n.logo-icon-only:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 6px 20px rgba(99, 102, 241, 0.4);\n}\n\n/* 侧边栏切换按钮 */\n.toggle-btn {\n  width: 32px;\n  height: 32px;\n  border: none;\n  border-radius: 8px;\n  background: rgba(255, 255, 255, 0.8);\n  color: #6366f1;\n  cursor: pointer;\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  backdrop-filter: blur(10px);\n}\n\n.toggle-btn .el-icon {\n  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n.toggle-btn:active {\n  transform: scale(0.95);\n}\n\n.toggle-btn:active .el-icon {\n  transform: scale(0.9);\n}\n\n.collapsed-toggle {\n  padding: 0;\n  margin: 0 4px 16px 4px;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: var(--space-4);\n}\n\n.collapsed-logo {\n  width: 48px;\n  height: 48px;\n  background: #6366f1;\n  border-radius: 12px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  box-shadow: 0 4px 20px rgba(99, 102, 241, 0.25);\n  transition: all 0.3s ease;\n}\n\n.collapsed-logo:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 6px 24px rgba(99, 102, 241, 0.35);\n}\n\n.collapsed-logo-text {\n  font-size: 20px;\n  font-weight: 700;\n  color: white;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\n}\n\n.toggle-btn.collapsed.nav-item-style {\n  width: 100%;\n  height: auto;\n  background: transparent;\n  border-radius: 12px;\n  box-shadow: none;\n  padding: 12px;\n  justify-content: center;\n  align-items: center;\n  color: #64748b;\n  font-weight: 500;\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n.toggle-btn.collapsed.nav-item-style .el-icon {\n  font-size: 16px;\n}\n\n.toggle-btn:hover {\n  background: #6366f1;\n  color: white;\n  transform: translateY(-1px);\n  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);\n}\n\n.toggle-btn:hover .el-icon {\n  transform: scale(1.1);\n}\n\n.sidebar.collapsed .toggle-btn.nav-item-style:hover {\n  background: linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, rgba(168, 85, 247, 0.1) 100%);\n  color: #6366f1;\n  transform: translateY(-1px);\n  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.15);\n}\n\n.sidebar.collapsed .toggle-btn.nav-item-style:active {\n  transform: scale(0.95);\n}\n\n/* 导航菜单 */\n.nav-menu {\n  display: flex;\n  flex-direction: column;\n  gap: var(--space-5);\n}\n\n.nav-section {\n  display: flex;\n  flex-direction: column;\n  gap: var(--space-2);\n}\n\n.nav-section-title {\n  font-size: 11px;\n  font-weight: 600;\n  color: #64748b;\n  text-transform: uppercase;\n  letter-spacing: 0.8px;\n  margin-bottom: var(--space-3);\n  padding: 0 var(--space-4);\n  transition: opacity 0.3s ease;\n}\n\n.nav-section-title.expandable {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  cursor: pointer;\n  padding: var(--space-4) var(--space-5);\n  border-radius: 0;\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n  background: transparent;\n  border: none;\n  margin-bottom: var(--space-1);\n  position: relative;\n  font-weight: var(--font-weight-semibold);\n  font-size: var(--text-sm);\n  color: var(--gray-700);\n  letter-spacing: 0.025em;\n}\n\n.nav-section-title.expandable::after {\n  content: '';\n  position: absolute;\n  left: var(--space-5);\n  right: var(--space-5);\n  bottom: 0;\n  height: 1px;\n  background: linear-gradient(90deg, transparent, var(--gray-300), transparent);\n  opacity: 0.6;\n}\n\n.nav-section-title.expandable:hover {\n  color: var(--gray-900);\n  background: rgba(0, 0, 0, 0.02);\n}\n\n.nav-section-title.expandable.expanded {\n  color: var(--primary-600);\n  background: rgba(99, 102, 241, 0.04);\n}\n\n.nav-section-title.expandable.expanded::after {\n  background: linear-gradient(90deg, transparent, var(--primary-300), transparent);\n}\n\n.expand-icon {\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n  font-size: 14px;\n  color: var(--gray-500);\n  width: 20px;\n  height: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: var(--radius-full);\n  background: transparent;\n}\n\n.nav-section-title.expandable:hover .expand-icon {\n  color: var(--gray-700);\n  background: rgba(0, 0, 0, 0.05);\n}\n\n.nav-section-title.expandable.expanded .expand-icon {\n  transform: rotate(0deg);\n  color: var(--primary-600);\n  background: rgba(99, 102, 241, 0.1);\n}\n\n.nav-submenu-items {\n  margin-left: 0;\n  border-left: none;\n  padding-left: 0;\n  margin-top: var(--space-2);\n  position: relative;\n  background: transparent;\n  border-radius: 0;\n  padding-top: 0;\n  padding-bottom: var(--space-3);\n}\n\n.nav-subitem {\n  display: flex;\n  align-items: center;\n  gap: var(--space-3);\n  padding: var(--space-2_5) var(--space-5);\n  color: var(--gray-500);\n  text-decoration: none;\n  border-radius: 0;\n  font-size: var(--text-sm);\n  font-weight: var(--font-weight-regular);\n  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);\n  margin-bottom: 0;\n  border: none;\n  position: relative;\n  background: transparent;\n  padding-left: calc(var(--space-5) + var(--space-4));\n}\n\n.nav-subitem::before {\n  content: '';\n  position: absolute;\n  left: calc(var(--space-5) + var(--space-2));\n  top: 50%;\n  transform: translateY(-50%);\n  width: 4px;\n  height: 4px;\n  border-radius: var(--radius-full);\n  background: var(--gray-400);\n  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n.nav-subitem:hover {\n  background: rgba(0, 0, 0, 0.02);\n  color: var(--gray-700);\n  padding-left: calc(var(--space-5) + var(--space-5));\n}\n\n.nav-subitem:hover::before {\n  background: var(--primary-500);\n  transform: translateY(-50%) scale(1.5);\n}\n\n.nav-subitem.active {\n  background: rgba(99, 102, 241, 0.06);\n  color: var(--primary-700);\n  font-weight: var(--font-weight-medium);\n}\n\n.nav-subitem.active::before {\n  background: var(--primary-500);\n  transform: translateY(-50%) scale(1.5);\n}\n\n.collapsed-menu-icons {\n  display: flex;\n  flex-direction: column;\n  gap: var(--space-1);\n}\n\n.nav-item {\n  display: flex;\n  align-items: center;\n  gap: var(--space-3);\n  padding: var(--space-3) var(--space-5);\n  border-radius: 0;\n  color: var(--gray-600);\n  text-decoration: none;\n  font-weight: var(--font-weight-medium);\n  font-size: var(--text-sm);\n  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);\n  position: relative;\n  margin: 0;\n  border: none;\n  background: transparent;\n}\n\n.sidebar.collapsed .nav-item {\n  justify-content: center;\n  padding: 12px;\n  margin: 0 8px;\n}\n\n.nav-item::before {\n  content: '';\n  position: absolute;\n  left: 0;\n  top: 0;\n  bottom: 0;\n  width: 3px;\n  background: var(--primary-500);\n  transform: scaleY(0);\n  transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);\n  border-radius: 0 2px 2px 0;\n}\n\n.nav-item:hover {\n  background: rgba(0, 0, 0, 0.03);\n  color: var(--gray-900);\n  padding-left: calc(var(--space-5) + var(--space-1));\n}\n\n.nav-item.active {\n  background: rgba(99, 102, 241, 0.08);\n  color: var(--primary-700);\n  font-weight: var(--font-weight-semibold);\n}\n\n.nav-item.active::before {\n  transform: scaleY(1);\n}\n\n.nav-item.active:hover {\n  background: rgba(99, 102, 241, 0.12);\n}\n\n.nav-item.active::before {\n  content: '';\n  position: absolute;\n  left: -6px;\n  top: 50%;\n  transform: translateY(-50%);\n  width: 3px;\n  height: 24px;\n  background: var(--primary-gradient);\n  border-radius: 0 var(--radius-sm) var(--radius-sm) 0;\n  box-shadow: var(--shadow-sm);\n}\n\n.sidebar.collapsed .nav-item.active::before {\n  display: none;\n}\n\n/* 过渡动画 */\n.fade-enter-active, .fade-leave-active {\n  transition: opacity 0.3s ease;\n}\n\n.fade-enter-from, .fade-leave-to {\n  opacity: 0;\n}\n\n/* 图标切换动画 */\n.icon-fade-enter-active, .icon-fade-leave-active {\n  transition: all 0.2s ease;\n}\n\n.icon-fade-enter-from {\n  opacity: 0;\n  transform: scale(0.8) rotate(90deg);\n}\n\n.icon-fade-leave-to {\n  opacity: 0;\n  transform: scale(0.8) rotate(-90deg);\n}\n\n/* 子菜单 */\n.nav-submenu {\n  margin-left: var(--space-4);\n}\n\n.nav-submenu-title {\n  display: flex;\n  align-items: center;\n  gap: var(--space-3);\n  padding: var(--space-3) var(--space-4);\n  color: var(--text-secondary);\n  font-weight: var(--font-weight-medium);\n  font-size: var(--font-size-sm);\n}\n\n.nav-submenu-items {\n  display: flex;\n  flex-direction: column;\n  gap: var(--space-1);\n  margin-left: var(--space-6);\n  padding-left: var(--space-4);\n  border-left: 2px solid rgba(160, 118, 249, 0.2);\n}\n\n.nav-subitem {\n  display: flex;\n  align-items: center;\n  gap: var(--space-3);\n  padding: var(--space-3);\n  border-radius: var(--radius-md);\n  color: var(--text-secondary);\n  text-decoration: none;\n  font-size: var(--font-size-sm);\n  transition: all var(--transition-base);\n}\n\n.nav-subitem:hover {\n  background: rgba(160, 118, 249, 0.1);\n  color: var(--primary-gradient-start);\n}\n\n.nav-subitem.active {\n  background: var(--primary-gradient);\n  color: var(--text-on-primary);\n}\n\n/* 主内容区域 */\n.main-content {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  overflow: hidden;\n  transition: margin-left 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n/* 顶部导航栏 */\n.top-header {\n  background: #ffffff;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n  border-radius: 0;\n  margin: 0;\n  position: relative;\n  z-index: 5;\n  border: none;\n  border-bottom: 1px solid rgba(0, 0, 0, 0.06);\n}\n\n.header-content {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: var(--space-5) 0;\n  width: 100%;\n  min-height: 80px;\n}\n\n.header-left {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  padding-left: var(--space-6);\n}\n\n.header-right {\n  flex-shrink: 0;\n  display: flex;\n  align-items: center;\n  padding-right: var(--space-6);\n}\n\n/* 面包屑导航 */\n.breadcrumb {\n  display: flex;\n  align-items: center;\n  gap: var(--space-2);\n  margin-bottom: var(--space-2);\n  padding: 0;\n  background: transparent;\n  border: none;\n  width: fit-content;\n}\n\n.breadcrumb-icon {\n  color: var(--gray-400);\n  font-size: 14px;\n  margin-right: var(--space-1);\n}\n\n.breadcrumb-text {\n  font-size: var(--text-xs);\n  color: var(--gray-500);\n  font-weight: var(--font-weight-medium);\n  font-family: var(--font-sans);\n  text-transform: uppercase;\n  letter-spacing: 0.05em;\n}\n\n.page-title {\n  font-size: 24px;\n  font-weight: 600;\n  color: var(--gray-900);\n  margin: 0 0 var(--space-1) 0;\n  background: none;\n  line-height: 1.3;\n  font-family: var(--font-sans);\n  position: relative;\n  letter-spacing: -0.01em;\n}\n\n.page-subtitle {\n  font-size: var(--text-sm);\n  color: var(--gray-500);\n  margin: 0;\n  font-weight: var(--font-weight-regular);\n  font-family: var(--font-sans);\n  line-height: var(--leading-relaxed);\n}\n\n.header-right {\n  display: flex;\n  align-items: center;\n  gap: var(--space-4);\n}\n\n/* 通知按钮 */\n.notification-btn {\n  position: relative;\n  width: 44px;\n  height: 44px;\n  border: none;\n  border-radius: var(--radius-full);\n  background: var(--background-color);\n  color: var(--text-secondary);\n  cursor: pointer;\n  transition: all var(--transition-base);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.notification-btn:hover {\n  background: var(--primary-gradient);\n  color: var(--text-on-primary);\n  transform: translateY(-2px);\n  box-shadow: var(--shadow-soft-colored);\n}\n\n.notification-badge {\n  position: absolute;\n  top: -2px;\n  right: -2px;\n  width: 18px;\n  height: 18px;\n  background: var(--accent-color);\n  color: white;\n  border-radius: 50%;\n  font-size: 10px;\n  font-weight: var(--font-weight-bold);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border: 2px solid var(--surface-color);\n}\n\n/* 用户菜单 */\n.user-menu {\n  position: relative;\n  cursor: pointer;\n}\n\n.user-info {\n  display: flex;\n  align-items: center;\n  gap: var(--space-3);\n  padding: var(--space-2) var(--space-3);\n  border-radius: var(--radius-lg);\n  background: transparent;\n  border: 1px solid rgba(0, 0, 0, 0.08);\n  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);\n  box-shadow: none;\n}\n\n.user-info:hover {\n  background: rgba(0, 0, 0, 0.02);\n  border-color: rgba(0, 0, 0, 0.12);\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);\n}\n\n.avatar {\n  width: 36px;\n  height: 36px;\n  border-radius: var(--radius-lg);\n  background: linear-gradient(135deg, var(--primary-500), var(--primary-600));\n  color: var(--text-on-primary);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-weight: var(--font-weight-semibold);\n  font-size: var(--text-sm);\n  border: none;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);\n  overflow: hidden;\n  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);\n  position: relative;\n}\n\n.avatar img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n.user-details {\n  display: flex;\n  flex-direction: column;\n  align-items: flex-start;\n  margin-right: var(--space-2);\n}\n\n.user-name {\n  font-size: var(--text-sm);\n  font-weight: var(--font-weight-semibold);\n  color: var(--gray-900);\n  line-height: var(--leading-tight);\n  font-family: var(--font-sans);\n  margin-bottom: 2px;\n}\n\n.user-role {\n  font-size: var(--text-xs);\n  color: var(--gray-500);\n  line-height: var(--leading-tight);\n  font-family: var(--font-sans);\n  font-weight: var(--font-weight-regular);\n}\n\n.dropdown-arrow {\n  color: var(--gray-400);\n  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);\n  font-size: 14px;\n}\n\n.user-info:hover .dropdown-arrow {\n  color: var(--gray-600);\n}\n\n.dropdown-arrow.rotated {\n  transform: rotate(180deg);\n}\n\n/* 下拉菜单 */\n.dropdown-content {\n  position: absolute;\n  top: calc(100% + 8px);\n  right: 0;\n  min-width: 200px;\n  background: #ffffff;\n  border-radius: var(--radius-xl);\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\n  padding: var(--space-2);\n  z-index: var(--z-dropdown);\n  opacity: 0;\n  visibility: hidden;\n  transform: translateY(-8px);\n  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);\n  border: 1px solid rgba(0, 0, 0, 0.08);\n}\n\n.dropdown-content.active {\n  opacity: 1;\n  visibility: visible;\n  transform: translateY(0);\n}\n\n.dropdown-item {\n  display: flex;\n  align-items: center;\n  gap: var(--space-3);\n  padding: var(--space-3);\n  border-radius: var(--radius-lg);\n  color: var(--gray-700);\n  text-decoration: none;\n  transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);\n  cursor: pointer;\n  font-size: var(--text-sm);\n  font-family: var(--font-sans);\n  font-weight: var(--font-weight-medium);\n  border: none;\n  position: relative;\n  background: transparent;\n}\n\n.dropdown-item:hover {\n  background: rgba(0, 0, 0, 0.04);\n  color: var(--gray-900);\n}\n\n.dropdown-item .el-icon {\n  font-size: 16px;\n  color: var(--gray-500);\n  transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n.dropdown-item:hover .el-icon {\n  color: var(--gray-700);\n}\n\n.dropdown-item.logout {\n  color: var(--error-600);\n}\n\n.dropdown-item.logout:hover {\n  background: rgba(239, 68, 68, 0.08);\n  color: var(--error-700);\n}\n\n.dropdown-item.logout .el-icon {\n  color: var(--error-500);\n}\n\n.dropdown-item.logout:hover .el-icon {\n  color: var(--error-600);\n}\n\n.dropdown-divider {\n  height: 1px;\n  background: rgba(0, 0, 0, 0.08);\n  margin: var(--space-1) 0;\n}\n\n/* 页面内容 */\n.page-content {\n  flex: 1;\n  padding: var(--space-6) var(--space-4) var(--space-4) var(--space-4);\n  overflow-y: auto;\n  overflow-x: hidden;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .sidebar {\n    width: 240px;\n  }\n\n  .header-content {\n    padding: var(--space-4) var(--space-6);\n  }\n\n  .page-title {\n    font-size: 20px;\n  }\n\n  .user-details {\n    display: none;\n  }\n\n  .page-content {\n    padding: var(--space-4);\n  }\n}\n\n@media (max-width: 640px) {\n  .dashboard-container {\n    flex-direction: column;\n  }\n\n  .sidebar {\n    width: 100%;\n    height: auto;\n    border-radius: 0;\n  }\n\n  .sidebar-content {\n    padding: var(--space-4);\n  }\n\n  .nav-menu {\n    flex-direction: row;\n    overflow-x: auto;\n    gap: var(--space-2);\n  }\n\n  .nav-section {\n    flex-direction: row;\n    min-width: max-content;\n  }\n\n  .nav-section-title {\n    display: none;\n  }\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAA4B;;EAChCA,KAAK,EAAC;AAAqB;;EAGvBA,KAAK,EAAC;AAAiB;;EAErBA,KAAK,EAAC;AAAc;;EAepBA,KAAK,EAAC;AAAkB;;EAqBjBA,KAAK,EAAC;AAAU;;EAErBA,KAAK,EAAC;AAAa;;EAenBA,KAAK,EAAC;AAAa;;EAeiCA,KAAK,EAAC;AAAmB;;EAwDjDA,KAAK,EAAC;AAAsB;;EAyDxDA,KAAK,EAAC;AAAa;;EAe8BA,KAAK,EAAC;AAAmB;;EAsD9CA,KAAK,EAAC;AAAsB;;EAuDxDA,KAAK,EAAC;AAAa;;EAeuCA,KAAK,EAAC;AAAmB;;EAkEvDA,KAAK,EAAC;AAAsB;;EAyExDA,KAAK,EAAC;AAAa;;EAekCA,KAAK,EAAC;AAAmB;;EAYlDA,KAAK,EAAC;AAAsB;;EAgB7DA,KAAK,EAAC;AAAc;;EAEhBA,KAAK,EAAC;AAAY;;EACnBA,KAAK,EAAC;AAAgB;;EACpBA,KAAK,EAAC;AAAa;;EACjBA,KAAK,EAAC;AAAY;;EAEfA,KAAK,EAAC;AAAiB;;EAE3BA,KAAK,EAAC;AAAY;;EACnBA,KAAK,EAAC;AAAe;;EAGrBA,KAAK,EAAC;AAAc;;EAMhBA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAQ;;;;;;EAIdA,KAAK,EAAC;AAAc;;EACjBA,KAAK,EAAC;AAAW;;EACjBA,KAAK,EAAC;AAAW;;EAyB9BA,KAAK,EAAC;AAAc;;;;;;;;;;;;;;;;;;;;;;;uBA/iB/BC,mBAAA,CAojBM,OApjBNC,UAojBM,GAnjBJC,mBAAA,CAkjBM,OAljBNC,UAkjBM,GAjjBJC,mBAAA,SAAY,EACZF,mBAAA,CAsfQ;IAtfDH,KAAK,EAAAM,eAAA,EAAC,SAAS;MAAAC,SAAA,EAAsBC,MAAA,CAAAC;IAAgB;MAC1DN,mBAAA,CAofM,OApfNO,UAofM,GAnfJL,mBAAA,YAAe,E,gBACfF,mBAAA,CAYM,OAZNQ,UAYM,G,4BAXJR,mBAAA,CAEM;IAFDH,KAAK,EAAC;EAAc,IACvBG,mBAAA,CAAiC;IAA7BH,KAAK,EAAC;EAAW,GAAC,QAAM,E,qBAE9BK,mBAAA,UAAa,EACbF,mBAAA,CAMS;IANDH,KAAK,EAAC,YAAY;IAAEY,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEN,MAAA,CAAAO,aAAA,IAAAP,MAAA,CAAAO,aAAA,IAAAD,IAAA,CAAa;MAC9CE,YAAA,CAIUC,kBAAA;sBAHR,MAEa,CAFbD,YAAA,CAEaE,WAAA;MAFDC,IAAI,EAAC,WAAW;MAACC,IAAI,EAAC;;wBAChC,MAAmB,CAAnBJ,YAAA,CAAmBK,eAAA;QAAbC,GAAG,EAAC;MAAM,G;;;;2CARWd,MAAA,CAAAC,gBAAgB,E,GAcnDJ,mBAAA,oBAAuB,E,gBACvBF,mBAAA,CAWM,OAXNoB,UAWM,G,4BAVJpB,mBAAA,CAEM;IAFDH,KAAK,EAAC;EAAgB,IACzBG,mBAAA,CAAwC;IAAnCH,KAAK,EAAC;EAAqB,GAAC,GAAC,E,qBAEpCG,mBAAA,CAMS;IANDH,KAAK,EAAC,qCAAqC;IAAEY,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEN,MAAA,CAAAO,aAAA,IAAAP,MAAA,CAAAO,aAAA,IAAAD,IAAA,CAAa;IAAEU,KAAK,EAAC;MAC/ER,YAAA,CAIUC,kBAAA;sBAHR,MAEa,CAFbD,YAAA,CAEaE,WAAA;MAFDC,IAAI,EAAC,WAAW;MAACC,IAAI,EAAC;;wBAChC,MAAuB,CAAvBJ,YAAA,CAAuBS,iBAAA;QAAfH,GAAG,EAAC;MAAQ,G;;;;0CAPUd,MAAA,CAAAC,gBAAgB,E,GAatDJ,mBAAA,UAAa,EACbA,mBAAA,aAAgB,EAERG,MAAA,CAAAkB,OAAO,I,cADfC,YAAA,CAGEC,0BAAA;;IADC,mBAAiB,EAAEpB,MAAA,CAAAC;mEAItBR,mBAAA,CA6cM4B,SAAA;IAAAP,GAAA;EAAA,IA9cNjB,mBAAA,YAAe,EACfF,mBAAA,CA6cM,OA7cN2B,UA6cM,GA5cJzB,mBAAA,SAAY,EACZF,mBAAA,CAYM,OAZN4B,UAYM,GAXJf,YAAA,CAUcgB,sBAAA;IATZC,EAAE,EAAC,YAAY;IACfjC,KAAK,EAAAM,eAAA,EAAC,UAAU;MAAA4B,MAAA,EACEC,IAAA,CAAAC,MAAM,CAACC,IAAI;IAAA;IAC5Bb,KAAK,EAAEhB,MAAA,CAAAC,gBAAgB;;sBAExB,MAA4B,CAA5BO,YAAA,CAA4BC,kBAAA;wBAAnB,MAAS,CAATD,YAAA,CAASsB,gBAAA,E;;QAClBtB,YAAA,CAEaE,WAAA;MAFDC,IAAI,EAAC,MAAM;MAAvBoB,SAEa,EAFb;;wBACE,MAA2C,C,gBAA3CpC,mBAAA,CAA2C,cAAV,KAAG,0B,UAArBK,MAAA,CAAAC,gBAAgB,E;;;;2CAKrCJ,mBAAA,UAAa,EACbF,mBAAA,CA6HM,OA7HNqC,UA6HM,G,gBA5HJrC,mBAAA,CAWM;IATJH,KAAK,EAAAM,eAAA,EAAC,8BAA8B;MAAAmC,QAAA,EAEhBjC,MAAA,CAAAkC;IAAmB;IADtC9B,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEN,MAAA,CAAAmC,iBAAA,IAAAnC,MAAA,CAAAmC,iBAAA,IAAA7B,IAAA,CAAiB;kCAGzBX,mBAAA,CAAiB,cAAX,MAAI,qBACVa,YAAA,CAGUC,kBAAA;IAHDjB,KAAK,EAAC;EAAa;sBAIxC,MACI,CAJ2BQ,MAAA,CAAAkC,mBAAmB,I,cAApCf,YAAA,CAAwCiB,oBAAA;MAAAtB,GAAA;IAAA,O,cACxCK,YAAA,CAAqBkB,qBAAA;MAAAvB,GAAA;IAAA,I;;kCARdd,MAAA,CAAAC,gBAAgB,E,GAY3BJ,mBAAA,aAAgB,E,gBAChBF,mBAAA,CAqDM,OArDN2C,UAqDM,GAnDItC,MAAA,CAAAuC,SAAS,I,cADjBpB,YAAA,CAQcK,sBAAA;;IANZC,EAAE,EAAC,qBAAqB;IACxBjC,KAAK,EAAAM,eAAA,EAAC,aAAa;MAAA4B,MAAA,EACDC,IAAA,CAAAC,MAAM,CAACC,IAAI;IAAA;;sBAE7B,MAA6B,CAA7BrB,YAAA,CAA6BC,kBAAA;wBAApB,MAAU,CAAVD,YAAA,CAAUgC,iBAAA,E;;oCACnB7C,mBAAA,CAAiB,cAAX,MAAI,oB;;;qEAGZE,mBAAA,mBAAsB,EAEdG,MAAA,CAAAyC,SAAS,I,cADjBtB,YAAA,CAQcK,sBAAA;;IANZC,EAAE,EAAC,wBAAwB;IAC3BjC,KAAK,EAAAM,eAAA,EAAC,aAAa;MAAA4B,MAAA,EACDC,IAAA,CAAAC,MAAM,CAACC,IAAI;IAAA;;sBAE7B,MAAmC,CAAnCrB,YAAA,CAAmCC,kBAAA;wBAA1B,MAAgB,CAAhBD,YAAA,CAAgBkC,uBAAA,E;;oCACzB/C,mBAAA,CAAiB,cAAX,MAAI,oB;;;qEAIJK,MAAA,CAAAuC,SAAS,I,cADjBpB,YAAA,CAQcK,sBAAA;;IANZC,EAAE,EAAC,6BAA6B;IAChCjC,KAAK,EAAAM,eAAA,EAAC,aAAa;MAAA4B,MAAA,EACDC,IAAA,CAAAC,MAAM,CAACC,IAAI;IAAA;;sBAE7B,MAAmC,CAAnCrB,YAAA,CAAmCC,kBAAA;wBAA1B,MAAgB,CAAhBD,YAAA,CAAgBkC,uBAAA,E;;oCACzB/C,mBAAA,CAAiB,cAAX,MAAI,oB;;;qEAGZE,mBAAA,YAAe,EACCG,MAAA,CAAAyC,SAAS,I,cACvBtB,YAAA,CAOcK,sBAAA;;IANZC,EAAE,EAAC,4BAA4B;IAC/BjC,KAAK,EAAAM,eAAA,EAAC,aAAa;MAAA4B,MAAA,EACDC,IAAA,CAAAC,MAAM,CAACC,IAAI;IAAA;;sBAE7B,MAA2B,CAA3BrB,YAAA,CAA2BC,kBAAA;wBAAlB,MAAQ,CAARD,YAAA,CAAQmC,eAAA,E;;oCACjBhD,mBAAA,CAAiB,cAAX,MAAI,oB;;;qEAIdE,mBAAA,mBAAsB,EACtBW,YAAA,CAOcgB,sBAAA;IANZC,EAAE,EAAC,8BAA8B;IACjCjC,KAAK,EAAAM,eAAA,EAAC,aAAa;MAAA4B,MAAA,EACDC,IAAA,CAAAC,MAAM,CAACC,IAAI;IAAA;;sBAE7B,MAA2B,CAA3BrB,YAAA,CAA2BC,kBAAA;wBAAlB,MAAQ,CAARD,YAAA,CAAQoC,eAAA,E;;oCACjBjD,mBAAA,CAAiB,cAAX,MAAI,oB;;;mEAnDAK,MAAA,CAAAC,gBAAgB,IAAID,MAAA,CAAAkC,mBAAmB,E,GAuDrDrC,mBAAA,kBAAqB,E,gBACrBF,mBAAA,CAqDM,OArDNkD,WAqDM,GAnDI7C,MAAA,CAAAuC,SAAS,I,cADjBpB,YAAA,CAQcK,sBAAA;;IANZC,EAAE,EAAC,qBAAqB;IACxBjC,KAAK,EAAAM,eAAA,EAAC,UAAU;MAAA4B,MAAA,EACEC,IAAA,CAAAC,MAAM,CAACC,IAAI;IAAA;IAC5Bb,KAAK,EAAE;;sBAER,MAA6B,CAA7BR,YAAA,CAA6BC,kBAAA;wBAApB,MAAU,CAAVD,YAAA,CAAUgC,iBAAA,E;;;;qEAGrB3C,mBAAA,mBAAsB,EAEdG,MAAA,CAAAyC,SAAS,I,cADjBtB,YAAA,CAQcK,sBAAA;;IANZC,EAAE,EAAC,wBAAwB;IAC3BjC,KAAK,EAAAM,eAAA,EAAC,UAAU;MAAA4B,MAAA,EACEC,IAAA,CAAAC,MAAM,CAACC,IAAI;IAAA;IAC5Bb,KAAK,EAAE;;sBAER,MAAmC,CAAnCR,YAAA,CAAmCC,kBAAA;wBAA1B,MAAgB,CAAhBD,YAAA,CAAgBkC,uBAAA,E;;;;qEAInB1C,MAAA,CAAAuC,SAAS,I,cADjBpB,YAAA,CAQcK,sBAAA;;IANZC,EAAE,EAAC,6BAA6B;IAChCjC,KAAK,EAAAM,eAAA,EAAC,UAAU;MAAA4B,MAAA,EACEC,IAAA,CAAAC,MAAM,CAACC,IAAI;IAAA;IAC5Bb,KAAK,EAAE;;sBAER,MAAmC,CAAnCR,YAAA,CAAmCC,kBAAA;wBAA1B,MAAgB,CAAhBD,YAAA,CAAgBkC,uBAAA,E;;;;qEAG3B7C,mBAAA,YAAe,EACCG,MAAA,CAAAyC,SAAS,I,cACvBtB,YAAA,CAOcK,sBAAA;;IANZC,EAAE,EAAC,4BAA4B;IAC/BjC,KAAK,EAAAM,eAAA,EAAC,UAAU;MAAA4B,MAAA,EACEC,IAAA,CAAAC,MAAM,CAACC,IAAI;IAAA;IAC5Bb,KAAK,EAAE;;sBAER,MAA2B,CAA3BR,YAAA,CAA2BC,kBAAA;wBAAlB,MAAQ,CAARD,YAAA,CAAQmC,eAAA,E;;;;qEAIrB9C,mBAAA,mBAAsB,EACtBW,YAAA,CAOcgB,sBAAA;IANZC,EAAE,EAAC,8BAA8B;IACjCjC,KAAK,EAAAM,eAAA,EAAC,UAAU;MAAA4B,MAAA,EACEC,IAAA,CAAAC,MAAM,CAACC,IAAI;IAAA;IAC5Bb,KAAK,EAAE;;sBAER,MAA2B,CAA3BR,YAAA,CAA2BC,kBAAA;wBAAlB,MAAQ,CAARD,YAAA,CAAQoC,eAAA,E;;;;kEAnDR5C,MAAA,CAAAC,gBAAgB,E,KAwD/BJ,mBAAA,UAAa,EACbF,mBAAA,CAyHM,OAzHNmD,WAyHM,G,gBAxHJnD,mBAAA,CAWM;IATJH,KAAK,EAAAM,eAAA,EAAC,8BAA8B;MAAAmC,QAAA,EAEhBjC,MAAA,CAAA+C;IAAgB;IADnC3C,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEN,MAAA,CAAAgD,cAAA,IAAAhD,MAAA,CAAAgD,cAAA,IAAA1C,IAAA,CAAc;kCAGtBX,mBAAA,CAAiB,cAAX,MAAI,qBACVa,YAAA,CAGUC,kBAAA;IAHDjB,KAAK,EAAC;EAAa;sBA2BE,MAEtC,CA5B2BQ,MAAA,CAAA+C,gBAAgB,I,cAAjC5B,YAAA,CAAqCiB,oBAAA;MAAAtB,GAAA;IAAA,O,cACrCK,YAAA,CAAqBkB,qBAAA;MAAAvB,GAAA;IAAA,I;;kCARdd,MAAA,CAAAC,gBAAgB,E,GAY3BJ,mBAAA,aAAgB,E,gBAChBF,mBAAA,CAmDM,OAnDNsD,WAmDM,GAlDJpD,mBAAA,YAAe,EACCG,MAAA,CAAAuC,SAAS,I,cAAzB9C,mBAAA,CA2BW4B,SAAA;IAAAP,GAAA;EAAA,IA1BTN,YAAA,CAOcgB,sBAAA;IANZC,EAAE,EAAC,qBAAqB;IACxBjC,KAAK,EAAAM,eAAA,EAAC,aAAa;MAAA4B,MAAA,EACDC,IAAA,CAAAC,MAAM,CAACC,IAAI;IAAA;;sBAE7B,MAA2B,CAA3BrB,YAAA,CAA2BC,kBAAA;wBAAlB,MAAQ,CAARD,YAAA,CAAQ0C,eAAA,E;;oCACjBvD,mBAAA,CAAiB,cAAX,MAAI,oB;;;gCAGZa,YAAA,CAOcgB,sBAAA;IANZC,EAAE,EAAC,kBAAkB;IACrBjC,KAAK,EAAAM,eAAA,EAAC,aAAa;MAAA4B,MAAA,EACDC,IAAA,CAAAC,MAAM,CAACC,IAAI;IAAA;;sBAE7B,MAAiC,CAAjCrB,YAAA,CAAiCC,kBAAA;wBAAxB,MAAc,CAAdD,YAAA,CAAc2C,qBAAA,E;;oCACvBxD,mBAAA,CAAiB,cAAX,MAAI,oB;;;gCAGZa,YAAA,CAOcgB,sBAAA;IANZC,EAAE,EAAC,yBAAyB;IAC5BjC,KAAK,EAAAM,eAAA,EAAC,aAAa;MAAA4B,MAAA,EACDC,IAAA,CAAAC,MAAM,CAACC,IAAI;IAAA;;sBAE7B,MAA2B,CAA3BrB,YAAA,CAA2BC,kBAAA;wBAAlB,MAAQ,CAARD,YAAA,CAAQmC,eAAA,E;;oCACjBhD,mBAAA,CAAiB,cAAX,MAAI,oB;;;iGAIdE,mBAAA,YAAe,EACCG,MAAA,CAAAyC,SAAS,I,cAAzBhD,mBAAA,CAkBW4B,SAAA;IAAAP,GAAA;EAAA,IAjBTN,YAAA,CAOcgB,sBAAA;IANZC,EAAE,EAAC,kBAAkB;IACrBjC,KAAK,EAAAM,eAAA,EAAC,aAAa;MAAA4B,MAAA,EACDC,IAAA,CAAAC,MAAM,CAACC,IAAI;IAAA;;sBAE7B,MAAiC,CAAjCrB,YAAA,CAAiCC,kBAAA;wBAAxB,MAAc,CAAdD,YAAA,CAAc2C,qBAAA,E;;oCACvBxD,mBAAA,CAAiB,cAAX,MAAI,oB;;;gCAGZa,YAAA,CAOcgB,sBAAA;IANZC,EAAE,EAAC,mBAAmB;IACtBjC,KAAK,EAAAM,eAAA,EAAC,aAAa;MAAA4B,MAAA,EACDC,IAAA,CAAAC,MAAM,CAACC,IAAI;IAAA;;sBAE7B,MAAsC,CAAtCrB,YAAA,CAAsCC,kBAAA;wBAA7B,MAAmB,CAAnBD,YAAA,CAAmB4C,0BAAA,E;;oCAC5BzD,mBAAA,CAAiB,cAAX,MAAI,oB;;;oIAhDFK,MAAA,CAAAC,gBAAgB,IAAID,MAAA,CAAA+C,gBAAgB,E,GAqDlDlD,mBAAA,kBAAqB,E,gBACrBF,mBAAA,CAmDM,OAnDN0D,WAmDM,GAlDJxD,mBAAA,YAAe,EACCG,MAAA,CAAAuC,SAAS,I,cAAzB9C,mBAAA,CA2BW4B,SAAA;IAAAP,GAAA;EAAA,IA1BTN,YAAA,CAOcgB,sBAAA;IANZC,EAAE,EAAC,qBAAqB;IACxBjC,KAAK,EAAAM,eAAA,EAAC,UAAU;MAAA4B,MAAA,EACEC,IAAA,CAAAC,MAAM,CAACC,IAAI;IAAA;IAC5Bb,KAAK,EAAE;;sBAER,MAA2B,CAA3BR,YAAA,CAA2BC,kBAAA;wBAAlB,MAAQ,CAARD,YAAA,CAAQ0C,eAAA,E;;;;gCAGnB1C,YAAA,CAOcgB,sBAAA;IANZC,EAAE,EAAC,kBAAkB;IACrBjC,KAAK,EAAAM,eAAA,EAAC,UAAU;MAAA4B,MAAA,EACEC,IAAA,CAAAC,MAAM,CAACC,IAAI;IAAA;IAC5Bb,KAAK,EAAE;;sBAER,MAAiC,CAAjCR,YAAA,CAAiCC,kBAAA;wBAAxB,MAAc,CAAdD,YAAA,CAAc2C,qBAAA,E;;;;gCAGzB3C,YAAA,CAOcgB,sBAAA;IANZC,EAAE,EAAC,yBAAyB;IAC5BjC,KAAK,EAAAM,eAAA,EAAC,UAAU;MAAA4B,MAAA,EACEC,IAAA,CAAAC,MAAM,CAACC,IAAI;IAAA;IAC5Bb,KAAK,EAAE;;sBAER,MAA2B,CAA3BR,YAAA,CAA2BC,kBAAA;wBAAlB,MAAQ,CAARD,YAAA,CAAQmC,eAAA,E;;;;iGAIrB9C,mBAAA,YAAe,EACCG,MAAA,CAAAyC,SAAS,I,cAAzBhD,mBAAA,CAkBW4B,SAAA;IAAAP,GAAA;EAAA,IAjBTN,YAAA,CAOcgB,sBAAA;IANZC,EAAE,EAAC,kBAAkB;IACrBjC,KAAK,EAAAM,eAAA,EAAC,UAAU;MAAA4B,MAAA,EACEC,IAAA,CAAAC,MAAM,CAACC,IAAI;IAAA;IAC5Bb,KAAK,EAAE;;sBAER,MAAiC,CAAjCR,YAAA,CAAiCC,kBAAA;wBAAxB,MAAc,CAAdD,YAAA,CAAc2C,qBAAA,E;;;;gCAGzB3C,YAAA,CAOcgB,sBAAA;IANZC,EAAE,EAAC,mBAAmB;IACtBjC,KAAK,EAAAM,eAAA,EAAC,UAAU;MAAA4B,MAAA,EACEC,IAAA,CAAAC,MAAM,CAACC,IAAI;IAAA;IAC5Bb,KAAK,EAAE;;sBAER,MAAsC,CAAtCR,YAAA,CAAsCC,kBAAA;wBAA7B,MAAmB,CAAnBD,YAAA,CAAmB4C,0BAAA,E;;;;mIAhDrBpD,MAAA,CAAAC,gBAAgB,E,KAsD/BJ,mBAAA,UAAa,EACbF,mBAAA,CAiJM,OAjJN2D,WAiJM,G,gBAhJJ3D,mBAAA,CAWM;IATJH,KAAK,EAAAM,eAAA,EAAC,8BAA8B;MAAAmC,QAAA,EAEhBjC,MAAA,CAAAuD;IAAyB;IAD5CnD,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEN,MAAA,CAAAwD,uBAAA,IAAAxD,MAAA,CAAAwD,uBAAA,IAAAlD,IAAA,CAAuB;kCAG/BX,mBAAA,CAAiB,cAAX,MAAI,qBACVa,YAAA,CAGUC,kBAAA;IAHDjB,KAAK,EAAC;EAAa;sBAyGtC,MAIG,CA5G0BQ,MAAA,CAAAuD,yBAAyB,I,cAA1CpC,YAAA,CAA8CiB,oBAAA;MAAAtB,GAAA;IAAA,O,cAC9CK,YAAA,CAAqBkB,qBAAA;MAAAvB,GAAA;IAAA,I;;kCARdd,MAAA,CAAAC,gBAAgB,E,GAY3BJ,mBAAA,aAAgB,E,gBAChBF,mBAAA,CA+DM,OA/DN8D,WA+DM,GA9DJjD,YAAA,CAOcgB,sBAAA;IANZC,EAAE,EAAC,qCAAqC;IACxCjC,KAAK,EAAAM,eAAA,EAAC,aAAa;MAAA4B,MAAA,EACDC,IAAA,CAAAC,MAAM,CAACC,IAAI;IAAA;;sBAE7B,MAAoC,CAApCrB,YAAA,CAAoCC,kBAAA;wBAA3B,MAAiB,CAAjBD,YAAA,CAAiBkD,wBAAA,E;;oCAC1B/D,mBAAA,CAAiB,cAAX,MAAI,oB;;;gCAGZa,YAAA,CAOcgB,sBAAA;IANZC,EAAE,EAAC,gCAAgC;IACnCjC,KAAK,EAAAM,eAAA,EAAC,aAAa;MAAA4B,MAAA,EACDC,IAAA,CAAAC,MAAM,CAACC,IAAI;IAAA;;sBAE7B,MAA2B,CAA3BrB,YAAA,CAA2BC,kBAAA;wBAAlB,MAAQ,CAARD,YAAA,CAAQmD,eAAA,E;;oCACjBhE,mBAAA,CAAiB,cAAX,MAAI,oB;;;gCAGZa,YAAA,CAOcgB,sBAAA;IANZC,EAAE,EAAC,gCAAgC;IACnCjC,KAAK,EAAAM,eAAA,EAAC,aAAa;MAAA4B,MAAA,EACDC,IAAA,CAAAC,MAAM,CAACC,IAAI,CAAC+B,QAAQ;IAAA;;sBAEtC,MAAmC,CAAnCpD,YAAA,CAAmCC,kBAAA;wBAA1B,MAAgB,CAAhBD,YAAA,CAAgBkC,uBAAA,E;;oCACzB/C,mBAAA,CAAiB,cAAX,MAAI,oB;;;gCAGZE,mBAAA,YAAe,EAEPG,MAAA,CAAAyC,SAAS,I,cADjBtB,YAAA,CAQcK,sBAAA;;IANZC,EAAE,EAAC,yBAAyB;IAC5BjC,KAAK,EAAAM,eAAA,EAAC,aAAa;MAAA4B,MAAA,EACDC,IAAA,CAAAC,MAAM,CAACC,IAAI;IAAA;;sBAE7B,MAA2B,CAA3BrB,YAAA,CAA2BC,kBAAA;wBAAlB,MAAQ,CAARD,YAAA,CAAQmC,eAAA,E;;oCACjBhD,mBAAA,CAAiB,cAAX,MAAI,oB;;;qEAGZE,mBAAA,YAAe,EAEPG,MAAA,CAAAyC,SAAS,I,cADjBtB,YAAA,CAQcK,sBAAA;;IANZC,EAAE,EAAC,wBAAwB;IAC3BjC,KAAK,EAAAM,eAAA,EAAC,aAAa;MAAA4B,MAAA,EACDC,IAAA,CAAAC,MAAM,CAACC,IAAI;IAAA;;sBAE7B,MAAsC,CAAtCrB,YAAA,CAAsCC,kBAAA;wBAA7B,MAAmB,CAAnBD,YAAA,CAAmB4C,0BAAA,E;;oCAC5BzD,mBAAA,CAAiB,cAAX,MAAI,oB;;;qEASJ,KAAK,I,cAJbwB,YAAA,CAQcK,sBAAA;;IAPZC,EAAE,EAAC,4BAA4B;IAC/BjC,KAAK,EAAAM,eAAA,EAAC,aAAa;MAAA4B,MAAA,EACDC,IAAA,CAAAC,MAAM,CAACC,IAAI;IAAA;;sBAG7B,MAAmC,CAAnCrB,YAAA,CAAmCC,kBAAA;wBAA1B,MAAgB,CAAhBD,YAAA,CAAgBkC,uBAAA,E;;oCACzB/C,mBAAA,CAAiB,cAAX,MAAI,oB;;;wGA3DAK,MAAA,CAAAC,gBAAgB,IAAID,MAAA,CAAAuD,yBAAyB,E,GAiE3D1D,mBAAA,kBAAqB,E,gBACrBF,mBAAA,CA+DM,OA/DNkE,WA+DM,GA9DJrD,YAAA,CAOcgB,sBAAA;IANZC,EAAE,EAAC,qCAAqC;IACxCjC,KAAK,EAAAM,eAAA,EAAC,UAAU;MAAA4B,MAAA,EACEC,IAAA,CAAAC,MAAM,CAACC,IAAI;IAAA;IAC5Bb,KAAK,EAAE;;sBAER,MAAoC,CAApCR,YAAA,CAAoCC,kBAAA;wBAA3B,MAAiB,CAAjBD,YAAA,CAAiBkD,wBAAA,E;;;;gCAG5BlD,YAAA,CAOcgB,sBAAA;IANZC,EAAE,EAAC,gCAAgC;IACnCjC,KAAK,EAAAM,eAAA,EAAC,UAAU;MAAA4B,MAAA,EACEC,IAAA,CAAAC,MAAM,CAACC,IAAI;IAAA;IAC5Bb,KAAK,EAAE;;sBAER,MAA2B,CAA3BR,YAAA,CAA2BC,kBAAA;wBAAlB,MAAQ,CAARD,YAAA,CAAQmD,eAAA,E;;;;gCAGnBnD,YAAA,CAOcgB,sBAAA;IANZC,EAAE,EAAC,gCAAgC;IACnCjC,KAAK,EAAAM,eAAA,EAAC,UAAU;MAAA4B,MAAA,EACEC,IAAA,CAAAC,MAAM,CAACC,IAAI,CAAC+B,QAAQ;IAAA;IACrC5C,KAAK,EAAE;;sBAER,MAAmC,CAAnCR,YAAA,CAAmCC,kBAAA;wBAA1B,MAAgB,CAAhBD,YAAA,CAAgBkC,uBAAA,E;;;;gCAG3B7C,mBAAA,YAAe,EAEPG,MAAA,CAAAyC,SAAS,I,cADjBtB,YAAA,CAQcK,sBAAA;;IANZC,EAAE,EAAC,yBAAyB;IAC5BjC,KAAK,EAAAM,eAAA,EAAC,UAAU;MAAA4B,MAAA,EACEC,IAAA,CAAAC,MAAM,CAACC,IAAI;IAAA;IAC5Bb,KAAK,EAAE;;sBAER,MAA2B,CAA3BR,YAAA,CAA2BC,kBAAA;wBAAlB,MAAQ,CAARD,YAAA,CAAQmC,eAAA,E;;;;qEAGnB9C,mBAAA,YAAe,EAEPG,MAAA,CAAAyC,SAAS,I,cADjBtB,YAAA,CAQcK,sBAAA;;IANZC,EAAE,EAAC,wBAAwB;IAC3BjC,KAAK,EAAAM,eAAA,EAAC,UAAU;MAAA4B,MAAA,EACEC,IAAA,CAAAC,MAAM,CAACC,IAAI;IAAA;IAC5Bb,KAAK,EAAE;;sBAER,MAAsC,CAAtCR,YAAA,CAAsCC,kBAAA;wBAA7B,MAAmB,CAAnBD,YAAA,CAAmB4C,0BAAA,E;;;;qEAUtB,KAAK,I,cALbjC,YAAA,CAQcK,sBAAA;;IAPZC,EAAE,EAAC,4BAA4B;IAC/BjC,KAAK,EAAAM,eAAA,EAAC,UAAU;MAAA4B,MAAA,EACEC,IAAA,CAAAC,MAAM,CAACC,IAAI;IAAA;IAC5Bb,KAAK,EAAE;;sBAGR,MAAmC,CAAnCR,YAAA,CAAmCC,kBAAA;wBAA1B,MAAgB,CAAhBD,YAAA,CAAgBkC,uBAAA,E;;;;uGA3DhB1C,MAAA,CAAAC,gBAAgB,E,KAwE/BJ,mBAAA,UAAa,EACbF,mBAAA,CAqCM,OArCNmE,WAqCM,G,gBApCJnE,mBAAA,CAWM;IATJH,KAAK,EAAAM,eAAA,EAAC,8BAA8B;MAAAmC,QAAA,EAEhBjC,MAAA,CAAA+D;IAAoB;IADvC3D,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEN,MAAA,CAAAgE,kBAAA,IAAAhE,MAAA,CAAAgE,kBAAA,IAAA1D,IAAA,CAAkB;kCAG1BX,mBAAA,CAAiB,cAAX,MAAI,qBACVa,YAAA,CAGUC,kBAAA;IAHDjB,KAAK,EAAC;EAAa;6BACTQ,MAAA,CAAA+D,oBAAoB,I,cAArC5C,YAAA,CAAyCiB,oBAAA;MAAAtB,GAAA;IAAA,O,cACzCK,YAAA,CAAqBkB,qBAAA;MAAAvB,GAAA;IAAA,I;;kCARdd,MAAA,CAAAC,gBAAgB,E,GAY3BJ,mBAAA,aAAgB,E,gBAChBF,mBAAA,CASM,OATNsE,WASM,GARJzD,YAAA,CAOcgB,sBAAA;IANZC,EAAE,EAAC,oBAAoB;IACvBjC,KAAK,EAAAM,eAAA,EAAC,aAAa;MAAA4B,MAAA,EACDC,IAAA,CAAAC,MAAM,CAACC,IAAI;IAAA;;sBAE7B,MAA8B,CAA9BrB,YAAA,CAA8BC,kBAAA;wBAArB,MAAW,CAAXD,YAAA,CAAW0D,kBAAA,E;;oCACpBvE,mBAAA,CAAiB,cAAX,MAAI,oB;;;mEAPAK,MAAA,CAAAC,gBAAgB,IAAID,MAAA,CAAA+D,oBAAoB,E,GAWtDlE,mBAAA,kBAAqB,E,gBACrBF,mBAAA,CASM,OATNwE,WASM,GARJ3D,YAAA,CAOcgB,sBAAA;IANZC,EAAE,EAAC,oBAAoB;IACvBjC,KAAK,EAAAM,eAAA,EAAC,UAAU;MAAA4B,MAAA,EACEC,IAAA,CAAAC,MAAM,CAACC,IAAI;IAAA;IAC5Bb,KAAK,EAAE;;sBAER,MAA8B,CAA9BR,YAAA,CAA8BC,kBAAA;wBAArB,MAAW,CAAXD,YAAA,CAAW0D,kBAAA,E;;;;kEAPXlE,MAAA,CAAAC,gBAAgB,E,4EAerCJ,mBAAA,UAAa,EACbF,mBAAA,CAsDO,QAtDPyE,WAsDO,GArDLvE,mBAAA,WAAc,EACdF,mBAAA,CA8CS,UA9CT0E,WA8CS,GA7CP1E,mBAAA,CA4CM,OA5CN2E,WA4CM,GA3CJ3E,mBAAA,CAOM,OAPN4E,WAOM,GANJ5E,mBAAA,CAGM,OAHN6E,WAGM,GAFJhE,YAAA,CAAuDC,kBAAA;IAA9CjB,KAAK,EAAC;EAAiB;sBAAC,MAAY,CAAZgB,YAAA,CAAYiE,mBAAA,E;;MAC7C9E,mBAAA,CAA0D,QAA1D+E,WAA0D,EAAAC,gBAAA,CAAzB3E,MAAA,CAAA4E,aAAa,mB,GAEhDjF,mBAAA,CAAgD,MAAhDkF,WAAgD,EAAAF,gBAAA,CAAtB3E,MAAA,CAAA8E,YAAY,oBACtCnF,mBAAA,CAAoD,KAApDoF,WAAoD,EAAAJ,gBAAA,CAAxB3E,MAAA,CAAAgF,eAAe,mB,GAG7CrF,mBAAA,CAiCM,OAjCNsF,WAiCM,GAhCJpF,mBAAA,UAAa,EACbA,mBAAA,4BAA+B,EAE/BA,mBAAA,YAAe,EACfF,mBAAA,CA2BM;IA3BDH,KAAK,EAAC,WAAW;IAAEY,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAC,IAAA,KAAEN,MAAA,CAAAkF,kBAAA,IAAAlF,MAAA,CAAAkF,kBAAA,IAAA5E,IAAA,CAAkB;MAC/CX,mBAAA,CAYM,OAZNwF,WAYM,GAXJxF,mBAAA,CAGM,OAHNyF,WAGM,GAFOpF,MAAA,CAAAqF,YAAY,CAACrF,MAAA,CAAAsF,WAAW,EAAEC,MAAM,K,cAA3C9F,mBAAA,CAA0K;;IAA3H+F,GAAG,EAAExF,MAAA,CAAAqF,YAAY,CAACrF,MAAA,CAAAsF,WAAW,EAAEC,MAAM;IAAIE,GAAG,EAAEzF,MAAA,CAAAsF,WAAW,EAAEI,QAAQ;IAAGC,OAAK,EAAAtF,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEN,MAAA,CAAA4F,iBAAA,IAAA5F,MAAA,CAAA4F,iBAAA,IAAAtF,IAAA,CAAiB;IAAGuF,MAAI,EAAAxF,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEN,MAAA,CAAA8F,gBAAA,IAAA9F,MAAA,CAAA8F,gBAAA,IAAAxF,IAAA,CAAgB;0EACtKb,mBAAA,CAA2D,QAAAsG,WAAA,EAAApB,gBAAA,CAA3C3E,MAAA,CAAAgG,UAAU,CAAChG,MAAA,CAAAsF,WAAW,EAAEI,QAAQ,mB,GAElD/F,mBAAA,CAGM,OAHNsG,WAGM,GAFJtG,mBAAA,CAA0D,QAA1DuG,WAA0D,EAAAvB,gBAAA,CAA/B3E,MAAA,CAAAsF,WAAW,EAAEI,QAAQ,kBAChD/F,mBAAA,CAAsD,QAAtDwG,WAAsD,EAAAxB,gBAAA,CAA3B3E,MAAA,CAAAoG,eAAe,mB,GAE5C5F,YAAA,CAEUC,kBAAA;IAFDjB,KAAK,EAAAM,eAAA,EAAC,gBAAgB;MAAAuG,OAAA,EAAoBrG,MAAA,CAAAsG;IAAgB;;sBACjE,MAAa,CAAb9F,YAAA,CAAa4B,oBAAA,E;;kCAIjBvC,mBAAA,UAAa,EACbF,mBAAA,CAUM;IAVDH,KAAK,EAAAM,eAAA,EAAC,kBAAkB;MAAA4B,MAAA,EAAmB1B,MAAA,CAAAsG;IAAgB;MAC9D3G,mBAAA,CAGM;IAHDH,KAAK,EAAC,eAAe;IAAEY,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAkG,cAAA,CAAAC,MAAA,IAAUxG,MAAA,CAAAyG,aAAa;MACtDjG,YAAA,CAA2BC,kBAAA;sBAAlB,MAAQ,CAARD,YAAA,CAAQ0C,eAAA,E;;kCACjBvD,mBAAA,CAAiB,cAAX,MAAI,oB,+BAEZA,mBAAA,CAAoC;IAA/BH,KAAK,EAAC;EAAkB,4BAC7BG,mBAAA,CAGM;IAHDH,KAAK,EAAC,sBAAsB;IAAEY,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAkG,cAAA,CAAAC,MAAA,IAAUxG,MAAA,CAAAyG,aAAa;MAC7DjG,YAAA,CAAmCC,kBAAA;sBAA1B,MAAgB,CAAhBD,YAAA,CAAgBkG,uBAAA,E;;kCACzB/G,mBAAA,CAAiB,cAAX,MAAI,oB,4BAQtBE,mBAAA,UAAa,EACbF,mBAAA,CAEM,OAFNgH,WAEM,GADJnG,YAAA,CAAeoG,sBAAA,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}