{"ast": null, "code": "import { createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, createTextVNode as _createTextVNode, createCommentVNode as _createCommentVNode, withKeys as _withKeys, openBlock as _openBlock, createBlock as _createBlock, toDisplayString as _toDisplayString, createElementBlock as _createElementBlock, resolveDirective as _resolveDirective, withDirectives as _withDirectives, renderList as _renderList, Fragment as _Fragment } from \"vue\";\nconst _hoisted_1 = {\n  class: \"file-manage\"\n};\nconst _hoisted_2 = {\n  class: \"header\"\n};\nconst _hoisted_3 = {\n  class: \"header-actions\"\n};\nconst _hoisted_4 = {\n  class: \"search-bar\"\n};\nconst _hoisted_5 = {\n  class: \"file-list\"\n};\nconst _hoisted_6 = {\n  class: \"file-info\"\n};\nconst _hoisted_7 = {\n  class: \"file-details\"\n};\nconst _hoisted_8 = {\n  class: \"file-name\"\n};\nconst _hoisted_9 = {\n  class: \"file-meta\"\n};\nconst _hoisted_10 = {\n  class: \"file-size\"\n};\nconst _hoisted_11 = {\n  class: \"file-type\"\n};\nconst _hoisted_12 = {\n  key: 3\n};\nconst _hoisted_13 = {\n  key: 0,\n  class: \"batch-actions\"\n};\nconst _hoisted_14 = {\n  key: 0,\n  class: \"pagination\"\n};\nconst _hoisted_15 = {\n  class: \"image-preview\"\n};\nconst _hoisted_16 = [\"src\"];\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_Upload = _resolveComponent(\"Upload\");\n  const _component_el_icon = _resolveComponent(\"el-icon\");\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_Search = _resolveComponent(\"Search\");\n  const _component_el_input = _resolveComponent(\"el-input\");\n  const _component_el_col = _resolveComponent(\"el-col\");\n  const _component_el_option = _resolveComponent(\"el-option\");\n  const _component_el_select = _resolveComponent(\"el-select\");\n  const _component_el_row = _resolveComponent(\"el-row\");\n  const _component_el_table_column = _resolveComponent(\"el-table-column\");\n  const _component_Document = _resolveComponent(\"Document\");\n  const _component_Picture = _resolveComponent(\"Picture\");\n  const _component_VideoPlay = _resolveComponent(\"VideoPlay\");\n  const _component_Headset = _resolveComponent(\"Headset\");\n  const _component_FolderOpened = _resolveComponent(\"FolderOpened\");\n  const _component_EditPen = _resolveComponent(\"EditPen\");\n  const _component_el_tag = _resolveComponent(\"el-tag\");\n  const _component_el_table = _resolveComponent(\"el-table\");\n  const _component_el_pagination = _resolveComponent(\"el-pagination\");\n  const _component_el_card = _resolveComponent(\"el-card\");\n  const _component_el_form_item = _resolveComponent(\"el-form-item\");\n  const _component_upload_filled = _resolveComponent(\"upload-filled\");\n  const _component_el_upload = _resolveComponent(\"el-upload\");\n  const _component_el_form = _resolveComponent(\"el-form\");\n  const _component_el_dialog = _resolveComponent(\"el-dialog\");\n  const _directive_loading = _resolveDirective(\"loading\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_el_card, null, {\n    header: _withCtx(() => [_createElementVNode(\"div\", _hoisted_2, [_cache[19] || (_cache[19] = _createElementVNode(\"h3\", null, \"文件管理\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_3, [_createVNode(_component_el_button, {\n      type: \"primary\",\n      onClick: _cache[0] || (_cache[0] = $event => $setup.showUploadDialog = true)\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [_createVNode(_component_Upload)]),\n        _: 1 /* STABLE */\n      }), _cache[18] || (_cache[18] = _createTextVNode(\" 上传文件 \"))]),\n      _: 1 /* STABLE */,\n      __: [18]\n    })])])]),\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_4, [_createVNode(_component_el_row, {\n      gutter: 20\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_col, {\n        span: 8\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.searchKeyword,\n          \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $setup.searchKeyword = $event),\n          placeholder: \"搜索文件名或描述\",\n          onKeyup: _withKeys($setup.searchFiles, [\"enter\"]),\n          clearable: \"\"\n        }, {\n          append: _withCtx(() => [_createVNode(_component_el_button, {\n            onClick: $setup.searchFiles\n          }, {\n            default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n              default: _withCtx(() => [_createVNode(_component_Search)]),\n              _: 1 /* STABLE */\n            })]),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"onClick\"])]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"modelValue\", \"onKeyup\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_col, {\n        span: 4\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_select, {\n          modelValue: $setup.filterType,\n          \"onUpdate:modelValue\": _cache[2] || (_cache[2] = $event => $setup.filterType = $event),\n          placeholder: \"文件类型\",\n          onChange: $setup.loadFiles,\n          clearable: \"\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_option, {\n            label: \"文档\",\n            value: \"DOCUMENT\"\n          }), _createVNode(_component_el_option, {\n            label: \"图片\",\n            value: \"IMAGE\"\n          }), _createVNode(_component_el_option, {\n            label: \"视频\",\n            value: \"VIDEO\"\n          }), _createVNode(_component_el_option, {\n            label: \"音频\",\n            value: \"AUDIO\"\n          }), _createVNode(_component_el_option, {\n            label: \"压缩包\",\n            value: \"ARCHIVE\"\n          }), _createVNode(_component_el_option, {\n            label: \"代码\",\n            value: \"CODE\"\n          }), _createVNode(_component_el_option, {\n            label: \"其他\",\n            value: \"OTHER\"\n          })]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"modelValue\", \"onChange\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_col, {\n        span: 4\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_select, {\n          modelValue: $setup.filterScope,\n          \"onUpdate:modelValue\": _cache[3] || (_cache[3] = $event => $setup.filterScope = $event),\n          placeholder: \"文件范围\",\n          onChange: $setup.loadFiles,\n          clearable: \"\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_option, {\n            label: \"我的文件\",\n            value: \"my\"\n          }), _createVNode(_component_el_option, {\n            label: \"项目文件\",\n            value: \"project\"\n          }), _createVNode(_component_el_option, {\n            label: \"团队文件\",\n            value: \"team\"\n          })]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"modelValue\", \"onChange\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_col, {\n        span: 4\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_select, {\n          modelValue: $setup.sortBy,\n          \"onUpdate:modelValue\": _cache[4] || (_cache[4] = $event => $setup.sortBy = $event),\n          placeholder: \"排序方式\",\n          onChange: $setup.loadFiles\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_option, {\n            label: \"上传时间\",\n            value: \"uploadTime\"\n          }), _createVNode(_component_el_option, {\n            label: \"文件大小\",\n            value: \"fileSize\"\n          }), _createVNode(_component_el_option, {\n            label: \"下载次数\",\n            value: \"downloadCount\"\n          }), _createVNode(_component_el_option, {\n            label: \"文件名\",\n            value: \"originalName\"\n          })]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"modelValue\", \"onChange\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_col, {\n        span: 4\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_select, {\n          modelValue: $setup.sortDir,\n          \"onUpdate:modelValue\": _cache[5] || (_cache[5] = $event => $setup.sortDir = $event),\n          placeholder: \"排序方向\",\n          onChange: $setup.loadFiles\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_option, {\n            label: \"降序\",\n            value: \"desc\"\n          }), _createVNode(_component_el_option, {\n            label: \"升序\",\n            value: \"asc\"\n          })]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"modelValue\", \"onChange\"])]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    })]), _withDirectives((_openBlock(), _createElementBlock(\"div\", _hoisted_5, [_createVNode(_component_el_table, {\n      data: $setup.files,\n      onSelectionChange: $setup.handleSelectionChange\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_table_column, {\n        type: \"selection\",\n        width: \"55\"\n      }), _createVNode(_component_el_table_column, {\n        label: \"文件名\",\n        \"min-width\": \"200\"\n      }, {\n        default: _withCtx(({\n          row\n        }) => [_createElementVNode(\"div\", _hoisted_6, [_createVNode(_component_el_icon, {\n          class: \"file-icon\"\n        }, {\n          default: _withCtx(() => [row.fileType === 'DOCUMENT' ? (_openBlock(), _createBlock(_component_Document, {\n            key: 0\n          })) : row.fileType === 'IMAGE' ? (_openBlock(), _createBlock(_component_Picture, {\n            key: 1\n          })) : row.fileType === 'VIDEO' ? (_openBlock(), _createBlock(_component_VideoPlay, {\n            key: 2\n          })) : row.fileType === 'AUDIO' ? (_openBlock(), _createBlock(_component_Headset, {\n            key: 3\n          })) : row.fileType === 'ARCHIVE' ? (_openBlock(), _createBlock(_component_FolderOpened, {\n            key: 4\n          })) : row.fileType === 'CODE' ? (_openBlock(), _createBlock(_component_EditPen, {\n            key: 5\n          })) : (_openBlock(), _createBlock(_component_Document, {\n            key: 6\n          }))]),\n          _: 2 /* DYNAMIC */\n        }, 1024 /* DYNAMIC_SLOTS */), _createElementVNode(\"div\", _hoisted_7, [_createElementVNode(\"div\", _hoisted_8, _toDisplayString(row.originalName), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_9, [_createElementVNode(\"span\", _hoisted_10, _toDisplayString(row.readableFileSize), 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_11, _toDisplayString(row.fileTypeDescription), 1 /* TEXT */)])])])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_table_column, {\n        label: \"描述\",\n        prop: \"description\",\n        \"min-width\": \"150\",\n        \"show-overflow-tooltip\": \"\"\n      }), _createVNode(_component_el_table_column, {\n        label: \"上传者\",\n        prop: \"uploaderName\",\n        width: \"100\"\n      }), _createVNode(_component_el_table_column, {\n        label: \"关联\",\n        width: \"120\"\n      }, {\n        default: _withCtx(({\n          row\n        }) => [row.projectName ? (_openBlock(), _createBlock(_component_el_tag, {\n          key: 0,\n          size: \"small\",\n          type: \"primary\"\n        }, {\n          default: _withCtx(() => [_createTextVNode(_toDisplayString(row.projectName), 1 /* TEXT */)]),\n          _: 2 /* DYNAMIC */\n        }, 1024 /* DYNAMIC_SLOTS */)) : row.teamName ? (_openBlock(), _createBlock(_component_el_tag, {\n          key: 1,\n          size: \"small\",\n          type: \"success\"\n        }, {\n          default: _withCtx(() => [_createTextVNode(_toDisplayString(row.teamName), 1 /* TEXT */)]),\n          _: 2 /* DYNAMIC */\n        }, 1024 /* DYNAMIC_SLOTS */)) : row.recordTitle ? (_openBlock(), _createBlock(_component_el_tag, {\n          key: 2,\n          size: \"small\",\n          type: \"info\"\n        }, {\n          default: _withCtx(() => [_createTextVNode(_toDisplayString(row.recordTitle), 1 /* TEXT */)]),\n          _: 2 /* DYNAMIC */\n        }, 1024 /* DYNAMIC_SLOTS */)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_12, \"-\"))]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_table_column, {\n        label: \"下载次数\",\n        prop: \"downloadCount\",\n        width: \"100\"\n      }), _createVNode(_component_el_table_column, {\n        label: \"上传时间\",\n        prop: \"uploadTime\",\n        width: \"160\"\n      }), _createVNode(_component_el_table_column, {\n        label: \"操作\",\n        width: \"200\",\n        fixed: \"right\"\n      }, {\n        default: _withCtx(({\n          row\n        }) => [row.canDownload ? (_openBlock(), _createBlock(_component_el_button, {\n          key: 0,\n          size: \"small\",\n          onClick: $event => $setup.downloadFile(row)\n        }, {\n          default: _withCtx(() => _cache[20] || (_cache[20] = [_createTextVNode(\" 下载 \")])),\n          _: 2 /* DYNAMIC */,\n          __: [20]\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"])) : _createCommentVNode(\"v-if\", true), row.fileType === 'IMAGE' ? (_openBlock(), _createBlock(_component_el_button, {\n          key: 1,\n          size: \"small\",\n          onClick: $event => $setup.previewFile(row)\n        }, {\n          default: _withCtx(() => _cache[21] || (_cache[21] = [_createTextVNode(\" 预览 \")])),\n          _: 2 /* DYNAMIC */,\n          __: [21]\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"])) : _createCommentVNode(\"v-if\", true), row.canEdit ? (_openBlock(), _createBlock(_component_el_button, {\n          key: 2,\n          size: \"small\",\n          onClick: $event => $setup.editFile(row)\n        }, {\n          default: _withCtx(() => _cache[22] || (_cache[22] = [_createTextVNode(\" 编辑 \")])),\n          _: 2 /* DYNAMIC */,\n          __: [22]\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"])) : _createCommentVNode(\"v-if\", true), row.canDelete ? (_openBlock(), _createBlock(_component_el_button, {\n          key: 3,\n          size: \"small\",\n          type: \"danger\",\n          onClick: $event => $setup.deleteFile(row)\n        }, {\n          default: _withCtx(() => _cache[23] || (_cache[23] = [_createTextVNode(\" 删除 \")])),\n          _: 2 /* DYNAMIC */,\n          __: [23]\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"])) : _createCommentVNode(\"v-if\", true)]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"data\", \"onSelectionChange\"]), _createCommentVNode(\" 批量操作 \"), $setup.selectedFiles.length > 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_13, [_createVNode(_component_el_button, {\n      type: \"danger\",\n      onClick: $setup.batchDeleteFiles\n    }, {\n      default: _withCtx(() => [_createTextVNode(\" 批量删除 (\" + _toDisplayString($setup.selectedFiles.length) + \") \", 1 /* TEXT */)]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"onClick\"])])) : _createCommentVNode(\"v-if\", true)])), [[_directive_loading, $setup.loading]]), $setup.total > 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_14, [_createVNode(_component_el_pagination, {\n      \"current-page\": $setup.currentPage,\n      \"onUpdate:currentPage\": _cache[6] || (_cache[6] = $event => $setup.currentPage = $event),\n      \"page-size\": $setup.pageSize,\n      \"onUpdate:pageSize\": _cache[7] || (_cache[7] = $event => $setup.pageSize = $event),\n      total: $setup.total,\n      \"page-sizes\": [10, 20, 50, 100],\n      layout: \"total, sizes, prev, pager, next, jumper\",\n      onSizeChange: $setup.loadFiles,\n      onCurrentChange: $setup.loadFiles\n    }, null, 8 /* PROPS */, [\"current-page\", \"page-size\", \"total\", \"onSizeChange\", \"onCurrentChange\"])])) : _createCommentVNode(\"v-if\", true)]),\n    _: 1 /* STABLE */\n  }), _createCommentVNode(\" 上传文件对话框 \"), _createVNode(_component_el_dialog, {\n    modelValue: $setup.showUploadDialog,\n    \"onUpdate:modelValue\": _cache[13] || (_cache[13] = $event => $setup.showUploadDialog = $event),\n    title: \"上传文件\",\n    width: \"600px\"\n  }, {\n    footer: _withCtx(() => [_createVNode(_component_el_button, {\n      onClick: _cache[12] || (_cache[12] = $event => $setup.showUploadDialog = false)\n    }, {\n      default: _withCtx(() => _cache[26] || (_cache[26] = [_createTextVNode(\"取消\")])),\n      _: 1 /* STABLE */,\n      __: [26]\n    }), _createVNode(_component_el_button, {\n      type: \"primary\",\n      onClick: $setup.uploadFiles,\n      loading: $setup.uploading\n    }, {\n      default: _withCtx(() => _cache[27] || (_cache[27] = [_createTextVNode(\" 上传 \")])),\n      _: 1 /* STABLE */,\n      __: [27]\n    }, 8 /* PROPS */, [\"onClick\", \"loading\"])]),\n    default: _withCtx(() => [_createVNode(_component_el_form, {\n      model: $setup.uploadForm,\n      \"label-width\": \"80px\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_form_item, {\n        label: \"关联项目\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_select, {\n          modelValue: $setup.uploadForm.projectId,\n          \"onUpdate:modelValue\": _cache[8] || (_cache[8] = $event => $setup.uploadForm.projectId = $event),\n          placeholder: \"选择项目\",\n          clearable: \"\"\n        }, {\n          default: _withCtx(() => [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.projects, project => {\n            return _openBlock(), _createBlock(_component_el_option, {\n              key: project.id,\n              label: project.name,\n              value: project.id\n            }, null, 8 /* PROPS */, [\"label\", \"value\"]);\n          }), 128 /* KEYED_FRAGMENT */))]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"关联团队\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_select, {\n          modelValue: $setup.uploadForm.teamId,\n          \"onUpdate:modelValue\": _cache[9] || (_cache[9] = $event => $setup.uploadForm.teamId = $event),\n          placeholder: \"选择团队\",\n          clearable: \"\"\n        }, {\n          default: _withCtx(() => [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.teams, team => {\n            return _openBlock(), _createBlock(_component_el_option, {\n              key: team.id,\n              label: team.name,\n              value: team.id\n            }, null, 8 /* PROPS */, [\"label\", \"value\"]);\n          }), 128 /* KEYED_FRAGMENT */))]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"文件类型\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_select, {\n          modelValue: $setup.uploadForm.fileType,\n          \"onUpdate:modelValue\": _cache[10] || (_cache[10] = $event => $setup.uploadForm.fileType = $event),\n          placeholder: \"选择文件类型\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_option, {\n            label: \"文档\",\n            value: \"DOCUMENT\"\n          }), _createVNode(_component_el_option, {\n            label: \"图片\",\n            value: \"IMAGE\"\n          }), _createVNode(_component_el_option, {\n            label: \"视频\",\n            value: \"VIDEO\"\n          }), _createVNode(_component_el_option, {\n            label: \"音频\",\n            value: \"AUDIO\"\n          }), _createVNode(_component_el_option, {\n            label: \"压缩包\",\n            value: \"ARCHIVE\"\n          }), _createVNode(_component_el_option, {\n            label: \"代码\",\n            value: \"CODE\"\n          }), _createVNode(_component_el_option, {\n            label: \"其他\",\n            value: \"OTHER\"\n          })]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"文件描述\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.uploadForm.description,\n          \"onUpdate:modelValue\": _cache[11] || (_cache[11] = $event => $setup.uploadForm.description = $event),\n          type: \"textarea\",\n          rows: 3,\n          placeholder: \"请输入文件描述\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"选择文件\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_upload, {\n          ref: \"uploadRef\",\n          \"file-list\": $setup.uploadFileList,\n          \"on-change\": $setup.handleUploadChange,\n          \"on-remove\": $setup.handleUploadRemove,\n          \"auto-upload\": false,\n          multiple: \"\",\n          drag: \"\"\n        }, {\n          tip: _withCtx(() => _cache[24] || (_cache[24] = [_createElementVNode(\"div\", {\n            class: \"el-upload__tip\"\n          }, \" 支持多文件上传，单个文件不超过10MB \", -1 /* CACHED */)])),\n          default: _withCtx(() => [_createVNode(_component_el_icon, {\n            class: \"el-icon--upload\"\n          }, {\n            default: _withCtx(() => [_createVNode(_component_upload_filled)]),\n            _: 1 /* STABLE */\n          }), _cache[25] || (_cache[25] = _createElementVNode(\"div\", {\n            class: \"el-upload__text\"\n          }, [_createTextVNode(\" 将文件拖到此处，或\"), _createElementVNode(\"em\", null, \"点击上传\")], -1 /* CACHED */))]),\n          _: 1 /* STABLE */,\n          __: [25]\n        }, 8 /* PROPS */, [\"file-list\", \"on-change\", \"on-remove\"])]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"model\"])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"]), _createCommentVNode(\" 编辑文件对话框 \"), _createVNode(_component_el_dialog, {\n    modelValue: $setup.showEditDialog,\n    \"onUpdate:modelValue\": _cache[16] || (_cache[16] = $event => $setup.showEditDialog = $event),\n    title: \"编辑文件\",\n    width: \"500px\"\n  }, {\n    footer: _withCtx(() => [_createVNode(_component_el_button, {\n      onClick: _cache[15] || (_cache[15] = $event => $setup.showEditDialog = false)\n    }, {\n      default: _withCtx(() => _cache[28] || (_cache[28] = [_createTextVNode(\"取消\")])),\n      _: 1 /* STABLE */,\n      __: [28]\n    }), _createVNode(_component_el_button, {\n      type: \"primary\",\n      onClick: $setup.updateFile,\n      loading: $setup.updating\n    }, {\n      default: _withCtx(() => _cache[29] || (_cache[29] = [_createTextVNode(\" 保存 \")])),\n      _: 1 /* STABLE */,\n      __: [29]\n    }, 8 /* PROPS */, [\"onClick\", \"loading\"])]),\n    default: _withCtx(() => [_createVNode(_component_el_form, {\n      model: $setup.editForm,\n      \"label-width\": \"80px\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_form_item, {\n        label: \"文件名\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          value: $setup.editingFile?.originalName,\n          disabled: \"\"\n        }, null, 8 /* PROPS */, [\"value\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"文件描述\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.editForm.description,\n          \"onUpdate:modelValue\": _cache[14] || (_cache[14] = $event => $setup.editForm.description = $event),\n          type: \"textarea\",\n          rows: 4,\n          placeholder: \"请输入文件描述\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"model\"])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"]), _createCommentVNode(\" 图片预览对话框 \"), _createVNode(_component_el_dialog, {\n    modelValue: $setup.showPreviewDialog,\n    \"onUpdate:modelValue\": _cache[17] || (_cache[17] = $event => $setup.showPreviewDialog = $event),\n    title: \"图片预览\",\n    width: \"80%\"\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_15, [_createElementVNode(\"img\", {\n      src: $setup.previewImageUrl,\n      alt: \"预览图片\",\n      style: {\n        \"max-width\": \"100%\",\n        \"max-height\": \"70vh\"\n      }\n    }, null, 8 /* PROPS */, _hoisted_16)])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_card", "header", "_withCtx", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_component_el_button", "type", "onClick", "_cache", "$event", "$setup", "showUploadDialog", "_component_el_icon", "_component_Upload", "_hoisted_4", "_component_el_row", "gutter", "_component_el_col", "span", "_component_el_input", "searchKeyword", "placeholder", "onKeyup", "_with<PERSON><PERSON><PERSON>", "searchFiles", "clearable", "append", "_component_Search", "_component_el_select", "filterType", "onChange", "loadFiles", "_component_el_option", "label", "value", "filterScope", "sortBy", "sortDir", "_hoisted_5", "_component_el_table", "data", "files", "onSelectionChange", "handleSelectionChange", "_component_el_table_column", "width", "default", "row", "_hoisted_6", "fileType", "_createBlock", "_component_Document", "key", "_component_Picture", "_component_VideoPlay", "_component_Headset", "_component_FolderOpened", "_component_EditPen", "_hoisted_7", "_hoisted_8", "_toDisplayString", "originalName", "_hoisted_9", "_hoisted_10", "readableFileSize", "_hoisted_11", "fileTypeDescription", "prop", "projectName", "_component_el_tag", "size", "teamName", "recordTitle", "_hoisted_12", "fixed", "canDownload", "downloadFile", "previewFile", "canEdit", "editFile", "canDelete", "deleteFile", "_createCommentVNode", "selectedFiles", "length", "_hoisted_13", "batchDeleteFiles", "loading", "total", "_hoisted_14", "_component_el_pagination", "currentPage", "pageSize", "layout", "onSizeChange", "onCurrentChange", "_component_el_dialog", "title", "footer", "uploadFiles", "uploading", "_component_el_form", "model", "uploadForm", "_component_el_form_item", "projectId", "_Fragment", "_renderList", "projects", "project", "id", "name", "teamId", "teams", "team", "description", "rows", "_component_el_upload", "ref", "uploadFileList", "handleUploadChange", "handleUploadRemove", "multiple", "drag", "tip", "_component_upload_filled", "showEditDialog", "updateFile", "updating", "editForm", "editingFile", "disabled", "showPreviewDialog", "_hoisted_15", "src", "previewImageUrl", "alt", "style"], "sources": ["D:\\workspace\\idea\\worker\\work_cli\\src\\views\\files\\FileManageView.vue"], "sourcesContent": ["<template>\n  <div class=\"file-manage\">\n    <el-card>\n      <template #header>\n        <div class=\"header\">\n          <h3>文件管理</h3>\n          <div class=\"header-actions\">\n            <el-button type=\"primary\" @click=\"showUploadDialog = true\">\n              <el-icon><Upload /></el-icon>\n              上传文件\n            </el-button>\n          </div>\n        </div>\n      </template>\n      \n      <!-- 搜索和筛选 -->\n      <div class=\"search-bar\">\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\">\n            <el-input\n              v-model=\"searchKeyword\"\n              placeholder=\"搜索文件名或描述\"\n              @keyup.enter=\"searchFiles\"\n              clearable\n            >\n              <template #append>\n                <el-button @click=\"searchFiles\">\n                  <el-icon><Search /></el-icon>\n                </el-button>\n              </template>\n            </el-input>\n          </el-col>\n          <el-col :span=\"4\">\n            <el-select v-model=\"filterType\" placeholder=\"文件类型\" @change=\"loadFiles\" clearable>\n              <el-option label=\"文档\" value=\"DOCUMENT\" />\n              <el-option label=\"图片\" value=\"IMAGE\" />\n              <el-option label=\"视频\" value=\"VIDEO\" />\n              <el-option label=\"音频\" value=\"AUDIO\" />\n              <el-option label=\"压缩包\" value=\"ARCHIVE\" />\n              <el-option label=\"代码\" value=\"CODE\" />\n              <el-option label=\"其他\" value=\"OTHER\" />\n            </el-select>\n          </el-col>\n          <el-col :span=\"4\">\n            <el-select v-model=\"filterScope\" placeholder=\"文件范围\" @change=\"loadFiles\" clearable>\n              <el-option label=\"我的文件\" value=\"my\" />\n              <el-option label=\"项目文件\" value=\"project\" />\n              <el-option label=\"团队文件\" value=\"team\" />\n            </el-select>\n          </el-col>\n          <el-col :span=\"4\">\n            <el-select v-model=\"sortBy\" placeholder=\"排序方式\" @change=\"loadFiles\">\n              <el-option label=\"上传时间\" value=\"uploadTime\" />\n              <el-option label=\"文件大小\" value=\"fileSize\" />\n              <el-option label=\"下载次数\" value=\"downloadCount\" />\n              <el-option label=\"文件名\" value=\"originalName\" />\n            </el-select>\n          </el-col>\n          <el-col :span=\"4\">\n            <el-select v-model=\"sortDir\" placeholder=\"排序方向\" @change=\"loadFiles\">\n              <el-option label=\"降序\" value=\"desc\" />\n              <el-option label=\"升序\" value=\"asc\" />\n            </el-select>\n          </el-col>\n        </el-row>\n      </div>\n      \n      <!-- 文件列表 -->\n      <div class=\"file-list\" v-loading=\"loading\">\n        <el-table :data=\"files\" @selection-change=\"handleSelectionChange\">\n          <el-table-column type=\"selection\" width=\"55\" />\n          <el-table-column label=\"文件名\" min-width=\"200\">\n            <template #default=\"{ row }\">\n              <div class=\"file-info\">\n                <el-icon class=\"file-icon\">\n                  <Document v-if=\"row.fileType === 'DOCUMENT'\" />\n                  <Picture v-else-if=\"row.fileType === 'IMAGE'\" />\n                  <VideoPlay v-else-if=\"row.fileType === 'VIDEO'\" />\n                  <Headset v-else-if=\"row.fileType === 'AUDIO'\" />\n                  <FolderOpened v-else-if=\"row.fileType === 'ARCHIVE'\" />\n                  <EditPen v-else-if=\"row.fileType === 'CODE'\" />\n                  <Document v-else />\n                </el-icon>\n                <div class=\"file-details\">\n                  <div class=\"file-name\">{{ row.originalName }}</div>\n                  <div class=\"file-meta\">\n                    <span class=\"file-size\">{{ row.readableFileSize }}</span>\n                    <span class=\"file-type\">{{ row.fileTypeDescription }}</span>\n                  </div>\n                </div>\n              </div>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"描述\" prop=\"description\" min-width=\"150\" show-overflow-tooltip />\n          <el-table-column label=\"上传者\" prop=\"uploaderName\" width=\"100\" />\n          <el-table-column label=\"关联\" width=\"120\">\n            <template #default=\"{ row }\">\n              <el-tag v-if=\"row.projectName\" size=\"small\" type=\"primary\">{{ row.projectName }}</el-tag>\n              <el-tag v-else-if=\"row.teamName\" size=\"small\" type=\"success\">{{ row.teamName }}</el-tag>\n              <el-tag v-else-if=\"row.recordTitle\" size=\"small\" type=\"info\">{{ row.recordTitle }}</el-tag>\n              <span v-else>-</span>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"下载次数\" prop=\"downloadCount\" width=\"100\" />\n          <el-table-column label=\"上传时间\" prop=\"uploadTime\" width=\"160\" />\n          <el-table-column label=\"操作\" width=\"200\" fixed=\"right\">\n            <template #default=\"{ row }\">\n              <el-button size=\"small\" @click=\"downloadFile(row)\" v-if=\"row.canDownload\">\n                下载\n              </el-button>\n              <el-button size=\"small\" @click=\"previewFile(row)\" v-if=\"row.fileType === 'IMAGE'\">\n                预览\n              </el-button>\n              <el-button size=\"small\" @click=\"editFile(row)\" v-if=\"row.canEdit\">\n                编辑\n              </el-button>\n              <el-button size=\"small\" type=\"danger\" @click=\"deleteFile(row)\" v-if=\"row.canDelete\">\n                删除\n              </el-button>\n            </template>\n          </el-table-column>\n        </el-table>\n        \n        <!-- 批量操作 -->\n        <div v-if=\"selectedFiles.length > 0\" class=\"batch-actions\">\n          <el-button type=\"danger\" @click=\"batchDeleteFiles\">\n            批量删除 ({{ selectedFiles.length }})\n          </el-button>\n        </div>\n      </div>\n      \n      <!-- 分页 -->\n      <div v-if=\"total > 0\" class=\"pagination\">\n        <el-pagination\n          v-model:current-page=\"currentPage\"\n          v-model:page-size=\"pageSize\"\n          :total=\"total\"\n          :page-sizes=\"[10, 20, 50, 100]\"\n          layout=\"total, sizes, prev, pager, next, jumper\"\n          @size-change=\"loadFiles\"\n          @current-change=\"loadFiles\"\n        />\n      </div>\n    </el-card>\n    \n    <!-- 上传文件对话框 -->\n    <el-dialog v-model=\"showUploadDialog\" title=\"上传文件\" width=\"600px\">\n      <el-form :model=\"uploadForm\" label-width=\"80px\">\n        <el-form-item label=\"关联项目\">\n          <el-select v-model=\"uploadForm.projectId\" placeholder=\"选择项目\" clearable>\n            <el-option\n              v-for=\"project in projects\"\n              :key=\"project.id\"\n              :label=\"project.name\"\n              :value=\"project.id\"\n            />\n          </el-select>\n        </el-form-item>\n        \n        <el-form-item label=\"关联团队\">\n          <el-select v-model=\"uploadForm.teamId\" placeholder=\"选择团队\" clearable>\n            <el-option\n              v-for=\"team in teams\"\n              :key=\"team.id\"\n              :label=\"team.name\"\n              :value=\"team.id\"\n            />\n          </el-select>\n        </el-form-item>\n        \n        <el-form-item label=\"文件类型\">\n          <el-select v-model=\"uploadForm.fileType\" placeholder=\"选择文件类型\">\n            <el-option label=\"文档\" value=\"DOCUMENT\" />\n            <el-option label=\"图片\" value=\"IMAGE\" />\n            <el-option label=\"视频\" value=\"VIDEO\" />\n            <el-option label=\"音频\" value=\"AUDIO\" />\n            <el-option label=\"压缩包\" value=\"ARCHIVE\" />\n            <el-option label=\"代码\" value=\"CODE\" />\n            <el-option label=\"其他\" value=\"OTHER\" />\n          </el-select>\n        </el-form-item>\n        \n        <el-form-item label=\"文件描述\">\n          <el-input\n            v-model=\"uploadForm.description\"\n            type=\"textarea\"\n            :rows=\"3\"\n            placeholder=\"请输入文件描述\"\n          />\n        </el-form-item>\n        \n        <el-form-item label=\"选择文件\">\n          <el-upload\n            ref=\"uploadRef\"\n            :file-list=\"uploadFileList\"\n            :on-change=\"handleUploadChange\"\n            :on-remove=\"handleUploadRemove\"\n            :auto-upload=\"false\"\n            multiple\n            drag\n          >\n            <el-icon class=\"el-icon--upload\"><upload-filled /></el-icon>\n            <div class=\"el-upload__text\">\n              将文件拖到此处，或<em>点击上传</em>\n            </div>\n            <template #tip>\n              <div class=\"el-upload__tip\">\n                支持多文件上传，单个文件不超过10MB\n              </div>\n            </template>\n          </el-upload>\n        </el-form-item>\n      </el-form>\n      \n      <template #footer>\n        <el-button @click=\"showUploadDialog = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"uploadFiles\" :loading=\"uploading\">\n          上传\n        </el-button>\n      </template>\n    </el-dialog>\n    \n    <!-- 编辑文件对话框 -->\n    <el-dialog v-model=\"showEditDialog\" title=\"编辑文件\" width=\"500px\">\n      <el-form :model=\"editForm\" label-width=\"80px\">\n        <el-form-item label=\"文件名\">\n          <el-input :value=\"editingFile?.originalName\" disabled />\n        </el-form-item>\n        \n        <el-form-item label=\"文件描述\">\n          <el-input\n            v-model=\"editForm.description\"\n            type=\"textarea\"\n            :rows=\"4\"\n            placeholder=\"请输入文件描述\"\n          />\n        </el-form-item>\n      </el-form>\n      \n      <template #footer>\n        <el-button @click=\"showEditDialog = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"updateFile\" :loading=\"updating\">\n          保存\n        </el-button>\n      </template>\n    </el-dialog>\n    \n    <!-- 图片预览对话框 -->\n    <el-dialog v-model=\"showPreviewDialog\" title=\"图片预览\" width=\"80%\">\n      <div class=\"image-preview\">\n        <img :src=\"previewImageUrl\" alt=\"预览图片\" style=\"max-width: 100%; max-height: 70vh;\" />\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { ref, reactive, computed, onMounted } from 'vue'\nimport { useStore } from 'vuex'\nimport { fileAPI, projectAPI, teamAPI } from '@/api'\nimport { ElMessage, ElMessageBox } from 'element-plus'\nimport { \n  Upload, Search, Document, Picture, VideoPlay, Headset,\n  FolderOpened, EditPen, UploadFilled \n} from '@element-plus/icons-vue'\n\nexport default {\n  name: 'FileManageView',\n  components: {\n    Upload, Search, Document, Picture, VideoPlay, Headset,\n    FolderOpened, EditPen, UploadFilled\n  },\n  setup() {\n    const store = useStore()\n    \n    // 响应式数据\n    const loading = ref(false)\n    const uploading = ref(false)\n    const updating = ref(false)\n    const showUploadDialog = ref(false)\n    const showEditDialog = ref(false)\n    const showPreviewDialog = ref(false)\n    \n    const files = ref([])\n    const projects = ref([])\n    const teams = ref([])\n    const selectedFiles = ref([])\n    const uploadFileList = ref([])\n    const editingFile = ref(null)\n    const previewImageUrl = ref('')\n    \n    // 搜索和筛选\n    const searchKeyword = ref('')\n    const filterType = ref('')\n    const filterScope = ref('')\n    const sortBy = ref('uploadTime')\n    const sortDir = ref('desc')\n    \n    // 分页\n    const currentPage = ref(1)\n    const pageSize = ref(20)\n    const total = ref(0)\n    \n    // 表单数据\n    const uploadForm = reactive({\n      projectId: null,\n      teamId: null,\n      fileType: 'DOCUMENT',\n      description: ''\n    })\n    \n    const editForm = reactive({\n      description: ''\n    })\n    \n    // 计算属性\n    const currentUser = computed(() => store.state.user)\n    const isTeacher = computed(() => store.getters.isTeacher)\n\n    // 加载文件列表\n    const loadFiles = async () => {\n      try {\n        loading.value = true\n\n        const params = {\n          page: currentPage.value - 1,\n          size: pageSize.value,\n          sortBy: sortBy.value,\n          sortDir: sortDir.value\n        }\n\n        if (searchKeyword.value) {\n          params.keyword = searchKeyword.value\n        }\n        if (filterType.value) {\n          params.fileType = filterType.value\n        }\n\n        let response\n        if (filterScope.value === 'my') {\n          response = await fileAPI.getMyFiles(params)\n        } else if (filterScope.value === 'project') {\n          // 如果选择了项目范围但没有指定项目ID，则获取所有文件\n          if (uploadForm.projectId) {\n            response = await fileAPI.getProjectFiles(uploadForm.projectId, params)\n          } else {\n            response = await fileAPI.getAllFiles(params)\n          }\n        } else if (filterScope.value === 'team') {\n          // 如果选择了团队范围但没有指定团队ID，则获取所有文件\n          if (uploadForm.teamId) {\n            response = await fileAPI.getTeamFiles(uploadForm.teamId, params)\n          } else {\n            response = await fileAPI.getAllFiles(params)\n          }\n        } else {\n          response = await fileAPI.getAllFiles(params)\n        }\n\n        // 响应拦截器已经提取了 result.data，直接使用 response.records\n        files.value = response?.records || []\n        total.value = response?.total || 0\n      } catch (error) {\n        console.error('加载文件列表失败:', error)\n        ElMessage.error('加载文件列表失败')\n      } finally {\n        loading.value = false\n      }\n    }\n\n    // 搜索文件\n    const searchFiles = () => {\n      currentPage.value = 1\n      loadFiles()\n    }\n\n    // 加载项目列表\n    const loadProjects = async () => {\n      try {\n        const response = await projectAPI.getProjects()\n        console.log('Projects response:', response)\n        // 响应拦截器已经提取了 result.data，直接使用 response.records\n        projects.value = response?.records || []\n        console.log('Projects:', projects.value)\n      } catch (error) {\n        console.error('加载项目列表失败:', error)\n      }\n    }\n\n    // 加载团队列表\n    const loadTeams = async () => {\n      try {\n        const response = await teamAPI.getMyTeams()\n        console.log('Teams response:', response)\n        // 响应拦截器已经提取了 result.data，直接使用 response.records\n        teams.value = response?.records || []\n        console.log('Teams:', teams.value)\n      } catch (error) {\n        console.error('加载团队列表失败:', error)\n      }\n    }\n\n    // 文件选择处理\n    const handleSelectionChange = (selection) => {\n      selectedFiles.value = selection\n    }\n\n    // 上传文件变化处理\n    const handleUploadChange = (file, fileList) => {\n      uploadFileList.value = fileList\n    }\n\n    // 移除上传文件\n    const handleUploadRemove = (file, fileList) => {\n      uploadFileList.value = fileList\n    }\n\n    // 上传文件\n    const uploadFiles = async () => {\n      if (uploadFileList.value.length === 0) {\n        ElMessage.warning('请选择要上传的文件')\n        return\n      }\n\n      try {\n        uploading.value = true\n\n        for (const file of uploadFileList.value) {\n          if (file.raw) {\n            await fileAPI.uploadFile(file.raw, {\n              projectId: uploadForm.projectId,\n              teamId: uploadForm.teamId,\n              fileType: uploadForm.fileType,\n              description: uploadForm.description\n            })\n          }\n        }\n\n        ElMessage.success('文件上传成功')\n        showUploadDialog.value = false\n        resetUploadForm()\n        loadFiles()\n      } catch (error) {\n        console.error('文件上传失败:', error)\n        ElMessage.error('文件上传失败')\n      } finally {\n        uploading.value = false\n      }\n    }\n\n    // 重置上传表单\n    const resetUploadForm = () => {\n      Object.assign(uploadForm, {\n        projectId: null,\n        teamId: null,\n        fileType: 'DOCUMENT',\n        description: ''\n      })\n      uploadFileList.value = []\n    }\n\n    // 下载文件\n    const downloadFile = async (file) => {\n      try {\n        const response = await fileAPI.downloadFile(file.id)\n\n        // 创建下载链接\n        const url = window.URL.createObjectURL(new Blob([response.data]))\n        const link = document.createElement('a')\n        link.href = url\n        link.setAttribute('download', file.originalName)\n        document.body.appendChild(link)\n        link.click()\n\n        // 清理\n        window.URL.revokeObjectURL(url)\n        document.body.removeChild(link)\n\n        ElMessage.success('文件下载成功')\n      } catch (error) {\n        console.error('文件下载失败:', error)\n        ElMessage.error('文件下载失败')\n      }\n    }\n\n    // 预览文件\n    const previewFile = async (file) => {\n      try {\n        const response = await fileAPI.previewFile(file.id)\n        previewImageUrl.value = window.URL.createObjectURL(new Blob([response.data]))\n        showPreviewDialog.value = true\n      } catch (error) {\n        console.error('文件预览失败:', error)\n        ElMessage.error('文件预览失败')\n      }\n    }\n\n    // 编辑文件\n    const editFile = (file) => {\n      editingFile.value = file\n      editForm.description = file.description || ''\n      showEditDialog.value = true\n    }\n\n    // 更新文件信息\n    const updateFile = async () => {\n      try {\n        updating.value = true\n\n        await fileAPI.updateFileInfo(editingFile.value.id, {\n          description: editForm.description\n        })\n\n        ElMessage.success('文件信息更新成功')\n        showEditDialog.value = false\n        loadFiles()\n      } catch (error) {\n        console.error('更新文件信息失败:', error)\n        ElMessage.error('更新文件信息失败')\n      } finally {\n        updating.value = false\n      }\n    }\n\n    // 删除文件\n    const deleteFile = async (file) => {\n      try {\n        await ElMessageBox.confirm(\n          `确定要删除文件 \"${file.originalName}\" 吗？`,\n          '确认删除',\n          {\n            confirmButtonText: '确定',\n            cancelButtonText: '取消',\n            type: 'warning'\n          }\n        )\n\n        await fileAPI.deleteFile(file.id)\n        ElMessage.success('文件删除成功')\n        loadFiles()\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('删除文件失败:', error)\n          ElMessage.error('删除文件失败')\n        }\n      }\n    }\n\n    // 批量删除文件\n    const batchDeleteFiles = async () => {\n      try {\n        await ElMessageBox.confirm(\n          `确定要删除选中的 ${selectedFiles.value.length} 个文件吗？`,\n          '确认批量删除',\n          {\n            confirmButtonText: '确定',\n            cancelButtonText: '取消',\n            type: 'warning'\n          }\n        )\n\n        const fileIds = selectedFiles.value.map(file => file.id)\n        await fileAPI.batchDeleteFiles(fileIds)\n\n        ElMessage.success('批量删除成功')\n        selectedFiles.value = []\n        loadFiles()\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('批量删除失败:', error)\n          ElMessage.error('批量删除失败')\n        }\n      }\n    }\n\n    // 初始化\n    onMounted(() => {\n      loadFiles()\n      loadProjects()\n      loadTeams()\n    })\n\n    return {\n      loading, uploading, updating, showUploadDialog, showEditDialog, showPreviewDialog,\n      files, projects, teams, selectedFiles, uploadFileList, editingFile, previewImageUrl,\n      searchKeyword, filterType, filterScope, sortBy, sortDir,\n      currentPage, pageSize, total,\n      uploadForm, editForm,\n      currentUser, isTeacher,\n      loadFiles, searchFiles, loadProjects, loadTeams,\n      handleSelectionChange, handleUploadChange, handleUploadRemove,\n      uploadFiles, resetUploadForm,\n      downloadFile, previewFile, editFile, updateFile, deleteFile, batchDeleteFiles\n    }\n  }\n}\n</script>\n\n<style scoped>\n.file-manage {\n  padding: 20px;\n}\n\n.header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.search-bar {\n  margin-bottom: 20px;\n}\n\n.file-info {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n}\n\n.file-icon {\n  font-size: 24px;\n  color: #409eff;\n}\n\n.file-details {\n  flex: 1;\n}\n\n.file-name {\n  font-weight: 500;\n  margin-bottom: 4px;\n}\n\n.file-meta {\n  font-size: 12px;\n  color: #999;\n  display: flex;\n  gap: 10px;\n}\n\n.batch-actions {\n  margin-top: 10px;\n  padding: 10px;\n  background: #f5f7fa;\n  border-radius: 4px;\n}\n\n.pagination {\n  margin-top: var(--space-6);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  padding: var(--space-4) 0;\n}\n\n.image-preview {\n  text-align: center;\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAa;;EAGbA,KAAK,EAAC;AAAQ;;EAEZA,KAAK,EAAC;AAAgB;;EAU1BA,KAAK,EAAC;AAAY;;EAoDlBA,KAAK,EAAC;AAAW;;EAKTA,KAAK,EAAC;AAAW;;EAUfA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAW;;EACjBA,KAAK,EAAC;AAAW;;EACdA,KAAK,EAAC;AAAW;;EACjBA,KAAK,EAAC;AAAW;;;;;;EAqCEA,KAAK,EAAC;;;;EAQvBA,KAAK,EAAC;;;EAqHvBA,KAAK,EAAC;AAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uBAxP9BC,mBAAA,CA4PM,OA5PNC,UA4PM,GA3PJC,YAAA,CA6IUC,kBAAA;IA5IGC,MAAM,EAAAC,QAAA,CACf,MAQM,CARNC,mBAAA,CAQM,OARNC,UAQM,G,4BAPJD,mBAAA,CAAa,YAAT,MAAI,qBACRA,mBAAA,CAKM,OALNE,UAKM,GAJJN,YAAA,CAGYO,oBAAA;MAHDC,IAAI,EAAC,SAAS;MAAEC,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEC,MAAA,CAAAC,gBAAgB;;wBAChD,MAA6B,CAA7Bb,YAAA,CAA6Bc,kBAAA;0BAApB,MAAU,CAAVd,YAAA,CAAUe,iBAAA,E;;uDAAU,QAE/B,G;;;;sBAMN,MAiDM,CAjDNX,mBAAA,CAiDM,OAjDNY,UAiDM,GAhDJhB,YAAA,CA+CSiB,iBAAA;MA/CAC,MAAM,EAAE;IAAE;wBACjB,MAaS,CAbTlB,YAAA,CAaSmB,iBAAA;QAbAC,IAAI,EAAE;MAAC;0BACd,MAWW,CAXXpB,YAAA,CAWWqB,mBAAA;sBAVAT,MAAA,CAAAU,aAAa;qEAAbV,MAAA,CAAAU,aAAa,GAAAX,MAAA;UACtBY,WAAW,EAAC,UAAU;UACrBC,OAAK,EAAAC,SAAA,CAAQb,MAAA,CAAAc,WAAW;UACzBC,SAAS,EAAT;;UAEWC,MAAM,EAAAzB,QAAA,CACf,MAEY,CAFZH,YAAA,CAEYO,oBAAA;YAFAE,OAAK,EAAEG,MAAA,CAAAc;UAAW;8BAC5B,MAA6B,CAA7B1B,YAAA,CAA6Bc,kBAAA;gCAApB,MAAU,CAAVd,YAAA,CAAU6B,iBAAA,E;;;;;;;;UAK3B7B,YAAA,CAUSmB,iBAAA;QAVAC,IAAI,EAAE;MAAC;0BACd,MAQY,CARZpB,YAAA,CAQY8B,oBAAA;sBARQlB,MAAA,CAAAmB,UAAU;qEAAVnB,MAAA,CAAAmB,UAAU,GAAApB,MAAA;UAAEY,WAAW,EAAC,MAAM;UAAES,QAAM,EAAEpB,MAAA,CAAAqB,SAAS;UAAEN,SAAS,EAAT;;4BACrE,MAAyC,CAAzC3B,YAAA,CAAyCkC,oBAAA;YAA9BC,KAAK,EAAC,IAAI;YAACC,KAAK,EAAC;cAC5BpC,YAAA,CAAsCkC,oBAAA;YAA3BC,KAAK,EAAC,IAAI;YAACC,KAAK,EAAC;cAC5BpC,YAAA,CAAsCkC,oBAAA;YAA3BC,KAAK,EAAC,IAAI;YAACC,KAAK,EAAC;cAC5BpC,YAAA,CAAsCkC,oBAAA;YAA3BC,KAAK,EAAC,IAAI;YAACC,KAAK,EAAC;cAC5BpC,YAAA,CAAyCkC,oBAAA;YAA9BC,KAAK,EAAC,KAAK;YAACC,KAAK,EAAC;cAC7BpC,YAAA,CAAqCkC,oBAAA;YAA1BC,KAAK,EAAC,IAAI;YAACC,KAAK,EAAC;cAC5BpC,YAAA,CAAsCkC,oBAAA;YAA3BC,KAAK,EAAC,IAAI;YAACC,KAAK,EAAC;;;;;UAGhCpC,YAAA,CAMSmB,iBAAA;QANAC,IAAI,EAAE;MAAC;0BACd,MAIY,CAJZpB,YAAA,CAIY8B,oBAAA;sBAJQlB,MAAA,CAAAyB,WAAW;qEAAXzB,MAAA,CAAAyB,WAAW,GAAA1B,MAAA;UAAEY,WAAW,EAAC,MAAM;UAAES,QAAM,EAAEpB,MAAA,CAAAqB,SAAS;UAAEN,SAAS,EAAT;;4BACtE,MAAqC,CAArC3B,YAAA,CAAqCkC,oBAAA;YAA1BC,KAAK,EAAC,MAAM;YAACC,KAAK,EAAC;cAC9BpC,YAAA,CAA0CkC,oBAAA;YAA/BC,KAAK,EAAC,MAAM;YAACC,KAAK,EAAC;cAC9BpC,YAAA,CAAuCkC,oBAAA;YAA5BC,KAAK,EAAC,MAAM;YAACC,KAAK,EAAC;;;;;UAGlCpC,YAAA,CAOSmB,iBAAA;QAPAC,IAAI,EAAE;MAAC;0BACd,MAKY,CALZpB,YAAA,CAKY8B,oBAAA;sBALQlB,MAAA,CAAA0B,MAAM;qEAAN1B,MAAA,CAAA0B,MAAM,GAAA3B,MAAA;UAAEY,WAAW,EAAC,MAAM;UAAES,QAAM,EAAEpB,MAAA,CAAAqB;;4BACtD,MAA6C,CAA7CjC,YAAA,CAA6CkC,oBAAA;YAAlCC,KAAK,EAAC,MAAM;YAACC,KAAK,EAAC;cAC9BpC,YAAA,CAA2CkC,oBAAA;YAAhCC,KAAK,EAAC,MAAM;YAACC,KAAK,EAAC;cAC9BpC,YAAA,CAAgDkC,oBAAA;YAArCC,KAAK,EAAC,MAAM;YAACC,KAAK,EAAC;cAC9BpC,YAAA,CAA8CkC,oBAAA;YAAnCC,KAAK,EAAC,KAAK;YAACC,KAAK,EAAC;;;;;UAGjCpC,YAAA,CAKSmB,iBAAA;QALAC,IAAI,EAAE;MAAC;0BACd,MAGY,CAHZpB,YAAA,CAGY8B,oBAAA;sBAHQlB,MAAA,CAAA2B,OAAO;qEAAP3B,MAAA,CAAA2B,OAAO,GAAA5B,MAAA;UAAEY,WAAW,EAAC,MAAM;UAAES,QAAM,EAAEpB,MAAA,CAAAqB;;4BACvD,MAAqC,CAArCjC,YAAA,CAAqCkC,oBAAA;YAA1BC,KAAK,EAAC,IAAI;YAACC,KAAK,EAAC;cAC5BpC,YAAA,CAAoCkC,oBAAA;YAAzBC,KAAK,EAAC,IAAI;YAACC,KAAK,EAAC;;;;;;;yCAOpCtC,mBAAA,CA6DM,OA7DN0C,UA6DM,GA5DJxC,YAAA,CAoDWyC,mBAAA;MApDAC,IAAI,EAAE9B,MAAA,CAAA+B,KAAK;MAAGC,iBAAgB,EAAEhC,MAAA,CAAAiC;;wBACzC,MAA+C,CAA/C7C,YAAA,CAA+C8C,0BAAA;QAA9BtC,IAAI,EAAC,WAAW;QAACuC,KAAK,EAAC;UACxC/C,YAAA,CAqBkB8C,0BAAA;QArBDX,KAAK,EAAC,KAAK;QAAC,WAAS,EAAC;;QAC1Ba,OAAO,EAAA7C,QAAA,CAChB,CAiBM;UAlBc8C;QAAG,OACvB7C,mBAAA,CAiBM,OAjBN8C,UAiBM,GAhBJlD,YAAA,CAQUc,kBAAA;UARDjB,KAAK,EAAC;QAAW;4BAYf,MAEnB,CAb0BoD,GAAG,CAACE,QAAQ,mB,cAA5BC,YAAA,CAA+CC,mBAAA;YAAAC,GAAA;UAAA,MAC3BL,GAAG,CAACE,QAAQ,gB,cAAhCC,YAAA,CAAgDG,kBAAA;YAAAD,GAAA;UAAA,MAC1BL,GAAG,CAACE,QAAQ,gB,cAAlCC,YAAA,CAAkDI,oBAAA;YAAAF,GAAA;UAAA,MAC9BL,GAAG,CAACE,QAAQ,gB,cAAhCC,YAAA,CAAgDK,kBAAA;YAAAH,GAAA;UAAA,MACvBL,GAAG,CAACE,QAAQ,kB,cAArCC,YAAA,CAAuDM,uBAAA;YAAAJ,GAAA;UAAA,MACnCL,GAAG,CAACE,QAAQ,e,cAAhCC,YAAA,CAA+CO,kBAAA;YAAAL,GAAA;UAAA,O,cAC/CF,YAAA,CAAmBC,mBAAA;YAAAC,GAAA;UAAA,I;;sCAErBlD,mBAAA,CAMM,OANNwD,UAMM,GALJxD,mBAAA,CAAmD,OAAnDyD,UAAmD,EAAAC,gBAAA,CAAzBb,GAAG,CAACc,YAAY,kBAC1C3D,mBAAA,CAGM,OAHN4D,UAGM,GAFJ5D,mBAAA,CAAyD,QAAzD6D,WAAyD,EAAAH,gBAAA,CAA9Bb,GAAG,CAACiB,gBAAgB,kBAC/C9D,mBAAA,CAA4D,QAA5D+D,WAA4D,EAAAL,gBAAA,CAAjCb,GAAG,CAACmB,mBAAmB,iB;;UAM5DpE,YAAA,CAAuF8C,0BAAA;QAAtEX,KAAK,EAAC,IAAI;QAACkC,IAAI,EAAC,aAAa;QAAC,WAAS,EAAC,KAAK;QAAC,uBAAqB,EAArB;UAC/DrE,YAAA,CAA+D8C,0BAAA;QAA9CX,KAAK,EAAC,KAAK;QAACkC,IAAI,EAAC,cAAc;QAACtB,KAAK,EAAC;UACvD/C,YAAA,CAOkB8C,0BAAA;QAPDX,KAAK,EAAC,IAAI;QAACY,KAAK,EAAC;;QACrBC,OAAO,EAAA7C,QAAA,CA0BR,CAIE;UA9BU8C;QAAG,OACTA,GAAG,CAACqB,WAAW,I,cAA7BlB,YAAA,CAAyFmB,iBAAA;;UAA1DC,IAAI,EAAC,OAAO;UAAChE,IAAI,EAAC;;4BAAU,MAAqB,C,kCAAlByC,GAAG,CAACqB,WAAW,iB;;wCAC1DrB,GAAG,CAACwB,QAAQ,I,cAA/BrB,YAAA,CAAwFmB,iBAAA;;UAAvDC,IAAI,EAAC,OAAO;UAAChE,IAAI,EAAC;;4BAAU,MAAkB,C,kCAAfyC,GAAG,CAACwB,QAAQ,iB;;wCACzDxB,GAAG,CAACyB,WAAW,I,cAAlCtB,YAAA,CAA2FmB,iBAAA;;UAAvDC,IAAI,EAAC,OAAO;UAAChE,IAAI,EAAC;;4BAAO,MAAqB,C,kCAAlByC,GAAG,CAACyB,WAAW,iB;;uDAC/E5E,mBAAA,CAAqB,QAAA6E,WAAA,EAAR,GAAC,G;;UAGlB3E,YAAA,CAAiE8C,0BAAA;QAAhDX,KAAK,EAAC,MAAM;QAACkC,IAAI,EAAC,eAAe;QAACtB,KAAK,EAAC;UACzD/C,YAAA,CAA8D8C,0BAAA;QAA7CX,KAAK,EAAC,MAAM;QAACkC,IAAI,EAAC,YAAY;QAACtB,KAAK,EAAC;UACtD/C,YAAA,CAekB8C,0BAAA;QAfDX,KAAK,EAAC,IAAI;QAACY,KAAK,EAAC,KAAK;QAAC6B,KAAK,EAAC;;QACjC5B,OAAO,EAAA7C,QAAA,CAoCzB,CAIiC;UAxCJ8C;QAAG,OACkCA,GAAG,CAAC4B,WAAW,I,cAAxEzB,YAAA,CAEY7C,oBAAA;;UAFDiE,IAAI,EAAC,OAAO;UAAE/D,OAAK,EAAAE,MAAA,IAAEC,MAAA,CAAAkE,YAAY,CAAC7B,GAAG;;4BAA0B,MAE1EvC,MAAA,SAAAA,MAAA,Q,iBAF0E,MAE1E,E;;;+FACwDuC,GAAG,CAACE,QAAQ,gB,cAApEC,YAAA,CAEY7C,oBAAA;;UAFDiE,IAAI,EAAC,OAAO;UAAE/D,OAAK,EAAAE,MAAA,IAAEC,MAAA,CAAAmE,WAAW,CAAC9B,GAAG;;4BAAmC,MAElFvC,MAAA,SAAAA,MAAA,Q,iBAFkF,MAElF,E;;;+FACqDuC,GAAG,CAAC+B,OAAO,I,cAAhE5B,YAAA,CAEY7C,oBAAA;;UAFDiE,IAAI,EAAC,OAAO;UAAE/D,OAAK,EAAAE,MAAA,IAAEC,MAAA,CAAAqE,QAAQ,CAAChC,GAAG;;4BAAsB,MAElEvC,MAAA,SAAAA,MAAA,Q,iBAFkE,MAElE,E;;;+FACqEuC,GAAG,CAACiC,SAAS,I,cAAlF9B,YAAA,CAEY7C,oBAAA;;UAFDiE,IAAI,EAAC,OAAO;UAAChE,IAAI,EAAC,QAAQ;UAAEC,OAAK,EAAAE,MAAA,IAAEC,MAAA,CAAAuE,UAAU,CAAClC,GAAG;;4BAAwB,MAEpFvC,MAAA,SAAAA,MAAA,Q,iBAFoF,MAEpF,E;;;;;;;sDAKN0E,mBAAA,UAAa,EACFxE,MAAA,CAAAyE,aAAa,CAACC,MAAM,Q,cAA/BxF,mBAAA,CAIM,OAJNyF,WAIM,GAHJvF,YAAA,CAEYO,oBAAA;MAFDC,IAAI,EAAC,QAAQ;MAAEC,OAAK,EAAEG,MAAA,CAAA4E;;wBAAkB,MAC3C,C,iBAD2C,SAC3C,GAAA1B,gBAAA,CAAGlD,MAAA,CAAAyE,aAAa,CAACC,MAAM,IAAG,IAClC,gB;;oGA3D8B1E,MAAA,CAAA6E,OAAO,E,GAgE9B7E,MAAA,CAAA8E,KAAK,Q,cAAhB5F,mBAAA,CAUM,OAVN6F,WAUM,GATJ3F,YAAA,CAQE4F,wBAAA;MAPQ,cAAY,EAAEhF,MAAA,CAAAiF,WAAW;kEAAXjF,MAAA,CAAAiF,WAAW,GAAAlF,MAAA;MACzB,WAAS,EAAEC,MAAA,CAAAkF,QAAQ;+DAARlF,MAAA,CAAAkF,QAAQ,GAAAnF,MAAA;MAC1B+E,KAAK,EAAE9E,MAAA,CAAA8E,KAAK;MACZ,YAAU,EAAE,iBAAiB;MAC9BK,MAAM,EAAC,yCAAyC;MAC/CC,YAAW,EAAEpF,MAAA,CAAAqB,SAAS;MACtBgE,eAAc,EAAErF,MAAA,CAAAqB;;;MAKvBmD,mBAAA,aAAgB,EAChBpF,YAAA,CA0EYkG,oBAAA;gBA1EQtF,MAAA,CAAAC,gBAAgB;iEAAhBD,MAAA,CAAAC,gBAAgB,GAAAF,MAAA;IAAEwF,KAAK,EAAC,MAAM;IAACpD,KAAK,EAAC;;IAoE5CqD,MAAM,EAAAjG,QAAA,CACf,MAA2D,CAA3DH,YAAA,CAA2DO,oBAAA;MAA/CE,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAAEC,MAAA,CAAAC,gBAAgB;;wBAAU,MAAEH,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;QAC/CV,YAAA,CAEYO,oBAAA;MAFDC,IAAI,EAAC,SAAS;MAAEC,OAAK,EAAEG,MAAA,CAAAyF,WAAW;MAAGZ,OAAO,EAAE7E,MAAA,CAAA0F;;wBAAW,MAEpE5F,MAAA,SAAAA,MAAA,Q,iBAFoE,MAEpE,E;;;;sBAvEF,MAiEU,CAjEVV,YAAA,CAiEUuG,kBAAA;MAjEAC,KAAK,EAAE5F,MAAA,CAAA6F,UAAU;MAAE,aAAW,EAAC;;wBACvC,MASe,CATfzG,YAAA,CASe0G,uBAAA;QATDvE,KAAK,EAAC;MAAM;0BACxB,MAOY,CAPZnC,YAAA,CAOY8B,oBAAA;sBAPQlB,MAAA,CAAA6F,UAAU,CAACE,SAAS;qEAApB/F,MAAA,CAAA6F,UAAU,CAACE,SAAS,GAAAhG,MAAA;UAAEY,WAAW,EAAC,MAAM;UAACI,SAAS,EAAT;;4BAEzD,MAA2B,E,kBAD7B7B,mBAAA,CAKE8G,SAAA,QAAAC,WAAA,CAJkBjG,MAAA,CAAAkG,QAAQ,EAAnBC,OAAO;iCADhB3D,YAAA,CAKElB,oBAAA;cAHCoB,GAAG,EAAEyD,OAAO,CAACC,EAAE;cACf7E,KAAK,EAAE4E,OAAO,CAACE,IAAI;cACnB7E,KAAK,EAAE2E,OAAO,CAACC;;;;;;UAKtBhH,YAAA,CASe0G,uBAAA;QATDvE,KAAK,EAAC;MAAM;0BACxB,MAOY,CAPZnC,YAAA,CAOY8B,oBAAA;sBAPQlB,MAAA,CAAA6F,UAAU,CAACS,MAAM;qEAAjBtG,MAAA,CAAA6F,UAAU,CAACS,MAAM,GAAAvG,MAAA;UAAEY,WAAW,EAAC,MAAM;UAACI,SAAS,EAAT;;4BAEtD,MAAqB,E,kBADvB7B,mBAAA,CAKE8G,SAAA,QAAAC,WAAA,CAJejG,MAAA,CAAAuG,KAAK,EAAbC,IAAI;iCADbhE,YAAA,CAKElB,oBAAA;cAHCoB,GAAG,EAAE8D,IAAI,CAACJ,EAAE;cACZ7E,KAAK,EAAEiF,IAAI,CAACH,IAAI;cAChB7E,KAAK,EAAEgF,IAAI,CAACJ;;;;;;UAKnBhH,YAAA,CAUe0G,uBAAA;QAVDvE,KAAK,EAAC;MAAM;0BACxB,MAQY,CARZnC,YAAA,CAQY8B,oBAAA;sBARQlB,MAAA,CAAA6F,UAAU,CAACtD,QAAQ;uEAAnBvC,MAAA,CAAA6F,UAAU,CAACtD,QAAQ,GAAAxC,MAAA;UAAEY,WAAW,EAAC;;4BACnD,MAAyC,CAAzCvB,YAAA,CAAyCkC,oBAAA;YAA9BC,KAAK,EAAC,IAAI;YAACC,KAAK,EAAC;cAC5BpC,YAAA,CAAsCkC,oBAAA;YAA3BC,KAAK,EAAC,IAAI;YAACC,KAAK,EAAC;cAC5BpC,YAAA,CAAsCkC,oBAAA;YAA3BC,KAAK,EAAC,IAAI;YAACC,KAAK,EAAC;cAC5BpC,YAAA,CAAsCkC,oBAAA;YAA3BC,KAAK,EAAC,IAAI;YAACC,KAAK,EAAC;cAC5BpC,YAAA,CAAyCkC,oBAAA;YAA9BC,KAAK,EAAC,KAAK;YAACC,KAAK,EAAC;cAC7BpC,YAAA,CAAqCkC,oBAAA;YAA1BC,KAAK,EAAC,IAAI;YAACC,KAAK,EAAC;cAC5BpC,YAAA,CAAsCkC,oBAAA;YAA3BC,KAAK,EAAC,IAAI;YAACC,KAAK,EAAC;;;;;UAIhCpC,YAAA,CAOe0G,uBAAA;QAPDvE,KAAK,EAAC;MAAM;0BACxB,MAKE,CALFnC,YAAA,CAKEqB,mBAAA;sBAJST,MAAA,CAAA6F,UAAU,CAACY,WAAW;uEAAtBzG,MAAA,CAAA6F,UAAU,CAACY,WAAW,GAAA1G,MAAA;UAC/BH,IAAI,EAAC,UAAU;UACd8G,IAAI,EAAE,CAAC;UACR/F,WAAW,EAAC;;;UAIhBvB,YAAA,CAoBe0G,uBAAA;QApBDvE,KAAK,EAAC;MAAM;0BACxB,MAkBY,CAlBZnC,YAAA,CAkBYuH,oBAAA;UAjBVC,GAAG,EAAC,WAAW;UACd,WAAS,EAAE5G,MAAA,CAAA6G,cAAc;UACzB,WAAS,EAAE7G,MAAA,CAAA8G,kBAAkB;UAC7B,WAAS,EAAE9G,MAAA,CAAA+G,kBAAkB;UAC7B,aAAW,EAAE,KAAK;UACnBC,QAAQ,EAAR,EAAQ;UACRC,IAAI,EAAJ;;UAMWC,GAAG,EAAA3H,QAAA,CACZ,MAEMO,MAAA,SAAAA,MAAA,QAFNN,mBAAA,CAEM;YAFDP,KAAK,EAAC;UAAgB,GAAC,uBAE5B,mB;4BAPF,MAA4D,CAA5DG,YAAA,CAA4Dc,kBAAA;YAAnDjB,KAAK,EAAC;UAAiB;8BAAC,MAAiB,CAAjBG,YAAA,CAAiB+H,wBAAA,E;;0CAClD3H,mBAAA,CAEM;YAFDP,KAAK,EAAC;UAAiB,I,iBAAC,YAClB,GAAAO,mBAAA,CAAa,YAAT,MAAI,E;;;;;;;;;qCAmB3BgF,mBAAA,aAAgB,EAChBpF,YAAA,CAsBYkG,oBAAA;gBAtBQtF,MAAA,CAAAoH,cAAc;iEAAdpH,MAAA,CAAAoH,cAAc,GAAArH,MAAA;IAAEwF,KAAK,EAAC,MAAM;IAACpD,KAAK,EAAC;;IAgB1CqD,MAAM,EAAAjG,QAAA,CACf,MAAyD,CAAzDH,YAAA,CAAyDO,oBAAA;MAA7CE,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAAEC,MAAA,CAAAoH,cAAc;;wBAAU,MAAEtH,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;QAC7CV,YAAA,CAEYO,oBAAA;MAFDC,IAAI,EAAC,SAAS;MAAEC,OAAK,EAAEG,MAAA,CAAAqH,UAAU;MAAGxC,OAAO,EAAE7E,MAAA,CAAAsH;;wBAAU,MAElExH,MAAA,SAAAA,MAAA,Q,iBAFkE,MAElE,E;;;;sBAnBF,MAaU,CAbVV,YAAA,CAaUuG,kBAAA;MAbAC,KAAK,EAAE5F,MAAA,CAAAuH,QAAQ;MAAE,aAAW,EAAC;;wBACrC,MAEe,CAFfnI,YAAA,CAEe0G,uBAAA;QAFDvE,KAAK,EAAC;MAAK;0BACvB,MAAwD,CAAxDnC,YAAA,CAAwDqB,mBAAA;UAA7Ce,KAAK,EAAExB,MAAA,CAAAwH,WAAW,EAAErE,YAAY;UAAEsE,QAAQ,EAAR;;;UAG/CrI,YAAA,CAOe0G,uBAAA;QAPDvE,KAAK,EAAC;MAAM;0BACxB,MAKE,CALFnC,YAAA,CAKEqB,mBAAA;sBAJST,MAAA,CAAAuH,QAAQ,CAACd,WAAW;uEAApBzG,MAAA,CAAAuH,QAAQ,CAACd,WAAW,GAAA1G,MAAA;UAC7BH,IAAI,EAAC,UAAU;UACd8G,IAAI,EAAE,CAAC;UACR/F,WAAW,EAAC;;;;;;;qCAapB6D,mBAAA,aAAgB,EAChBpF,YAAA,CAIYkG,oBAAA;gBAJQtF,MAAA,CAAA0H,iBAAiB;iEAAjB1H,MAAA,CAAA0H,iBAAiB,GAAA3H,MAAA;IAAEwF,KAAK,EAAC,MAAM;IAACpD,KAAK,EAAC;;sBACxD,MAEM,CAFN3C,mBAAA,CAEM,OAFNmI,WAEM,GADJnI,mBAAA,CAAoF;MAA9EoI,GAAG,EAAE5H,MAAA,CAAA6H,eAAe;MAAEC,GAAG,EAAC,MAAM;MAACC,KAA0C,EAA1C;QAAA;QAAA;MAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}