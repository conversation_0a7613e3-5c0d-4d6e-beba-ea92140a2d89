{"ast": null, "code": "import { createElementVNode as _createElementVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, resolveComponent as _resolveComponent, createBlock as _createBlock, withCtx as _withCtx, createVNode as _createVNode, createTextVNode as _createTextVNode, createCommentVNode as _createCommentVNode, toDisplayString as _toDisplayString, withModifiers as _withModifiers, resolveDirective as _resolveDirective, withDirectives as _withDirectives } from \"vue\";\nconst _hoisted_1 = {\n  class: \"task-management\"\n};\nconst _hoisted_2 = {\n  class: \"card-header\"\n};\nconst _hoisted_3 = {\n  class: \"header-actions\"\n};\nconst _hoisted_4 = {\n  class: \"task-stats\"\n};\nconst _hoisted_5 = {\n  class: \"task-list\"\n};\nconst _hoisted_6 = {\n  key: 0,\n  class: \"empty-state\"\n};\nconst _hoisted_7 = {\n  key: 1,\n  style: {\n    \"color\": \"#909399\",\n    \"font-size\": \"14px\",\n    \"margin-top\": \"16px\"\n  }\n};\nconst _hoisted_8 = {\n  key: 1,\n  class: \"task-grid\"\n};\nconst _hoisted_9 = {\n  class: \"task-description\"\n};\nconst _hoisted_10 = {\n  class: \"task-meta\"\n};\nconst _hoisted_11 = {\n  class: \"task-info\"\n};\nconst _hoisted_12 = {\n  key: 0,\n  class: \"info-item\"\n};\nconst _hoisted_13 = {\n  key: 1,\n  class: \"info-item\"\n};\nconst _hoisted_14 = {\n  class: \"task-footer\"\n};\nconst _hoisted_15 = {\n  key: 0,\n  class: \"pagination\"\n};\nconst _hoisted_16 = {\n  key: 0,\n  class: \"task-detail\"\n};\nconst _hoisted_17 = {\n  key: 0,\n  class: \"progress-update\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_option = _resolveComponent(\"el-option\");\n  const _component_el_select = _resolveComponent(\"el-select\");\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_el_statistic = _resolveComponent(\"el-statistic\");\n  const _component_el_col = _resolveComponent(\"el-col\");\n  const _component_el_row = _resolveComponent(\"el-row\");\n  const _component_el_tab_pane = _resolveComponent(\"el-tab-pane\");\n  const _component_el_tabs = _resolveComponent(\"el-tabs\");\n  const _component_el_empty = _resolveComponent(\"el-empty\");\n  const _component_el_tag = _resolveComponent(\"el-tag\");\n  const _component_Calendar = _resolveComponent(\"Calendar\");\n  const _component_el_icon = _resolveComponent(\"el-icon\");\n  const _component_User = _resolveComponent(\"User\");\n  const _component_el_dropdown_item = _resolveComponent(\"el-dropdown-item\");\n  const _component_el_dropdown_menu = _resolveComponent(\"el-dropdown-menu\");\n  const _component_el_dropdown = _resolveComponent(\"el-dropdown\");\n  const _component_el_card = _resolveComponent(\"el-card\");\n  const _component_el_pagination = _resolveComponent(\"el-pagination\");\n  const _component_el_input = _resolveComponent(\"el-input\");\n  const _component_el_form_item = _resolveComponent(\"el-form-item\");\n  const _component_el_date_picker = _resolveComponent(\"el-date-picker\");\n  const _component_el_form = _resolveComponent(\"el-form\");\n  const _component_el_dialog = _resolveComponent(\"el-dialog\");\n  const _component_el_descriptions_item = _resolveComponent(\"el-descriptions-item\");\n  const _component_el_descriptions = _resolveComponent(\"el-descriptions\");\n  const _component_el_slider = _resolveComponent(\"el-slider\");\n  const _component_upload_filled = _resolveComponent(\"upload-filled\");\n  const _component_el_upload = _resolveComponent(\"el-upload\");\n  const _directive_loading = _resolveDirective(\"loading\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_el_card, null, {\n    header: _withCtx(() => [_createElementVNode(\"div\", _hoisted_2, [_cache[19] || (_cache[19] = _createElementVNode(\"h3\", null, \"任务管理\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_3, [_createVNode(_component_el_select, {\n      modelValue: $setup.currentTeamId,\n      \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $setup.currentTeamId = $event),\n      placeholder: \"选择团队\",\n      onChange: $setup.loadTasks,\n      style: {\n        \"width\": \"200px\"\n      }\n    }, {\n      default: _withCtx(() => [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.myTeams, team => {\n        return _openBlock(), _createBlock(_component_el_option, {\n          key: team.id,\n          label: team.projectName ? `${team.name} (${team.projectName})` : team.name,\n          value: team.id\n        }, null, 8 /* PROPS */, [\"label\", \"value\"]);\n      }), 128 /* KEYED_FRAGMENT */))]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\", \"onChange\"]), $setup.isTeacher ? (_openBlock(), _createBlock(_component_el_button, {\n      key: 0,\n      type: \"primary\",\n      onClick: $setup.goToTaskPublish,\n      icon: _ctx.Plus\n    }, {\n      default: _withCtx(() => _cache[17] || (_cache[17] = [_createTextVNode(\" 发布任务 \")])),\n      _: 1 /* STABLE */,\n      __: [17]\n    }, 8 /* PROPS */, [\"onClick\", \"icon\"])) : _createCommentVNode(\"v-if\", true), _createVNode(_component_el_button, {\n      onClick: $setup.loadTasks,\n      loading: $setup.loading,\n      icon: _ctx.Refresh\n    }, {\n      default: _withCtx(() => _cache[18] || (_cache[18] = [_createTextVNode(\" 刷新 \")])),\n      _: 1 /* STABLE */,\n      __: [18]\n    }, 8 /* PROPS */, [\"onClick\", \"loading\", \"icon\"])])])]),\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_4, [_createVNode(_component_el_row, {\n      gutter: 16\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_col, {\n        span: 6\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_statistic, {\n          title: \"总任务数\",\n          value: $setup.taskStats.total\n        }, null, 8 /* PROPS */, [\"value\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_col, {\n        span: 6\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_statistic, {\n          title: \"进行中\",\n          value: $setup.taskStats.inProgress\n        }, null, 8 /* PROPS */, [\"value\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_col, {\n        span: 6\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_statistic, {\n          title: \"已完成\",\n          value: $setup.taskStats.completed\n        }, null, 8 /* PROPS */, [\"value\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_col, {\n        span: 6\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_statistic, {\n          title: \"完成率\",\n          value: $setup.taskStats.completionRate,\n          suffix: \"%\"\n        }, null, 8 /* PROPS */, [\"value\"])]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    })]), _withDirectives((_openBlock(), _createElementBlock(\"div\", _hoisted_5, [_createVNode(_component_el_tabs, {\n      modelValue: $setup.activeTab,\n      \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $setup.activeTab = $event),\n      onTabChange: $setup.loadTasks\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_tab_pane, {\n        label: \"全部任务\",\n        name: \"all\"\n      }), _createVNode(_component_el_tab_pane, {\n        label: \"已发布\",\n        name: \"PUBLISHED\"\n      }), _createVNode(_component_el_tab_pane, {\n        label: \"活跃任务\",\n        name: \"ACTIVE\"\n      }), _createVNode(_component_el_tab_pane, {\n        label: \"进行中\",\n        name: \"IN_PROGRESS\"\n      }), _createVNode(_component_el_tab_pane, {\n        label: \"待审核\",\n        name: \"SUBMITTED\"\n      }), _createVNode(_component_el_tab_pane, {\n        label: \"已完成\",\n        name: \"COMPLETED\"\n      }), _createVNode(_component_el_tab_pane, {\n        label: \"已取消\",\n        name: \"CANCELLED\"\n      })]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\", \"onTabChange\"]), $setup.tasks.length === 0 && !$setup.loading ? (_openBlock(), _createElementBlock(\"div\", _hoisted_6, [_createVNode(_component_el_empty, {\n      description: \"暂无任务\"\n    }, {\n      default: _withCtx(() => [$setup.isTeacher ? (_openBlock(), _createBlock(_component_el_button, {\n        key: 0,\n        type: \"primary\",\n        onClick: $setup.goToTaskPublish\n      }, {\n        default: _withCtx(() => _cache[20] || (_cache[20] = [_createTextVNode(\" 发布第一个任务 \")])),\n        _: 1 /* STABLE */,\n        __: [20]\n      }, 8 /* PROPS */, [\"onClick\"])) : (_openBlock(), _createElementBlock(\"p\", _hoisted_7, \" 等待教师发布任务 \"))]),\n      _: 1 /* STABLE */\n    })])) : (_openBlock(), _createElementBlock(\"div\", _hoisted_8, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.tasks, task => {\n      return _openBlock(), _createElementBlock(\"div\", {\n        key: task.id,\n        class: \"task-card\"\n      }, [_createVNode(_component_el_card, {\n        shadow: \"hover\",\n        onClick: $event => $setup.viewTask(task)\n      }, {\n        default: _withCtx(() => [_createElementVNode(\"h4\", null, _toDisplayString(task.title), 1 /* TEXT */), _createElementVNode(\"p\", _hoisted_9, _toDisplayString(task.content || task.description || '暂无描述'), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_10, [_createVNode(_component_el_tag, {\n          type: $setup.getStatusColor(task.status),\n          size: \"small\"\n        }, {\n          default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.getStatusText(task.status)), 1 /* TEXT */)]),\n          _: 2 /* DYNAMIC */\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"type\"]), task.priority ? (_openBlock(), _createBlock(_component_el_tag, {\n          key: 0,\n          type: $setup.getPriorityColor(task.priority),\n          size: \"small\"\n        }, {\n          default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.getPriorityText(task.priority)), 1 /* TEXT */)]),\n          _: 2 /* DYNAMIC */\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"type\"])) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"div\", _hoisted_11, [task.deadline ? (_openBlock(), _createElementBlock(\"div\", _hoisted_12, [_createVNode(_component_el_icon, null, {\n          default: _withCtx(() => [_createVNode(_component_Calendar)]),\n          _: 1 /* STABLE */\n        }), _createElementVNode(\"span\", null, _toDisplayString($setup.formatDate(task.deadline)), 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true), task.assignee ? (_openBlock(), _createElementBlock(\"div\", _hoisted_13, [_createVNode(_component_el_icon, null, {\n          default: _withCtx(() => [_createVNode(_component_User)]),\n          _: 1 /* STABLE */\n        }), _createElementVNode(\"span\", null, _toDisplayString(task.assignee), 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"div\", _hoisted_14, [_createVNode(_component_el_button, {\n          size: \"small\",\n          onClick: _withModifiers($event => $setup.viewTask(task), [\"stop\"])\n        }, {\n          default: _withCtx(() => [...(_cache[21] || (_cache[21] = [_createTextVNode(\" 查看详情 \")]))]),\n          _: 2 /* DYNAMIC */,\n          __: [21]\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"]), _createCommentVNode(\" 学生操作 \"), !$setup.isTeacher ? (_openBlock(), _createElementBlock(_Fragment, {\n          key: 0\n        }, [task.status === 'PUBLISHED' ? (_openBlock(), _createBlock(_component_el_button, {\n          key: 0,\n          size: \"small\",\n          type: \"primary\",\n          onClick: _withModifiers($event => $setup.startTask(task), [\"stop\"])\n        }, {\n          default: _withCtx(() => [...(_cache[22] || (_cache[22] = [_createTextVNode(\" 开始任务 \")]))]),\n          _: 2 /* DYNAMIC */,\n          __: [22]\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"])) : _createCommentVNode(\"v-if\", true), task.status === 'ACTIVE' || task.status === 'IN_PROGRESS' ? (_openBlock(), _createBlock(_component_el_button, {\n          key: 1,\n          size: \"small\",\n          type: \"success\",\n          onClick: _withModifiers($event => $setup.showSubmitDialog(task), [\"stop\"])\n        }, {\n          default: _withCtx(() => [...(_cache[23] || (_cache[23] = [_createTextVNode(\" 提交任务 \")]))]),\n          _: 2 /* DYNAMIC */,\n          __: [23]\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"])) : _createCommentVNode(\"v-if\", true)], 64 /* STABLE_FRAGMENT */)) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 教师操作 \"), $setup.isTeacher ? (_openBlock(), _createElementBlock(_Fragment, {\n          key: 1\n        }, [$setup.canEdit(task) ? (_openBlock(), _createBlock(_component_el_button, {\n          key: 0,\n          size: \"small\",\n          onClick: _withModifiers($event => $setup.editTask(task), [\"stop\"])\n        }, {\n          default: _withCtx(() => [...(_cache[24] || (_cache[24] = [_createTextVNode(\" 编辑 \")]))]),\n          _: 2 /* DYNAMIC */,\n          __: [24]\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"])) : _createCommentVNode(\"v-if\", true), $setup.canManage(task) ? (_openBlock(), _createBlock(_component_el_dropdown, {\n          key: 1,\n          onCommand: $setup.handleTaskAction,\n          onClick: _cache[2] || (_cache[2] = _withModifiers(() => {}, [\"stop\"]))\n        }, {\n          dropdown: _withCtx(() => [_createVNode(_component_el_dropdown_menu, null, {\n            default: _withCtx(() => [task.status === 'SUBMITTED' ? (_openBlock(), _createBlock(_component_el_dropdown_item, {\n              key: 0,\n              command: {\n                action: 'approve',\n                task\n              }\n            }, {\n              default: _withCtx(() => [...(_cache[26] || (_cache[26] = [_createTextVNode(\" 通过审核 \")]))]),\n              _: 2 /* DYNAMIC */,\n              __: [26]\n            }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"command\"])) : _createCommentVNode(\"v-if\", true), task.status === 'SUBMITTED' ? (_openBlock(), _createBlock(_component_el_dropdown_item, {\n              key: 1,\n              command: {\n                action: 'reject',\n                task\n              }\n            }, {\n              default: _withCtx(() => [...(_cache[27] || (_cache[27] = [_createTextVNode(\" 拒绝任务 \")]))]),\n              _: 2 /* DYNAMIC */,\n              __: [27]\n            }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"command\"])) : _createCommentVNode(\"v-if\", true), task.status === 'PUBLISHED' ? (_openBlock(), _createBlock(_component_el_dropdown_item, {\n              key: 2,\n              command: {\n                action: 'activate',\n                task\n              }\n            }, {\n              default: _withCtx(() => [...(_cache[28] || (_cache[28] = [_createTextVNode(\" 激活任务 \")]))]),\n              _: 2 /* DYNAMIC */,\n              __: [28]\n            }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"command\"])) : _createCommentVNode(\"v-if\", true), ['PUBLISHED', 'ACTIVE', 'IN_PROGRESS'].includes(task.status) ? (_openBlock(), _createBlock(_component_el_dropdown_item, {\n              key: 3,\n              command: {\n                action: 'cancel',\n                task\n              }\n            }, {\n              default: _withCtx(() => [...(_cache[29] || (_cache[29] = [_createTextVNode(\" 取消任务 \")]))]),\n              _: 2 /* DYNAMIC */,\n              __: [29]\n            }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"command\"])) : _createCommentVNode(\"v-if\", true), _createVNode(_component_el_dropdown_item, {\n              command: {\n                action: 'delete',\n                task\n              },\n              divided: \"\"\n            }, {\n              default: _withCtx(() => [...(_cache[30] || (_cache[30] = [_createTextVNode(\" 删除任务 \")]))]),\n              _: 2 /* DYNAMIC */,\n              __: [30]\n            }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"command\"])]),\n            _: 2 /* DYNAMIC */\n          }, 1024 /* DYNAMIC_SLOTS */)]),\n          default: _withCtx(() => [_createVNode(_component_el_button, {\n            size: \"small\",\n            icon: _ctx.ArrowDown\n          }, {\n            default: _withCtx(() => [...(_cache[25] || (_cache[25] = [_createTextVNode(\" 更多 \")]))]),\n            _: 1 /* STABLE */,\n            __: [25]\n          }, 8 /* PROPS */, [\"icon\"])]),\n          _: 2 /* DYNAMIC */\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onCommand\"])) : _createCommentVNode(\"v-if\", true)], 64 /* STABLE_FRAGMENT */)) : _createCommentVNode(\"v-if\", true)])]),\n        _: 2 /* DYNAMIC */\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"])]);\n    }), 128 /* KEYED_FRAGMENT */))]))])), [[_directive_loading, $setup.loading]]), $setup.total > 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_15, [_createVNode(_component_el_pagination, {\n      \"current-page\": $setup.currentPage,\n      \"onUpdate:currentPage\": _cache[3] || (_cache[3] = $event => $setup.currentPage = $event),\n      \"page-size\": $setup.pageSize,\n      \"onUpdate:pageSize\": _cache[4] || (_cache[4] = $event => $setup.pageSize = $event),\n      total: $setup.total,\n      \"page-sizes\": [10, 20, 50],\n      layout: \"total, sizes, prev, pager, next, jumper\",\n      onSizeChange: $setup.loadTasks,\n      onCurrentChange: $setup.loadTasks\n    }, null, 8 /* PROPS */, [\"current-page\", \"page-size\", \"total\", \"onSizeChange\", \"onCurrentChange\"])])) : _createCommentVNode(\"v-if\", true)]),\n    _: 1 /* STABLE */\n  }), _createCommentVNode(\" 创建/编辑任务对话框 \"), _createVNode(_component_el_dialog, {\n    modelValue: $setup.showCreateDialog,\n    \"onUpdate:modelValue\": _cache[10] || (_cache[10] = $event => $setup.showCreateDialog = $event),\n    title: $setup.editingTask ? '编辑任务' : '创建任务',\n    width: \"600px\"\n  }, {\n    footer: _withCtx(() => [_createVNode(_component_el_button, {\n      onClick: _cache[9] || (_cache[9] = $event => $setup.showCreateDialog = false)\n    }, {\n      default: _withCtx(() => _cache[32] || (_cache[32] = [_createTextVNode(\"取消\")])),\n      _: 1 /* STABLE */,\n      __: [32]\n    }), _createVNode(_component_el_button, {\n      type: \"primary\",\n      onClick: $setup.submitTask,\n      loading: $setup.submitting\n    }, {\n      default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.editingTask ? '更新' : '创建'), 1 /* TEXT */)]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"onClick\", \"loading\"])]),\n    default: _withCtx(() => [_createVNode(_component_el_form, {\n      ref: \"formRef\",\n      model: $setup.taskForm,\n      rules: $setup.formRules,\n      \"label-width\": \"80px\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_form_item, {\n        label: \"任务标题\",\n        prop: \"title\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.taskForm.title,\n          \"onUpdate:modelValue\": _cache[5] || (_cache[5] = $event => $setup.taskForm.title = $event),\n          placeholder: \"请输入任务标题\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"任务描述\",\n        prop: \"description\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.taskForm.description,\n          \"onUpdate:modelValue\": _cache[6] || (_cache[6] = $event => $setup.taskForm.description = $event),\n          type: \"textarea\",\n          rows: 4,\n          placeholder: \"请输入任务描述\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"负责人\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          value: $setup.currentUser?.realName || '当前用户',\n          disabled: \"\"\n        }, null, 8 /* PROPS */, [\"value\"]), _cache[31] || (_cache[31] = _createElementVNode(\"div\", {\n          class: \"form-help-text\"\n        }, \"任务创建者即为负责人\", -1 /* CACHED */))]),\n        _: 1 /* STABLE */,\n        __: [31]\n      }), _createVNode(_component_el_form_item, {\n        label: \"优先级\",\n        prop: \"priority\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_select, {\n          modelValue: $setup.taskForm.priority,\n          \"onUpdate:modelValue\": _cache[7] || (_cache[7] = $event => $setup.taskForm.priority = $event),\n          placeholder: \"请选择优先级\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_option, {\n            label: \"低\",\n            value: \"LOW\"\n          }), _createVNode(_component_el_option, {\n            label: \"中\",\n            value: \"MEDIUM\"\n          }), _createVNode(_component_el_option, {\n            label: \"高\",\n            value: \"HIGH\"\n          }), _createVNode(_component_el_option, {\n            label: \"紧急\",\n            value: \"URGENT\"\n          })]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"截止时间\",\n        prop: \"deadline\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_date_picker, {\n          modelValue: $setup.taskForm.deadline,\n          \"onUpdate:modelValue\": _cache[8] || (_cache[8] = $event => $setup.taskForm.deadline = $event),\n          type: \"datetime\",\n          placeholder: \"请选择截止时间\",\n          style: {\n            \"width\": \"100%\"\n          }\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"model\", \"rules\"])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"title\"]), _createCommentVNode(\" 任务详情对话框 \"), _createVNode(_component_el_dialog, {\n    modelValue: $setup.showDetailDialog,\n    \"onUpdate:modelValue\": _cache[13] || (_cache[13] = $event => $setup.showDetailDialog = $event),\n    title: \"任务详情\",\n    width: \"700px\"\n  }, {\n    footer: _withCtx(() => [_createVNode(_component_el_button, {\n      onClick: _cache[12] || (_cache[12] = $event => $setup.showDetailDialog = false)\n    }, {\n      default: _withCtx(() => _cache[34] || (_cache[34] = [_createTextVNode(\"关闭\")])),\n      _: 1 /* STABLE */,\n      __: [34]\n    })]),\n    default: _withCtx(() => [$setup.selectedTask ? (_openBlock(), _createElementBlock(\"div\", _hoisted_16, [_createVNode(_component_el_descriptions, {\n      column: 2,\n      border: \"\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_descriptions_item, {\n        label: \"任务标题\",\n        span: 2\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.selectedTask.title), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_descriptions_item, {\n        label: \"任务状态\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_tag, {\n          type: $setup.getStatusColor($setup.selectedTask.status)\n        }, {\n          default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.getStatusText($setup.selectedTask.status)), 1 /* TEXT */)]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"type\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_descriptions_item, {\n        label: \"优先级\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_tag, {\n          type: $setup.getPriorityColor($setup.selectedTask.priority)\n        }, {\n          default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.getPriorityText($setup.selectedTask.priority)), 1 /* TEXT */)]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"type\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_descriptions_item, {\n        label: \"创建人\"\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.selectedTask.creator?.realName), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_descriptions_item, {\n        label: \"创建时间\"\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.formatDate($setup.selectedTask.createTime)), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_descriptions_item, {\n        label: \"截止时间\"\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.formatDate($setup.selectedTask.deadline)), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_descriptions_item, {\n        label: \"任务描述\",\n        span: 2\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.selectedTask.content || $setup.selectedTask.description || '暂无描述'), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }), _createCommentVNode(\" 进度更新 \"), $setup.selectedTask.status === 'IN_PROGRESS' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_17, [_cache[33] || (_cache[33] = _createElementVNode(\"h4\", null, \"更新进度\", -1 /* CACHED */)), _createVNode(_component_el_slider, {\n      modelValue: $setup.progressValue,\n      \"onUpdate:modelValue\": _cache[11] || (_cache[11] = $event => $setup.progressValue = $event),\n      max: 100,\n      step: 5,\n      \"show-stops\": \"\",\n      \"show-input\": \"\",\n      onChange: $setup.updateProgress\n    }, null, 8 /* PROPS */, [\"modelValue\", \"onChange\"])])) : _createCommentVNode(\"v-if\", true)])) : _createCommentVNode(\"v-if\", true)]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"]), _createCommentVNode(\" 任务提交对话框 \"), _createVNode(_component_el_dialog, {\n    modelValue: $setup.showSubmitTaskDialog,\n    \"onUpdate:modelValue\": _cache[16] || (_cache[16] = $event => $setup.showSubmitTaskDialog = $event),\n    title: \"提交任务\",\n    width: \"600px\"\n  }, {\n    footer: _withCtx(() => [_createVNode(_component_el_button, {\n      onClick: _cache[15] || (_cache[15] = $event => $setup.showSubmitTaskDialog = false)\n    }, {\n      default: _withCtx(() => _cache[37] || (_cache[37] = [_createTextVNode(\"取消\")])),\n      _: 1 /* STABLE */,\n      __: [37]\n    }), _createVNode(_component_el_button, {\n      type: \"primary\",\n      onClick: $setup.submitTaskCompletion,\n      loading: $setup.submitting\n    }, {\n      default: _withCtx(() => _cache[38] || (_cache[38] = [_createTextVNode(\" 提交 \")])),\n      _: 1 /* STABLE */,\n      __: [38]\n    }, 8 /* PROPS */, [\"onClick\", \"loading\"])]),\n    default: _withCtx(() => [_createVNode(_component_el_form, {\n      ref: \"submitFormRef\",\n      model: $setup.submitForm,\n      rules: $setup.submitFormRules,\n      \"label-width\": \"100px\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_form_item, {\n        label: \"任务名称\"\n      }, {\n        default: _withCtx(() => [_createElementVNode(\"span\", null, _toDisplayString($setup.selectedTask?.title), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"提交说明\",\n        prop: \"content\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.submitForm.content,\n          \"onUpdate:modelValue\": _cache[14] || (_cache[14] = $event => $setup.submitForm.content = $event),\n          type: \"textarea\",\n          rows: 5,\n          placeholder: \"请详细描述您的任务完成情况...\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"提交文件\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_upload, {\n          ref: \"uploadRef\",\n          \"file-list\": $setup.submitForm.fileList,\n          \"on-change\": $setup.handleFileChange,\n          \"on-remove\": $setup.handleFileRemove,\n          \"before-upload\": $setup.beforeUpload,\n          \"auto-upload\": false,\n          multiple: \"\",\n          drag: \"\"\n        }, {\n          tip: _withCtx(() => _cache[35] || (_cache[35] = [_createElementVNode(\"div\", {\n            class: \"el-upload__tip\"\n          }, \" 支持多个文件上传，单个文件大小不超过10MB \", -1 /* CACHED */)])),\n          default: _withCtx(() => [_createVNode(_component_el_icon, {\n            class: \"el-icon--upload\"\n          }, {\n            default: _withCtx(() => [_createVNode(_component_upload_filled)]),\n            _: 1 /* STABLE */\n          }), _cache[36] || (_cache[36] = _createElementVNode(\"div\", {\n            class: \"el-upload__text\"\n          }, [_createTextVNode(\" 将文件拖到此处，或\"), _createElementVNode(\"em\", null, \"点击上传\")], -1 /* CACHED */))]),\n          _: 1 /* STABLE */,\n          __: [36]\n        }, 8 /* PROPS */, [\"file-list\", \"on-change\", \"on-remove\", \"before-upload\"])]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"model\", \"rules\"])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"])]);\n}", "map": {"version": 3, "names": ["class", "style", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_card", "header", "_withCtx", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_component_el_select", "$setup", "currentTeamId", "$event", "placeholder", "onChange", "loadTasks", "_Fragment", "_renderList", "myTeams", "team", "_createBlock", "_component_el_option", "key", "id", "label", "projectName", "name", "value", "<PERSON><PERSON><PERSON>er", "_component_el_button", "type", "onClick", "goToTaskPublish", "icon", "_ctx", "Plus", "_cache", "loading", "Refresh", "_hoisted_4", "_component_el_row", "gutter", "_component_el_col", "span", "_component_el_statistic", "title", "taskStats", "total", "inProgress", "completed", "completionRate", "suffix", "_hoisted_5", "_component_el_tabs", "activeTab", "onTabChange", "_component_el_tab_pane", "tasks", "length", "_hoisted_6", "_component_el_empty", "description", "_hoisted_7", "_hoisted_8", "task", "shadow", "viewTask", "_toDisplayString", "_hoisted_9", "content", "_hoisted_10", "_component_el_tag", "getStatusColor", "status", "size", "getStatusText", "priority", "getPriorityColor", "getPriorityText", "_hoisted_11", "deadline", "_hoisted_12", "_component_el_icon", "_component_Calendar", "formatDate", "assignee", "_hoisted_13", "_component_User", "_hoisted_14", "_withModifiers", "_createCommentVNode", "startTask", "showSubmitDialog", "canEdit", "editTask", "canManage", "_component_el_dropdown", "onCommand", "handleTaskAction", "dropdown", "_component_el_dropdown_menu", "_component_el_dropdown_item", "command", "action", "includes", "divided", "ArrowDown", "_hoisted_15", "_component_el_pagination", "currentPage", "pageSize", "layout", "onSizeChange", "onCurrentChange", "_component_el_dialog", "showCreateDialog", "editingTask", "width", "footer", "submitTask", "submitting", "_component_el_form", "ref", "model", "taskForm", "rules", "formRules", "_component_el_form_item", "prop", "_component_el_input", "rows", "currentUser", "realName", "disabled", "_component_el_date_picker", "showDetailDialog", "selectedTask", "_hoisted_16", "_component_el_descriptions", "column", "border", "_component_el_descriptions_item", "creator", "createTime", "_hoisted_17", "_component_el_slider", "progressValue", "max", "step", "updateProgress", "showSubmitTaskDialog", "submitTaskCompletion", "submitForm", "submitFormRules", "_component_el_upload", "fileList", "handleFileChange", "handleFileRemove", "beforeUpload", "multiple", "drag", "tip", "_component_upload_filled"], "sources": ["D:\\workspace\\idea\\worker\\work_cli\\src\\views\\collaboration\\TaskManagementView.vue"], "sourcesContent": ["<template>\n  <div class=\"task-management\">\n    <el-card>\n      <template #header>\n        <div class=\"card-header\">\n          <h3>任务管理</h3>\n          <div class=\"header-actions\">\n            <el-select v-model=\"currentTeamId\" placeholder=\"选择团队\" @change=\"loadTasks\" style=\"width: 200px;\">\n              <el-option\n                v-for=\"team in myTeams\"\n                :key=\"team.id\"\n                :label=\"team.projectName ? `${team.name} (${team.projectName})` : team.name\"\n                :value=\"team.id\"\n              />\n            </el-select>\n            <el-button v-if=\"isTeacher\" type=\"primary\" @click=\"goToTaskPublish\" :icon=\"Plus\">\n              发布任务\n            </el-button>\n            <el-button @click=\"loadTasks\" :loading=\"loading\" :icon=\"Refresh\">\n              刷新\n            </el-button>\n          </div>\n        </div>\n      </template>\n      \n      <!-- 任务统计 -->\n      <div class=\"task-stats\">\n        <el-row :gutter=\"16\">\n          <el-col :span=\"6\">\n            <el-statistic title=\"总任务数\" :value=\"taskStats.total\" />\n          </el-col>\n          <el-col :span=\"6\">\n            <el-statistic title=\"进行中\" :value=\"taskStats.inProgress\" />\n          </el-col>\n          <el-col :span=\"6\">\n            <el-statistic title=\"已完成\" :value=\"taskStats.completed\" />\n          </el-col>\n          <el-col :span=\"6\">\n            <el-statistic title=\"完成率\" :value=\"taskStats.completionRate\" suffix=\"%\" />\n          </el-col>\n        </el-row>\n      </div>\n      \n\n\n      <!-- 任务列表 -->\n      <div class=\"task-list\" v-loading=\"loading\">\n        <el-tabs v-model=\"activeTab\" @tab-change=\"loadTasks\">\n          <el-tab-pane label=\"全部任务\" name=\"all\" />\n          <el-tab-pane label=\"已发布\" name=\"PUBLISHED\" />\n          <el-tab-pane label=\"活跃任务\" name=\"ACTIVE\" />\n          <el-tab-pane label=\"进行中\" name=\"IN_PROGRESS\" />\n          <el-tab-pane label=\"待审核\" name=\"SUBMITTED\" />\n          <el-tab-pane label=\"已完成\" name=\"COMPLETED\" />\n          <el-tab-pane label=\"已取消\" name=\"CANCELLED\" />\n        </el-tabs>\n\n        <div v-if=\"tasks.length === 0 && !loading\" class=\"empty-state\">\n          <el-empty description=\"暂无任务\">\n            <el-button v-if=\"isTeacher\" type=\"primary\" @click=\"goToTaskPublish\">\n              发布第一个任务\n            </el-button>\n            <template v-else>\n              <p style=\"color: #909399; font-size: 14px; margin-top: 16px;\">\n                等待教师发布任务\n              </p>\n            </template>\n          </el-empty>\n        </div>\n\n        <div v-else class=\"task-grid\">\n          <div v-for=\"task in tasks\" :key=\"task.id\" class=\"task-card\">\n            <el-card shadow=\"hover\" @click=\"viewTask(task)\">\n              <h4>{{ task.title }}</h4>\n              <p class=\"task-description\">{{ task.content || task.description || '暂无描述' }}</p>\n              <div class=\"task-meta\">\n                <el-tag :type=\"getStatusColor(task.status)\" size=\"small\">\n                  {{ getStatusText(task.status) }}\n                </el-tag>\n                <el-tag v-if=\"task.priority\" :type=\"getPriorityColor(task.priority)\" size=\"small\">\n                  {{ getPriorityText(task.priority) }}\n                </el-tag>\n              </div>\n              <div class=\"task-info\">\n                <div class=\"info-item\" v-if=\"task.deadline\">\n                  <el-icon><Calendar /></el-icon>\n                  <span>{{ formatDate(task.deadline) }}</span>\n                </div>\n                <div class=\"info-item\" v-if=\"task.assignee\">\n                  <el-icon><User /></el-icon>\n                  <span>{{ task.assignee }}</span>\n                </div>\n              </div>\n              <div class=\"task-footer\">\n                <el-button size=\"small\" @click.stop=\"viewTask(task)\">\n                  查看详情\n                </el-button>\n\n                <!-- 学生操作 -->\n                <template v-if=\"!isTeacher\">\n                  <el-button\n                    v-if=\"task.status === 'PUBLISHED'\"\n                    size=\"small\"\n                    type=\"primary\"\n                    @click.stop=\"startTask(task)\"\n                  >\n                    开始任务\n                  </el-button>\n                  <el-button\n                    v-if=\"task.status === 'ACTIVE' || task.status === 'IN_PROGRESS'\"\n                    size=\"small\"\n                    type=\"success\"\n                    @click.stop=\"showSubmitDialog(task)\"\n                  >\n                    提交任务\n                  </el-button>\n                </template>\n\n                <!-- 教师操作 -->\n                <template v-if=\"isTeacher\">\n                  <el-button v-if=\"canEdit(task)\" size=\"small\" @click.stop=\"editTask(task)\">\n                    编辑\n                  </el-button>\n                  <el-dropdown v-if=\"canManage(task)\" @command=\"handleTaskAction\" @click.stop>\n                    <el-button size=\"small\" :icon=\"ArrowDown\">\n                      更多\n                    </el-button>\n                    <template #dropdown>\n                      <el-dropdown-menu>\n                        <el-dropdown-item\n                          v-if=\"task.status === 'SUBMITTED'\"\n                          :command=\"{action: 'approve', task}\"\n                        >\n                          通过审核\n                        </el-dropdown-item>\n                        <el-dropdown-item\n                          v-if=\"task.status === 'SUBMITTED'\"\n                          :command=\"{action: 'reject', task}\"\n                        >\n                          拒绝任务\n                        </el-dropdown-item>\n                        <el-dropdown-item\n                          v-if=\"task.status === 'PUBLISHED'\"\n                          :command=\"{action: 'activate', task}\"\n                        >\n                          激活任务\n                        </el-dropdown-item>\n                        <el-dropdown-item\n                          v-if=\"['PUBLISHED', 'ACTIVE', 'IN_PROGRESS'].includes(task.status)\"\n                          :command=\"{action: 'cancel', task}\"\n                        >\n                          取消任务\n                        </el-dropdown-item>\n                        <el-dropdown-item\n                          :command=\"{action: 'delete', task}\"\n                          divided\n                        >\n                          删除任务\n                        </el-dropdown-item>\n                      </el-dropdown-menu>\n                    </template>\n                  </el-dropdown>\n                </template>\n              </div>\n            </el-card>\n          </div>\n        </div>\n      </div>\n\n      \n      <!-- 分页 -->\n      <div v-if=\"total > 0\" class=\"pagination\">\n        <el-pagination\n          v-model:current-page=\"currentPage\"\n          v-model:page-size=\"pageSize\"\n          :total=\"total\"\n          :page-sizes=\"[10, 20, 50]\"\n          layout=\"total, sizes, prev, pager, next, jumper\"\n          @size-change=\"loadTasks\"\n          @current-change=\"loadTasks\"\n        />\n      </div>\n    </el-card>\n    \n    <!-- 创建/编辑任务对话框 -->\n    <el-dialog\n      v-model=\"showCreateDialog\"\n      :title=\"editingTask ? '编辑任务' : '创建任务'\"\n      width=\"600px\"\n    >\n      <el-form\n        ref=\"formRef\"\n        :model=\"taskForm\"\n        :rules=\"formRules\"\n        label-width=\"80px\"\n      >\n        <el-form-item label=\"任务标题\" prop=\"title\">\n          <el-input v-model=\"taskForm.title\" placeholder=\"请输入任务标题\" />\n        </el-form-item>\n        \n        <el-form-item label=\"任务描述\" prop=\"description\">\n          <el-input\n            v-model=\"taskForm.description\"\n            type=\"textarea\"\n            :rows=\"4\"\n            placeholder=\"请输入任务描述\"\n          />\n        </el-form-item>\n        \n        <el-form-item label=\"负责人\">\n          <el-input :value=\"currentUser?.realName || '当前用户'\" disabled />\n          <div class=\"form-help-text\">任务创建者即为负责人</div>\n        </el-form-item>\n        \n        <el-form-item label=\"优先级\" prop=\"priority\">\n          <el-select v-model=\"taskForm.priority\" placeholder=\"请选择优先级\">\n            <el-option label=\"低\" value=\"LOW\" />\n            <el-option label=\"中\" value=\"MEDIUM\" />\n            <el-option label=\"高\" value=\"HIGH\" />\n            <el-option label=\"紧急\" value=\"URGENT\" />\n          </el-select>\n        </el-form-item>\n        \n        <el-form-item label=\"截止时间\" prop=\"deadline\">\n          <el-date-picker\n            v-model=\"taskForm.deadline\"\n            type=\"datetime\"\n            placeholder=\"请选择截止时间\"\n            style=\"width: 100%\"\n          />\n        </el-form-item>\n      </el-form>\n      \n      <template #footer>\n        <el-button @click=\"showCreateDialog = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"submitTask\" :loading=\"submitting\">\n          {{ editingTask ? '更新' : '创建' }}\n        </el-button>\n      </template>\n    </el-dialog>\n    \n    <!-- 任务详情对话框 -->\n    <el-dialog v-model=\"showDetailDialog\" title=\"任务详情\" width=\"700px\">\n      <div v-if=\"selectedTask\" class=\"task-detail\">\n        <el-descriptions :column=\"2\" border>\n          <el-descriptions-item label=\"任务标题\" :span=\"2\">{{ selectedTask.title }}</el-descriptions-item>\n          <el-descriptions-item label=\"任务状态\">\n            <el-tag :type=\"getStatusColor(selectedTask.status)\">\n              {{ getStatusText(selectedTask.status) }}\n            </el-tag>\n          </el-descriptions-item>\n          <el-descriptions-item label=\"优先级\">\n            <el-tag :type=\"getPriorityColor(selectedTask.priority)\">\n              {{ getPriorityText(selectedTask.priority) }}\n            </el-tag>\n          </el-descriptions-item>\n          <el-descriptions-item label=\"创建人\">{{ selectedTask.creator?.realName }}</el-descriptions-item>\n          <el-descriptions-item label=\"创建时间\">{{ formatDate(selectedTask.createTime) }}</el-descriptions-item>\n          <el-descriptions-item label=\"截止时间\">{{ formatDate(selectedTask.deadline) }}</el-descriptions-item>\n          <el-descriptions-item label=\"任务描述\" :span=\"2\">{{ selectedTask.content || selectedTask.description || '暂无描述' }}</el-descriptions-item>\n        </el-descriptions>\n        \n        <!-- 进度更新 -->\n        <div v-if=\"selectedTask.status === 'IN_PROGRESS'\" class=\"progress-update\">\n          <h4>更新进度</h4>\n          <el-slider\n            v-model=\"progressValue\"\n            :max=\"100\"\n            :step=\"5\"\n            show-stops\n            show-input\n            @change=\"updateProgress\"\n          />\n        </div>\n      </div>\n      \n      <template #footer>\n        <el-button @click=\"showDetailDialog = false\">关闭</el-button>\n      </template>\n    </el-dialog>\n\n    <!-- 任务提交对话框 -->\n    <el-dialog\n      v-model=\"showSubmitTaskDialog\"\n      title=\"提交任务\"\n      width=\"600px\"\n    >\n      <el-form\n        ref=\"submitFormRef\"\n        :model=\"submitForm\"\n        :rules=\"submitFormRules\"\n        label-width=\"100px\"\n      >\n        <el-form-item label=\"任务名称\">\n          <span>{{ selectedTask?.title }}</span>\n        </el-form-item>\n\n        <el-form-item label=\"提交说明\" prop=\"content\">\n          <el-input\n            v-model=\"submitForm.content\"\n            type=\"textarea\"\n            :rows=\"5\"\n            placeholder=\"请详细描述您的任务完成情况...\"\n          />\n        </el-form-item>\n\n        <el-form-item label=\"提交文件\">\n          <el-upload\n            ref=\"uploadRef\"\n            :file-list=\"submitForm.fileList\"\n            :on-change=\"handleFileChange\"\n            :on-remove=\"handleFileRemove\"\n            :before-upload=\"beforeUpload\"\n            :auto-upload=\"false\"\n            multiple\n            drag\n          >\n            <el-icon class=\"el-icon--upload\"><upload-filled /></el-icon>\n            <div class=\"el-upload__text\">\n              将文件拖到此处，或<em>点击上传</em>\n            </div>\n            <template #tip>\n              <div class=\"el-upload__tip\">\n                支持多个文件上传，单个文件大小不超过10MB\n              </div>\n            </template>\n          </el-upload>\n        </el-form-item>\n      </el-form>\n\n      <template #footer>\n        <el-button @click=\"showSubmitTaskDialog = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"submitTaskCompletion\" :loading=\"submitting\">\n          提交\n        </el-button>\n      </template>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { ref, reactive, onMounted, computed, watch, nextTick } from 'vue'\nimport { useStore } from 'vuex'\nimport { useRouter } from 'vue-router'\nimport { recordAPI, teamAPI, projectAPI } from '@/api'\nimport { ElMessage, ElMessageBox } from 'element-plus'\nimport { Plus, ArrowDown, UploadFilled, Refresh, Calendar, User } from '@element-plus/icons-vue'\n\nexport default {\n  name: 'TaskManagementView',\n  components: {\n    Plus,\n    ArrowDown,\n    UploadFilled,\n    Refresh,\n    Calendar,\n    User\n  },\n  setup() {\n    const store = useStore()\n    const router = useRouter()\n    const formRef = ref()\n    \n    const loading = ref(false)\n    const submitting = ref(false)\n    const showCreateDialog = ref(false)\n    const showDetailDialog = ref(false)\n    const showSubmitTaskDialog = ref(false)\n    const editingTask = ref(null)\n    const selectedTask = ref(null)\n    const progressValue = ref(0)\n\n    // 任务提交表单\n    const submitForm = reactive({\n      content: '',\n      fileList: []\n    })\n    const submitFormRules = {\n      content: [\n        { required: true, message: '请输入提交说明', trigger: 'blur' }\n      ]\n    }\n    \n    const tasks = ref([])\n    const myTeams = ref([])\n    const currentTeamId = ref('')\n    const activeTab = ref('all')\n    const currentPage = ref(1)\n    const pageSize = ref(10)\n    const total = ref(0)\n    \n    const taskForm = reactive({\n      title: '',\n      description: '',\n      priority: 'MEDIUM',\n      deadline: null\n    })\n    \n    const formRules = {\n      title: [\n        { required: true, message: '请输入任务标题', trigger: 'blur' },\n        { min: 2, max: 100, message: '标题长度在 2 到 100 个字符', trigger: 'blur' }\n      ],\n      description: [\n        { required: true, message: '请输入任务描述', trigger: 'blur' }\n      ],\n      deadline: [\n        { required: true, message: '请选择截止时间', trigger: 'change' }\n      ]\n    }\n    \n    const currentUser = computed(() => store.getters.currentUser)\n    const isTeacher = computed(() => currentUser.value?.role === 'TEACHER')\n\n    // 跳转到任务发布页面\n    const goToTaskPublish = () => {\n      router.push('/dashboard/task-publish')\n    }\n    \n    // 任务统计\n    const taskStats = computed(() => {\n      const total = tasks.value.length\n      const completed = tasks.value.filter(t => t.status === 'COMPLETED').length\n      const inProgress = tasks.value.filter(t => t.status === 'IN_PROGRESS').length\n      const completionRate = total > 0 ? Math.round((completed / total) * 100) : 0\n\n      return {\n        total,\n        completed,\n        inProgress,\n        completionRate\n      }\n    })\n\n    // 教师端：按项目和团队分组的任务数据\n    const groupedTasks = computed(() => {\n      if (!isTeacher.value || tasks.value.length === 0) {\n        return []\n      }\n\n      // 按项目分组\n      const projectGroups = {}\n\n      tasks.value.forEach(task => {\n        const projectId = task.projectId || 'unknown'\n        const projectName = task.projectName || '未知项目'\n        const teamId = task.teamId || 'unknown'\n        const teamName = task.teamName || '未知团队'\n\n        if (!projectGroups[projectId]) {\n          projectGroups[projectId] = {\n            projectId,\n            projectName,\n            teams: {},\n            totalTasks: 0,\n            completedTasks: 0\n          }\n        }\n\n        if (!projectGroups[projectId].teams[teamId]) {\n          projectGroups[projectId].teams[teamId] = {\n            teamId,\n            teamName,\n            tasks: []\n          }\n        }\n\n        projectGroups[projectId].teams[teamId].tasks.push(task)\n        projectGroups[projectId].totalTasks++\n\n        if (task.status === 'COMPLETED') {\n          projectGroups[projectId].completedTasks++\n        }\n      })\n\n      // 转换为数组格式\n      return Object.values(projectGroups).map(project => ({\n        ...project,\n        teams: Object.values(project.teams)\n      }))\n    })\n    \n    // 加载我的团队\n    const loadMyTeams = async () => {\n      try {\n        console.log('=== 团队加载调试信息 ===')\n        console.log('当前用户:', currentUser.value)\n        console.log('用户角色:', currentUser.value?.role)\n\n        if (currentUser.value?.role === 'TEACHER') {\n          // 教师：获取自己项目的团队\n          await loadTeacherProjectTeams()\n        } else {\n          // 学生：获取参与的团队\n          console.log('🔍 学生获取团队列表...')\n          const response = await teamAPI.getJoinedTeams()\n          console.log('✅ 学生团队响应:', response)\n          myTeams.value = response?.records || []\n          console.log('📋 学生团队列表:', myTeams.value.map(team => ({\n            id: team.id,\n            name: team.name,\n            projectId: team.projectId,\n            projectName: team.projectName,\n            status: team.status\n          })))\n        }\n\n        console.log('团队列表:', myTeams.value)\n        console.log('团队数量:', myTeams.value.length)\n\n        if (myTeams.value.length > 0) {\n          currentTeamId.value = myTeams.value[0].id\n          console.log('选中的团队ID:', currentTeamId.value)\n          loadTasks()\n        } else {\n          console.log('没有找到任何团队')\n        }\n      } catch (error) {\n        console.error('加载团队列表失败:', error)\n        ElMessage.error('加载团队列表失败')\n      }\n    }\n\n    // 加载教师项目的团队\n    const loadTeacherProjectTeams = async () => {\n      try {\n        // 1. 先获取教师自己的项目列表\n        const projectsResponse = await projectAPI.getMyProjects()\n        console.log('教师项目响应:', projectsResponse)\n        const myProjects = projectsResponse?.records || []\n        console.log('教师项目列表:', myProjects)\n\n        if (myProjects.length === 0) {\n          console.log('教师没有发布任何项目')\n          myTeams.value = []\n          return\n        }\n\n        // 2. 获取每个项目的团队列表\n        const allTeams = []\n        for (const project of myProjects) {\n          try {\n            console.log(`=== 开始获取项目 ${project.name} (ID: ${project.id}) 的团队 ===`)\n            const teamsResponse = await teamAPI.getProjectTeams(project.id)\n            console.log(`项目 ${project.name} 的团队响应:`, teamsResponse)\n            console.log('响应类型:', typeof teamsResponse)\n            console.log('响应结构:', JSON.stringify(teamsResponse, null, 2))\n\n            const projectTeams = teamsResponse?.records || []\n            console.log(`项目 ${project.name} 的团队数量:`, projectTeams.length)\n\n            // 为每个团队添加项目信息，便于显示\n            const teamsWithProject = projectTeams.map(team => ({\n              ...team,\n              projectName: project.name,\n              projectId: project.id\n            }))\n\n            allTeams.push(...teamsWithProject)\n            console.log(`项目 ${project.name} 处理完成，累计团队数:`, allTeams.length)\n          } catch (error) {\n            console.error(`=== 获取项目 ${project.name} 的团队失败 ===`)\n            console.error('错误对象:', error)\n            console.error('错误消息:', error.message)\n            console.error('错误响应:', error.response)\n            console.error('错误状态:', error.response?.status)\n            console.error('错误数据:', error.response?.data)\n          }\n        }\n\n        console.log('教师所有项目的团队:', allTeams)\n        myTeams.value = allTeams\n\n      } catch (error) {\n        console.error('加载教师项目团队失败:', error)\n        throw error\n      }\n    }\n\n    // 加载任务列表\n    const loadTasks = async () => {\n      if (!currentTeamId.value) {\n        console.log('❌ 没有选择团队，无法加载任务')\n        return\n      }\n\n      try {\n        loading.value = true\n        const params = {\n          page: currentPage.value,\n          size: pageSize.value,\n          type: 'TASK',  // 只获取任务类型的记录\n          sortBy: 'createTime',\n          sortDir: 'desc'\n        }\n\n        console.log('=== 任务加载调试信息 ===')\n        console.log('当前用户:', currentUser.value)\n        console.log('当前团队ID:', currentTeamId.value)\n        console.log('所有团队:', myTeams.value)\n        console.log('请求参数:', params)\n        console.log('请求URL:', `/records/teams/${currentTeamId.value}`)\n\n        // 获取团队任务\n        const response = await recordAPI.getTeamRecords(currentTeamId.value, params)\n        console.log('✅ 团队任务API响应:', response)\n        console.log('响应数据结构:', {\n          hasRecords: !!response?.records,\n          recordsLength: response?.records?.length || 0,\n          totalElements: response?.totalElements,\n          totalPages: response?.totalPages,\n          currentPage: response?.currentPage\n        })\n\n        let allTasks = response?.records || []\n        console.log('📋 获取到的任务数量:', allTasks.length)\n\n        // 显示任务的详细信息\n        if (allTasks.length > 0) {\n          console.log('任务详情:', allTasks.map(task => ({\n            id: task.id,\n            title: task.title,\n            type: task.type,\n            status: task.status,\n            teamId: task.teamId,\n            projectId: task.projectId\n          })))\n\n          console.log('任务状态分布:', allTasks.reduce((acc, task) => {\n            acc[task.status] = (acc[task.status] || 0) + 1\n            return acc\n          }, {}))\n        } else {\n          console.log('❌ 没有获取到任何任务数据')\n\n          // 尝试获取所有任务进行对比\n          try {\n            console.log('🔍 尝试获取所有任务进行对比...')\n            const allTasksResponse = await recordAPI.getRecords({ type: 'TASK', page: 1, size: 50 })\n            console.log('所有任务响应:', allTasksResponse)\n            const allTasksInSystem = allTasksResponse?.records || []\n            console.log('系统中所有任务:', allTasksInSystem.map(task => ({\n              id: task.id,\n              title: task.title,\n              teamId: task.teamId,\n              projectId: task.projectId,\n              status: task.status,\n              creator: task.creator?.realName\n            })))\n\n            // 调用调试接口\n            console.log('🔍 调用调试接口获取任务详情...')\n            const debugResponse = await fetch('/api/records/debug/tasks', {\n              headers: {\n                'Authorization': `Bearer ${localStorage.getItem('token')}`\n              }\n            })\n            const debugData = await debugResponse.json()\n            console.log('调试接口响应:', debugData)\n          } catch (error) {\n            console.log('获取所有任务失败:', error)\n          }\n        }\n\n\n\n        // 为缺少status字段的任务添加默认状态\n        allTasks = allTasks.map(task => ({\n          ...task,\n          status: task.status || 'PUBLISHED',  // 默认状态为PUBLISHED（已发布）\n          deadline: task.dueDate || null,  // 使用dueDate字段\n          assignee: task.assignee || null,\n          progress: task.progress || 0\n        }))\n\n        // 前端过滤任务状态\n        console.log('当前选中的标签页:', activeTab.value)\n        console.log('过滤前所有任务:', allTasks.map(task => ({ id: task.id, title: task.title, status: task.status })))\n\n        if (activeTab.value !== 'all') {\n          const filteredTasks = allTasks.filter(task => task.status === activeTab.value)\n          console.log(`过滤前任务数量: ${allTasks.length}, 过滤后任务数量: ${filteredTasks.length}`)\n          console.log('过滤后的任务:', filteredTasks.map(task => ({ id: task.id, title: task.title, status: task.status })))\n          allTasks = filteredTasks\n        }\n\n        // 强制触发响应式更新\n        tasks.value = []\n        await nextTick()\n        tasks.value = [...allTasks]\n        total.value = allTasks.length\n\n      } catch (error) {\n        console.error('=== 任务加载错误信息 ===')\n        console.error('错误对象:', error)\n        console.error('错误消息:', error.message)\n        console.error('错误响应:', error.response)\n        console.error('错误状态:', error.response?.status)\n        console.error('错误数据:', error.response?.data)\n\n        ElMessage.error('加载任务失败: ' + (error.response?.data?.message || error.message || '未知错误'))\n      } finally {\n        loading.value = false\n      }\n    }\n    \n    // 提交任务\n    const submitTask = async () => {\n      if (!formRef.value) return\n\n      try {\n        await formRef.value.validate()\n        submitting.value = true\n\n        const taskData = {\n          title: taskForm.title,\n          content: taskForm.description,\n          priority: getPriorityNumber(taskForm.priority),\n          dueDate: taskForm.deadline ? new Date(taskForm.deadline).toISOString() : null\n        }\n\n        // 创建任务时需要额外的字段\n        if (!editingTask.value) {\n          taskData.type = 'TASK'\n          taskData.teamId = currentTeamId.value\n        }\n\n        console.log('提交任务数据:', taskData)\n        console.log('是否为编辑模式:', !!editingTask.value)\n\n        if (editingTask.value) {\n          console.log('更新任务ID:', editingTask.value.id)\n          await recordAPI.updateRecord(editingTask.value.id, taskData)\n          ElMessage.success('任务更新成功')\n        } else {\n          console.log('创建新任务')\n          await recordAPI.createRecord(taskData)\n          ElMessage.success('任务创建成功')\n        }\n\n        showCreateDialog.value = false\n        resetForm()\n        loadTasks()\n      } catch (error) {\n        console.error('提交任务失败:', error)\n        console.error('错误详情:', error.response?.data)\n        const errorMessage = error.response?.data?.message || error.message || '提交任务失败'\n        ElMessage.error(errorMessage)\n      } finally {\n        submitting.value = false\n      }\n    }\n    \n    // 重置表单\n    const resetForm = () => {\n      Object.assign(taskForm, {\n        title: '',\n        description: '',\n        priority: 'MEDIUM',\n        deadline: null\n      })\n      editingTask.value = null\n    }\n    \n    // 查看任务详情\n    const viewTask = (task) => {\n      selectedTask.value = task\n      progressValue.value = task.progress || 0\n      showDetailDialog.value = true\n    }\n    \n    // 编辑任务\n    const editTask = (task) => {\n      editingTask.value = task\n      Object.assign(taskForm, {\n        title: task.title,\n        description: task.content,\n        priority: task.priority,\n        deadline: task.dueDate ? new Date(task.dueDate) : null\n      })\n      showCreateDialog.value = true\n    }\n    \n    // 处理任务操作\n    const handleTaskAction = async ({ action, task }) => {\n      try {\n        let message = ''\n        let status = ''\n        \n        switch (action) {\n          case 'start':\n            status = 'IN_PROGRESS'\n            message = '任务已开始'\n            break\n          case 'submit':\n            // 学生提交任务，显示提交对话框\n            showSubmitDialog(task)\n            return\n          case 'approve':\n            // 教师审核通过任务\n            const approveParams = new URLSearchParams()\n            approveParams.append('approved', 'true')\n            await recordAPI.reviewTask(task.id, approveParams)\n            ElMessage.success('任务审核通过')\n            loadTasks()\n            return\n          case 'reject':\n            // 教师拒绝任务，需要提供反馈\n            const feedback = await ElMessageBox.prompt('请输入拒绝理由', '任务审核', {\n              confirmButtonText: '确定',\n              cancelButtonText: '取消',\n              inputType: 'textarea'\n            })\n            const rejectParams = new URLSearchParams()\n            rejectParams.append('approved', 'false')\n            rejectParams.append('feedback', feedback.value || '')\n            await recordAPI.reviewTask(task.id, rejectParams)\n            ElMessage.success('任务已拒绝，学生需要重新完成')\n            loadTasks()\n            return\n          case 'activate':\n            // 教师激活任务\n            await recordAPI.updateTaskStatus(task.id, 'ACTIVE')\n            ElMessage.success('任务已激活')\n            loadTasks()\n            return\n          case 'cancel':\n            status = 'CANCELLED'\n            message = '任务已取消'\n            break\n          case 'delete':\n            await ElMessageBox.confirm('确定要删除这个任务吗？', '确认删除', {\n              confirmButtonText: '确定',\n              cancelButtonText: '取消',\n              type: 'warning'\n            })\n            await recordAPI.deleteRecord(task.id)\n            ElMessage.success('任务删除成功')\n            loadTasks()\n            return\n        }\n        \n        if (status) {\n          await recordAPI.updateRecord(task.id, { status })\n          ElMessage.success(message)\n          loadTasks()\n        }\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('任务操作失败:', error)\n          ElMessage.error('操作失败')\n        }\n      }\n    }\n    \n    // 更新进度\n    const updateProgress = async (value) => {\n      if (!selectedTask.value) return\n\n      try {\n        await recordAPI.updateRecord(selectedTask.value.id, { progress: value })\n        ElMessage.success('进度更新成功')\n        loadTasks()\n      } catch (error) {\n        console.error('更新进度失败:', error)\n        ElMessage.error('更新进度失败')\n      }\n    }\n\n    // 开始任务\n    const startTask = async (task) => {\n      try {\n        await ElMessageBox.confirm(\n          `确定要开始任务\"${task.title}\"吗？开始后任务状态将变为活跃状态。`,\n          '开始任务',\n          {\n            confirmButtonText: '开始',\n            cancelButtonText: '取消',\n            type: 'info'\n          }\n        )\n\n        // 调用API更新任务状态为ACTIVE\n        await recordAPI.updateTaskStatus(task.id, 'ACTIVE')\n        ElMessage.success('任务已开始')\n        loadTasks() // 刷新任务列表\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('开始任务失败:', error)\n          ElMessage.error('开始任务失败')\n        }\n      }\n    }\n\n    // 显示任务提交对话框\n    const showSubmitDialog = (task) => {\n      selectedTask.value = task\n      submitForm.content = ''\n      submitForm.fileList = []\n      showSubmitTaskDialog.value = true\n    }\n\n    // 文件上传处理\n    const uploadRef = ref()\n\n    const handleFileChange = (_, fileList) => {\n      submitForm.fileList = fileList\n    }\n\n    const handleFileRemove = (_, fileList) => {\n      submitForm.fileList = fileList\n    }\n\n    const beforeUpload = (file) => {\n      const isLt10M = file.size / 1024 / 1024 < 10\n      if (!isLt10M) {\n        ElMessage.error('文件大小不能超过10MB!')\n        return false\n      }\n      return true\n    }\n\n    // 提交任务完成\n    const submitTaskCompletion = async () => {\n      try {\n        submitting.value = true\n\n        // 使用FormData支持文件上传\n        const formData = new FormData()\n        formData.append('submissionContent', submitForm.content)\n\n        // 添加文件\n        if (submitForm.fileList && submitForm.fileList.length > 0) {\n          submitForm.fileList.forEach((fileItem) => {\n            if (fileItem.raw) {\n              formData.append('files', fileItem.raw)\n            }\n          })\n        }\n\n        await recordAPI.submitTaskWithFiles(selectedTask.value.id, formData)\n\n        ElMessage.success('任务提交成功')\n        showSubmitTaskDialog.value = false\n        loadTasks()\n      } catch (error) {\n        console.error('任务提交失败:', error)\n        ElMessage.error('任务提交失败')\n      } finally {\n        submitting.value = false\n      }\n    }\n    \n    // 权限检查\n    const canEdit = (task) => {\n      return task.creator?.id === currentUser.value?.id || currentUser.value?.role === 'TEACHER'\n    }\n\n    const canManage = (task) => {\n      return task.creator?.id === currentUser.value?.id || currentUser.value?.role === 'TEACHER'\n    }\n    \n    // 工具方法\n    const formatDate = (date) => {\n      if (!date) return ''\n      return new Date(date).toLocaleString('zh-CN')\n    }\n    \n    const isOverdue = (deadline) => {\n      return new Date(deadline) < new Date()\n    }\n    \n    const getStatusColor = (status) => {\n      const colorMap = {\n        'ACTIVE': 'success',\n        'PUBLISHED': 'info',\n        'IN_PROGRESS': 'primary',\n        'SUBMITTED': 'warning',\n        'COMPLETED': 'success',\n        'CANCELLED': 'danger',\n        'REJECTED': 'danger',\n        'APPROVED': 'success',\n        'DRAFT': 'info'\n      }\n      return colorMap[status] || 'info'  // 默认返回 'info' 而不是空字符串\n    }\n\n    const getStatusText = (status) => {\n      const textMap = {\n        'ACTIVE': '活跃',\n        'PUBLISHED': '已发布',\n        'IN_PROGRESS': '进行中',\n        'SUBMITTED': '待审核',\n        'COMPLETED': '已完成',\n        'CANCELLED': '已取消',\n        'REJECTED': '已拒绝',\n        'APPROVED': '已通过',\n        'DRAFT': '草稿'\n      }\n      return textMap[status] || status\n    }\n    \n    const getPriorityColor = (priority) => {\n      // 处理数字类型的优先级\n      let priorityKey = priority\n      if (typeof priority === 'number') {\n        const numberToEnum = {\n          1: 'LOW',\n          2: 'MEDIUM',\n          3: 'HIGH',\n          4: 'URGENT',\n          5: 'URGENT'\n        }\n        priorityKey = numberToEnum[priority] || 'MEDIUM'\n      }\n\n      const colorMap = {\n        'LOW': 'info',\n        'MEDIUM': 'primary',\n        'HIGH': 'warning',\n        'URGENT': 'danger'\n      }\n      return colorMap[priorityKey] || 'info'  // 默认返回 'info' 而不是空字符串\n    }\n\n    const getPriorityText = (priority) => {\n      // 处理数字类型的优先级\n      let priorityKey = priority\n      if (typeof priority === 'number') {\n        const numberToEnum = {\n          1: 'LOW',\n          2: 'MEDIUM',\n          3: 'HIGH',\n          4: 'URGENT',\n          5: 'URGENT'\n        }\n        priorityKey = numberToEnum[priority] || 'MEDIUM'\n      }\n\n      const textMap = {\n        'LOW': '低',\n        'MEDIUM': '中',\n        'HIGH': '高',\n        'URGENT': '紧急'\n      }\n      return textMap[priorityKey] || '中'  // 默认返回 '中'\n    }\n\n    const getPriorityNumber = (priority) => {\n      const numberMap = {\n        'LOW': 1,\n        'MEDIUM': 2,\n        'HIGH': 3,\n        'URGENT': 4\n      }\n      return numberMap[priority] || 2\n    }\n    \n    // 监听团队变化\n    watch(currentTeamId, (newTeamId) => {\n      if (newTeamId) {\n        loadTasks()\n      }\n    })\n\n\n    \n\n\n    onMounted(() => {\n      loadTasks()\n      loadMyTeams()\n    })\n    \n    return {\n      loading,\n      submitting,\n      showCreateDialog,\n      showDetailDialog,\n      showSubmitTaskDialog,\n      editingTask,\n      selectedTask,\n      progressValue,\n      submitForm,\n      submitFormRules,\n      tasks,\n      myTeams,\n      currentTeamId,\n      activeTab,\n      currentPage,\n      pageSize,\n      total,\n      taskForm,\n      formRules,\n      formRef,\n      currentUser,\n      isTeacher,\n      taskStats,\n      groupedTasks,\n      goToTaskPublish,\n      loadTasks,\n      submitTask,\n      viewTask,\n      editTask,\n      handleTaskAction,\n      updateProgress,\n      startTask,\n      showSubmitDialog,\n      submitTaskCompletion,\n      uploadRef,\n      handleFileChange,\n      handleFileRemove,\n      beforeUpload,\n      canEdit,\n      canManage,\n      formatDate,\n      isOverdue,\n      getStatusColor,\n      getStatusText,\n      getPriorityColor,\n      getPriorityText,\n      getPriorityNumber\n    }\n  }\n}\n</script>\n\n<style scoped>\n.task-management {\n  padding: 0;\n}\n\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.card-header h3 {\n  margin: 0;\n}\n\n.header-actions {\n  display: flex;\n  gap: 12px;\n  align-items: center;\n}\n\n.task-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\n  gap: 20px;\n  margin-top: 16px;\n}\n\n.task-card {\n  cursor: pointer;\n  transition: all 0.3s ease;\n  height: 100%;\n}\n\n.task-card:hover {\n  transform: translateY(-4px);\n}\n\n.task-card .el-card {\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n}\n\n.task-card .el-card__body {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  min-height: 200px; /* 设置最小高度，确保卡片一致性 */\n}\n\n.task-card h4 {\n  margin: 0 0 10px 0;\n  color: #303133;\n}\n\n.task-description {\n  color: #606266;\n  margin: 10px 0;\n  display: -webkit-box;\n  -webkit-line-clamp: 2; /* 只显示2行 */\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  line-height: 1.5;\n  height: 3em; /* 固定高度为2行 (1.5 * 2 = 3em) */\n  flex: none; /* 不允许弹性伸缩，保持固定高度 */\n}\n\n.task-meta {\n  display: flex;\n  gap: 8px;\n  margin: 16px 0;\n  padding-top: 12px;\n  border-top: 1px solid #f0f0f0;\n}\n\n.task-info {\n  margin: 12px 0;\n}\n\n.info-item {\n  display: flex;\n  align-items: center;\n  gap: 6px;\n  margin-bottom: 6px;\n  color: #909399;\n  font-size: 13px;\n}\n\n.task-footer {\n  margin-top: auto;\n  display: flex;\n  justify-content: flex-end;\n  align-items: center;\n  gap: 8px; /* 统一按钮间距 */\n  flex-wrap: wrap; /* 允许按钮换行 */\n}\n\n.pagination {\n  margin-top: 20px;\n  text-align: center;\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAiB;;EAGjBA,KAAK,EAAC;AAAa;;EAEjBA,KAAK,EAAC;AAAgB;;EAoB1BA,KAAK,EAAC;AAAY;;EAoBlBA,KAAK,EAAC;AAAW;;;EAWuBA,KAAK,EAAC;;;;EAMxCC,KAA0D,EAA1D;IAAA;IAAA;IAAA;EAAA;;;;EAOGD,KAAK,EAAC;;;EAITA,KAAK,EAAC;AAAkB;;EACtBA,KAAK,EAAC;AAAW;;EAQjBA,KAAK,EAAC;AAAW;;;EACfA,KAAK,EAAC;;;;EAINA,KAAK,EAAC;;;EAKRA,KAAK,EAAC;AAAa;;;EA8EVA,KAAK,EAAC;;;;EAwEHA,KAAK,EAAC;;;;EAoBqBA,KAAK,EAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uBAtQ9DE,mBAAA,CAgVM,OAhVNC,UAgVM,GA/UJC,YAAA,CAoLUC,kBAAA;IAnLGC,MAAM,EAAAC,QAAA,CACf,MAkBM,CAlBNC,mBAAA,CAkBM,OAlBNC,UAkBM,G,4BAjBJD,mBAAA,CAAa,YAAT,MAAI,qBACRA,mBAAA,CAeM,OAfNE,UAeM,GAdJN,YAAA,CAOYO,oBAAA;kBAPQC,MAAA,CAAAC,aAAa;iEAAbD,MAAA,CAAAC,aAAa,GAAAC,MAAA;MAAEC,WAAW,EAAC,MAAM;MAAEC,QAAM,EAAEJ,MAAA,CAAAK,SAAS;MAAEhB,KAAqB,EAArB;QAAA;MAAA;;wBAEtE,MAAuB,E,kBADzBC,mBAAA,CAKEgB,SAAA,QAAAC,WAAA,CAJeP,MAAA,CAAAQ,OAAO,EAAfC,IAAI;6BADbC,YAAA,CAKEC,oBAAA;UAHCC,GAAG,EAAEH,IAAI,CAACI,EAAE;UACZC,KAAK,EAAEL,IAAI,CAACM,WAAW,MAAMN,IAAI,CAACO,IAAI,KAAKP,IAAI,CAACM,WAAW,MAAMN,IAAI,CAACO,IAAI;UAC1EC,KAAK,EAAER,IAAI,CAACI;;;;mDAGAb,MAAA,CAAAkB,SAAS,I,cAA1BR,YAAA,CAEYS,oBAAA;;MAFgBC,IAAI,EAAC,SAAS;MAAEC,OAAK,EAAErB,MAAA,CAAAsB,eAAe;MAAGC,IAAI,EAAEC,IAAA,CAAAC;;wBAAM,MAEjFC,MAAA,SAAAA,MAAA,Q,iBAFiF,QAEjF,E;;;iFACAlC,YAAA,CAEY2B,oBAAA;MAFAE,OAAK,EAAErB,MAAA,CAAAK,SAAS;MAAGsB,OAAO,EAAE3B,MAAA,CAAA2B,OAAO;MAAGJ,IAAI,EAAEC,IAAA,CAAAI;;wBAAS,MAEjEF,MAAA,SAAAA,MAAA,Q,iBAFiE,MAEjE,E;;;;sBAMN,MAeM,CAfN9B,mBAAA,CAeM,OAfNiC,UAeM,GAdJrC,YAAA,CAaSsC,iBAAA;MAbAC,MAAM,EAAE;IAAE;wBACjB,MAES,CAFTvC,YAAA,CAESwC,iBAAA;QAFAC,IAAI,EAAE;MAAC;0BACd,MAAsD,CAAtDzC,YAAA,CAAsD0C,uBAAA;UAAxCC,KAAK,EAAC,MAAM;UAAElB,KAAK,EAAEjB,MAAA,CAAAoC,SAAS,CAACC;;;UAE/C7C,YAAA,CAESwC,iBAAA;QAFAC,IAAI,EAAE;MAAC;0BACd,MAA0D,CAA1DzC,YAAA,CAA0D0C,uBAAA;UAA5CC,KAAK,EAAC,KAAK;UAAElB,KAAK,EAAEjB,MAAA,CAAAoC,SAAS,CAACE;;;UAE9C9C,YAAA,CAESwC,iBAAA;QAFAC,IAAI,EAAE;MAAC;0BACd,MAAyD,CAAzDzC,YAAA,CAAyD0C,uBAAA;UAA3CC,KAAK,EAAC,KAAK;UAAElB,KAAK,EAAEjB,MAAA,CAAAoC,SAAS,CAACG;;;UAE9C/C,YAAA,CAESwC,iBAAA;QAFAC,IAAI,EAAE;MAAC;0BACd,MAAyE,CAAzEzC,YAAA,CAAyE0C,uBAAA;UAA3DC,KAAK,EAAC,KAAK;UAAElB,KAAK,EAAEjB,MAAA,CAAAoC,SAAS,CAACI,cAAc;UAAEC,MAAM,EAAC;;;;;yCAQzEnD,mBAAA,CAyHM,OAzHNoD,UAyHM,GAxHJlD,YAAA,CAQUmD,kBAAA;kBARQ3C,MAAA,CAAA4C,SAAS;iEAAT5C,MAAA,CAAA4C,SAAS,GAAA1C,MAAA;MAAG2C,WAAU,EAAE7C,MAAA,CAAAK;;wBACxC,MAAuC,CAAvCb,YAAA,CAAuCsD,sBAAA;QAA1BhC,KAAK,EAAC,MAAM;QAACE,IAAI,EAAC;UAC/BxB,YAAA,CAA4CsD,sBAAA;QAA/BhC,KAAK,EAAC,KAAK;QAACE,IAAI,EAAC;UAC9BxB,YAAA,CAA0CsD,sBAAA;QAA7BhC,KAAK,EAAC,MAAM;QAACE,IAAI,EAAC;UAC/BxB,YAAA,CAA8CsD,sBAAA;QAAjChC,KAAK,EAAC,KAAK;QAACE,IAAI,EAAC;UAC9BxB,YAAA,CAA4CsD,sBAAA;QAA/BhC,KAAK,EAAC,KAAK;QAACE,IAAI,EAAC;UAC9BxB,YAAA,CAA4CsD,sBAAA;QAA/BhC,KAAK,EAAC,KAAK;QAACE,IAAI,EAAC;UAC9BxB,YAAA,CAA4CsD,sBAAA;QAA/BhC,KAAK,EAAC,KAAK;QAACE,IAAI,EAAC;;;sDAGrBhB,MAAA,CAAA+C,KAAK,CAACC,MAAM,WAAWhD,MAAA,CAAA2B,OAAO,I,cAAzCrC,mBAAA,CAWM,OAXN2D,UAWM,GAVJzD,YAAA,CASW0D,mBAAA;MATDC,WAAW,EAAC;IAAM;wBAQpC,MAKF,CAZ6BnD,MAAA,CAAAkB,SAAS,I,cAA1BR,YAAA,CAEYS,oBAAA;;QAFgBC,IAAI,EAAC,SAAS;QAAEC,OAAK,EAAErB,MAAA,CAAAsB;;0BAAiB,MAEpEI,MAAA,SAAAA,MAAA,Q,iBAFoE,WAEpE,E;;;uDAEEpC,mBAAA,CAEI,KAFJ8D,UAEI,EAF0D,YAE9D,G;;2BAKN9D,mBAAA,CAgGM,OAhGN+D,UAgGM,I,kBA/FJ/D,mBAAA,CA8FMgB,SAAA,QAAAC,WAAA,CA9FcP,MAAA,CAAA+C,KAAK,EAAbO,IAAI;2BAAhBhE,mBAAA,CA8FM;QA9FsBsB,GAAG,EAAE0C,IAAI,CAACzC,EAAE;QAAEzB,KAAK,EAAC;UAC9CI,YAAA,CA4FUC,kBAAA;QA5FD8D,MAAM,EAAC,OAAO;QAAElC,OAAK,EAAAnB,MAAA,IAAEF,MAAA,CAAAwD,QAAQ,CAACF,IAAI;;0BAC3C,MAAyB,CAAzB1D,mBAAA,CAAyB,YAAA6D,gBAAA,CAAlBH,IAAI,CAACnB,KAAK,kBACjBvC,mBAAA,CAAgF,KAAhF8D,UAAgF,EAAAD,gBAAA,CAAjDH,IAAI,CAACK,OAAO,IAAIL,IAAI,CAACH,WAAW,4BAC/DvD,mBAAA,CAOM,OAPNgE,WAOM,GANJpE,YAAA,CAESqE,iBAAA;UAFAzC,IAAI,EAAEpB,MAAA,CAAA8D,cAAc,CAACR,IAAI,CAACS,MAAM;UAAGC,IAAI,EAAC;;4BAC/C,MAAgC,C,kCAA7BhE,MAAA,CAAAiE,aAAa,CAACX,IAAI,CAACS,MAAM,kB;;uDAEhBT,IAAI,CAACY,QAAQ,I,cAA3BxD,YAAA,CAESmD,iBAAA;;UAFqBzC,IAAI,EAAEpB,MAAA,CAAAmE,gBAAgB,CAACb,IAAI,CAACY,QAAQ;UAAGF,IAAI,EAAC;;4BACxE,MAAoC,C,kCAAjChE,MAAA,CAAAoE,eAAe,CAACd,IAAI,CAACY,QAAQ,kB;;8FAGpCtE,mBAAA,CASM,OATNyE,WASM,GARyBf,IAAI,CAACgB,QAAQ,I,cAA1ChF,mBAAA,CAGM,OAHNiF,WAGM,GAFJ/E,YAAA,CAA+BgF,kBAAA;4BAAtB,MAAY,CAAZhF,YAAA,CAAYiF,mBAAA,E;;YACrB7E,mBAAA,CAA4C,cAAA6D,gBAAA,CAAnCzD,MAAA,CAAA0E,UAAU,CAACpB,IAAI,CAACgB,QAAQ,kB,wCAENhB,IAAI,CAACqB,QAAQ,I,cAA1CrF,mBAAA,CAGM,OAHNsF,WAGM,GAFJpF,YAAA,CAA2BgF,kBAAA;4BAAlB,MAAQ,CAARhF,YAAA,CAAQqF,eAAA,E;;YACjBjF,mBAAA,CAAgC,cAAA6D,gBAAA,CAAvBH,IAAI,CAACqB,QAAQ,iB,0CAG1B/E,mBAAA,CAsEM,OAtENkF,WAsEM,GArEJtF,YAAA,CAEY2B,oBAAA;UAFD6C,IAAI,EAAC,OAAO;UAAE3C,OAAK,EAAA0D,cAAA,CAAA7E,MAAA,IAAOF,MAAA,CAAAwD,QAAQ,CAACF,IAAI;;4BAAG,MAErD,KAAA5B,MAAA,SAAAA,MAAA,Q,iBAFqD,QAErD,E;;;0DAEAsD,mBAAA,UAAa,E,CACIhF,MAAA,CAAAkB,SAAS,I,cAA1B5B,mBAAA,CAiBWgB,SAAA;UAAAM,GAAA;QAAA,IAfD0C,IAAI,CAACS,MAAM,oB,cADnBrD,YAAA,CAOYS,oBAAA;;UALV6C,IAAI,EAAC,OAAO;UACZ5C,IAAI,EAAC,SAAS;UACbC,OAAK,EAAA0D,cAAA,CAAA7E,MAAA,IAAOF,MAAA,CAAAiF,SAAS,CAAC3B,IAAI;;4BAC5B,MAED,KAAA5B,MAAA,SAAAA,MAAA,Q,iBAFC,QAED,E;;;+FAEQ4B,IAAI,CAACS,MAAM,iBAAiBT,IAAI,CAACS,MAAM,sB,cAD/CrD,YAAA,CAOYS,oBAAA;;UALV6C,IAAI,EAAC,OAAO;UACZ5C,IAAI,EAAC,SAAS;UACbC,OAAK,EAAA0D,cAAA,CAAA7E,MAAA,IAAOF,MAAA,CAAAkF,gBAAgB,CAAC5B,IAAI;;4BACnC,MAED,KAAA5B,MAAA,SAAAA,MAAA,Q,iBAFC,QAED,E;;;gKAGFsD,mBAAA,UAAa,EACGhF,MAAA,CAAAkB,SAAS,I,cAAzB5B,mBAAA,CA2CWgB,SAAA;UAAAM,GAAA;QAAA,IA1CQZ,MAAA,CAAAmF,OAAO,CAAC7B,IAAI,K,cAA7B5C,YAAA,CAEYS,oBAAA;;UAFoB6C,IAAI,EAAC,OAAO;UAAE3C,OAAK,EAAA0D,cAAA,CAAA7E,MAAA,IAAOF,MAAA,CAAAoF,QAAQ,CAAC9B,IAAI;;4BAAG,MAE1E,KAAA5B,MAAA,SAAAA,MAAA,Q,iBAF0E,MAE1E,E;;;+FACmB1B,MAAA,CAAAqF,SAAS,CAAC/B,IAAI,K,cAAjC5C,YAAA,CAsCc4E,sBAAA;;UAtCuBC,SAAO,EAAEvF,MAAA,CAAAwF,gBAAgB;UAAGnE,OAAK,EAAAK,MAAA,QAAAA,MAAA,MAAAqD,cAAA,CAAN,QAAW;;UAI9DU,QAAQ,EAAA9F,QAAA,CACjB,MA+BmB,CA/BnBH,YAAA,CA+BmBkG,2BAAA;8BALrB,MASiC,CAjCrBpC,IAAI,CAACS,MAAM,oB,cADnBrD,YAAA,CAKmBiF,2BAAA;;cAHhBC,OAAO;gBAAAC,MAAA;gBAAsBvC;cAAI;;gCACnC,MAED,KAAA5B,MAAA,SAAAA,MAAA,Q,iBAFC,QAED,E;;;mGAEQ4B,IAAI,CAACS,MAAM,oB,cADnBrD,YAAA,CAKmBiF,2BAAA;;cAHhBC,OAAO;gBAAAC,MAAA;gBAAqBvC;cAAI;;gCAClC,MAED,KAAA5B,MAAA,SAAAA,MAAA,Q,iBAFC,QAED,E;;;mGAEQ4B,IAAI,CAACS,MAAM,oB,cADnBrD,YAAA,CAKmBiF,2BAAA;;cAHhBC,OAAO;gBAAAC,MAAA;gBAAuBvC;cAAI;;gCACpC,MAED,KAAA5B,MAAA,SAAAA,MAAA,Q,iBAFC,QAED,E;;;0IAE+CoE,QAAQ,CAACxC,IAAI,CAACS,MAAM,K,cADnErD,YAAA,CAKmBiF,2BAAA;;cAHhBC,OAAO;gBAAAC,MAAA;gBAAqBvC;cAAI;;gCAClC,MAED,KAAA5B,MAAA,SAAAA,MAAA,Q,iBAFC,QAED,E;;;mGACAlC,YAAA,CAKmBmG,2BAAA;cAJhBC,OAAO;gBAAAC,MAAA;gBAAqBvC;cAAI;cACjCyC,OAAO,EAAP;;gCACD,MAED,KAAArE,MAAA,SAAAA,MAAA,Q,iBAFC,QAED,E;;;;;;4BAlCJ,MAEY,CAFZlC,YAAA,CAEY2B,oBAAA;YAFD6C,IAAI,EAAC,OAAO;YAAEzC,IAAI,EAAEC,IAAA,CAAAwE;;8BAAW,MAE1C,KAAAtE,MAAA,SAAAA,MAAA,Q,iBAF0C,MAE1C,E;;;;;;;;gEAhFoB1B,MAAA,CAAA2B,OAAO,E,GA6H9B3B,MAAA,CAAAqC,KAAK,Q,cAAhB/C,mBAAA,CAUM,OAVN2G,WAUM,GATJzG,YAAA,CAQE0G,wBAAA;MAPQ,cAAY,EAAElG,MAAA,CAAAmG,WAAW;kEAAXnG,MAAA,CAAAmG,WAAW,GAAAjG,MAAA;MACzB,WAAS,EAAEF,MAAA,CAAAoG,QAAQ;+DAARpG,MAAA,CAAAoG,QAAQ,GAAAlG,MAAA;MAC1BmC,KAAK,EAAErC,MAAA,CAAAqC,KAAK;MACZ,YAAU,EAAE,YAAY;MACzBgE,MAAM,EAAC,yCAAyC;MAC/CC,YAAW,EAAEtG,MAAA,CAAAK,SAAS;MACtBkG,eAAc,EAAEvG,MAAA,CAAAK;;;MAKvB2E,mBAAA,gBAAmB,EACnBxF,YAAA,CAsDYgH,oBAAA;gBArDDxG,MAAA,CAAAyG,gBAAgB;iEAAhBzG,MAAA,CAAAyG,gBAAgB,GAAAvG,MAAA;IACxBiC,KAAK,EAAEnC,MAAA,CAAA0G,WAAW;IACnBC,KAAK,EAAC;;IA6CKC,MAAM,EAAAjH,QAAA,CACf,MAA2D,CAA3DH,YAAA,CAA2D2B,oBAAA;MAA/CE,OAAK,EAAAK,MAAA,QAAAA,MAAA,MAAAxB,MAAA,IAAEF,MAAA,CAAAyG,gBAAgB;;wBAAU,MAAE/E,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;QAC/ClC,YAAA,CAEY2B,oBAAA;MAFDC,IAAI,EAAC,SAAS;MAAEC,OAAK,EAAErB,MAAA,CAAA6G,UAAU;MAAGlF,OAAO,EAAE3B,MAAA,CAAA8G;;wBACtD,MAA+B,C,kCAA5B9G,MAAA,CAAA0G,WAAW,+B;;;sBA9ClB,MAyCU,CAzCVlH,YAAA,CAyCUuH,kBAAA;MAxCRC,GAAG,EAAC,SAAS;MACZC,KAAK,EAAEjH,MAAA,CAAAkH,QAAQ;MACfC,KAAK,EAAEnH,MAAA,CAAAoH,SAAS;MACjB,aAAW,EAAC;;wBAEZ,MAEe,CAFf5H,YAAA,CAEe6H,uBAAA;QAFDvG,KAAK,EAAC,MAAM;QAACwG,IAAI,EAAC;;0BAC9B,MAA2D,CAA3D9H,YAAA,CAA2D+H,mBAAA;sBAAxCvH,MAAA,CAAAkH,QAAQ,CAAC/E,KAAK;qEAAdnC,MAAA,CAAAkH,QAAQ,CAAC/E,KAAK,GAAAjC,MAAA;UAAEC,WAAW,EAAC;;;UAGjDX,YAAA,CAOe6H,uBAAA;QAPDvG,KAAK,EAAC,MAAM;QAACwG,IAAI,EAAC;;0BAC9B,MAKE,CALF9H,YAAA,CAKE+H,mBAAA;sBAJSvH,MAAA,CAAAkH,QAAQ,CAAC/D,WAAW;qEAApBnD,MAAA,CAAAkH,QAAQ,CAAC/D,WAAW,GAAAjD,MAAA;UAC7BkB,IAAI,EAAC,UAAU;UACdoG,IAAI,EAAE,CAAC;UACRrH,WAAW,EAAC;;;UAIhBX,YAAA,CAGe6H,uBAAA;QAHDvG,KAAK,EAAC;MAAK;0BACvB,MAA8D,CAA9DtB,YAAA,CAA8D+H,mBAAA;UAAnDtG,KAAK,EAAEjB,MAAA,CAAAyH,WAAW,EAAEC,QAAQ;UAAYC,QAAQ,EAAR;wEACnD/H,mBAAA,CAA4C;UAAvCR,KAAK,EAAC;QAAgB,GAAC,YAAU,oB;;;UAGxCI,YAAA,CAOe6H,uBAAA;QAPDvG,KAAK,EAAC,KAAK;QAACwG,IAAI,EAAC;;0BAC7B,MAKY,CALZ9H,YAAA,CAKYO,oBAAA;sBALQC,MAAA,CAAAkH,QAAQ,CAAChD,QAAQ;qEAAjBlE,MAAA,CAAAkH,QAAQ,CAAChD,QAAQ,GAAAhE,MAAA;UAAEC,WAAW,EAAC;;4BACjD,MAAmC,CAAnCX,YAAA,CAAmCmB,oBAAA;YAAxBG,KAAK,EAAC,GAAG;YAACG,KAAK,EAAC;cAC3BzB,YAAA,CAAsCmB,oBAAA;YAA3BG,KAAK,EAAC,GAAG;YAACG,KAAK,EAAC;cAC3BzB,YAAA,CAAoCmB,oBAAA;YAAzBG,KAAK,EAAC,GAAG;YAACG,KAAK,EAAC;cAC3BzB,YAAA,CAAuCmB,oBAAA;YAA5BG,KAAK,EAAC,IAAI;YAACG,KAAK,EAAC;;;;;UAIhCzB,YAAA,CAOe6H,uBAAA;QAPDvG,KAAK,EAAC,MAAM;QAACwG,IAAI,EAAC;;0BAC9B,MAKE,CALF9H,YAAA,CAKEoI,yBAAA;sBAJS5H,MAAA,CAAAkH,QAAQ,CAAC5C,QAAQ;qEAAjBtE,MAAA,CAAAkH,QAAQ,CAAC5C,QAAQ,GAAApE,MAAA;UAC1BkB,IAAI,EAAC,UAAU;UACfjB,WAAW,EAAC,SAAS;UACrBd,KAAmB,EAAnB;YAAA;UAAA;;;;;;;8CAaR2F,mBAAA,aAAgB,EAChBxF,YAAA,CAqCYgH,oBAAA;gBArCQxG,MAAA,CAAA6H,gBAAgB;iEAAhB7H,MAAA,CAAA6H,gBAAgB,GAAA3H,MAAA;IAAEiC,KAAK,EAAC,MAAM;IAACwE,KAAK,EAAC;;IAkC5CC,MAAM,EAAAjH,QAAA,CACf,MAA2D,CAA3DH,YAAA,CAA2D2B,oBAAA;MAA/CE,OAAK,EAAAK,MAAA,SAAAA,MAAA,OAAAxB,MAAA,IAAEF,MAAA,CAAA6H,gBAAgB;;wBAAU,MAAEnG,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;;sBAVjD,MAyCL,CAjEgB1B,MAAA,CAAA8H,YAAY,I,cAAvBxI,mBAAA,CA+BM,OA/BNyI,WA+BM,GA9BJvI,YAAA,CAgBkBwI,0BAAA;MAhBAC,MAAM,EAAE,CAAC;MAAEC,MAAM,EAAN;;wBAC3B,MAA4F,CAA5F1I,YAAA,CAA4F2I,+BAAA;QAAtErH,KAAK,EAAC,MAAM;QAAEmB,IAAI,EAAE;;0BAAG,MAAwB,C,kCAArBjC,MAAA,CAAA8H,YAAY,CAAC3F,KAAK,iB;;UAClE3C,YAAA,CAIuB2I,+BAAA;QAJDrH,KAAK,EAAC;MAAM;0BAChC,MAES,CAFTtB,YAAA,CAESqE,iBAAA;UAFAzC,IAAI,EAAEpB,MAAA,CAAA8D,cAAc,CAAC9D,MAAA,CAAA8H,YAAY,CAAC/D,MAAM;;4BAC/C,MAAwC,C,kCAArC/D,MAAA,CAAAiE,aAAa,CAACjE,MAAA,CAAA8H,YAAY,CAAC/D,MAAM,kB;;;;UAGxCvE,YAAA,CAIuB2I,+BAAA;QAJDrH,KAAK,EAAC;MAAK;0BAC/B,MAES,CAFTtB,YAAA,CAESqE,iBAAA;UAFAzC,IAAI,EAAEpB,MAAA,CAAAmE,gBAAgB,CAACnE,MAAA,CAAA8H,YAAY,CAAC5D,QAAQ;;4BACnD,MAA4C,C,kCAAzClE,MAAA,CAAAoE,eAAe,CAACpE,MAAA,CAAA8H,YAAY,CAAC5D,QAAQ,kB;;;;UAG5C1E,YAAA,CAA6F2I,+BAAA;QAAvErH,KAAK,EAAC;MAAK;0BAAC,MAAoC,C,kCAAjCd,MAAA,CAAA8H,YAAY,CAACM,OAAO,EAAEV,QAAQ,iB;;UACnElI,YAAA,CAAmG2I,+BAAA;QAA7ErH,KAAK,EAAC;MAAM;0BAAC,MAAyC,C,kCAAtCd,MAAA,CAAA0E,UAAU,CAAC1E,MAAA,CAAA8H,YAAY,CAACO,UAAU,kB;;UACxE7I,YAAA,CAAiG2I,+BAAA;QAA3ErH,KAAK,EAAC;MAAM;0BAAC,MAAuC,C,kCAApCd,MAAA,CAAA0E,UAAU,CAAC1E,MAAA,CAAA8H,YAAY,CAACxD,QAAQ,kB;;UACtE9E,YAAA,CAAoI2I,+BAAA;QAA9GrH,KAAK,EAAC,MAAM;QAAEmB,IAAI,EAAE;;0BAAG,MAAgE,C,kCAA7DjC,MAAA,CAAA8H,YAAY,CAACnE,OAAO,IAAI3D,MAAA,CAAA8H,YAAY,CAAC3E,WAAW,2B;;;;QAGlG6B,mBAAA,UAAa,EACFhF,MAAA,CAAA8H,YAAY,CAAC/D,MAAM,sB,cAA9BzE,mBAAA,CAUM,OAVNgJ,WAUM,G,4BATJ1I,mBAAA,CAAa,YAAT,MAAI,qBACRJ,YAAA,CAOE+I,oBAAA;kBANSvI,MAAA,CAAAwI,aAAa;mEAAbxI,MAAA,CAAAwI,aAAa,GAAAtI,MAAA;MACrBuI,GAAG,EAAE,GAAG;MACRC,IAAI,EAAE,CAAC;MACR,YAAU,EAAV,EAAU;MACV,YAAU,EAAV,EAAU;MACTtI,QAAM,EAAEJ,MAAA,CAAA2I;;;qCAUjB3D,mBAAA,aAAgB,EAChBxF,YAAA,CAsDYgH,oBAAA;gBArDDxG,MAAA,CAAA4I,oBAAoB;iEAApB5I,MAAA,CAAA4I,oBAAoB,GAAA1I,MAAA;IAC7BiC,KAAK,EAAC,MAAM;IACZwE,KAAK,EAAC;;IA6CKC,MAAM,EAAAjH,QAAA,CACf,MAA+D,CAA/DH,YAAA,CAA+D2B,oBAAA;MAAnDE,OAAK,EAAAK,MAAA,SAAAA,MAAA,OAAAxB,MAAA,IAAEF,MAAA,CAAA4I,oBAAoB;;wBAAU,MAAElH,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;QACnDlC,YAAA,CAEY2B,oBAAA;MAFDC,IAAI,EAAC,SAAS;MAAEC,OAAK,EAAErB,MAAA,CAAA6I,oBAAoB;MAAGlH,OAAO,EAAE3B,MAAA,CAAA8G;;wBAAY,MAE9EpF,MAAA,SAAAA,MAAA,Q,iBAF8E,MAE9E,E;;;;sBA/CF,MAyCU,CAzCVlC,YAAA,CAyCUuH,kBAAA;MAxCRC,GAAG,EAAC,eAAe;MAClBC,KAAK,EAAEjH,MAAA,CAAA8I,UAAU;MACjB3B,KAAK,EAAEnH,MAAA,CAAA+I,eAAe;MACvB,aAAW,EAAC;;wBAEZ,MAEe,CAFfvJ,YAAA,CAEe6H,uBAAA;QAFDvG,KAAK,EAAC;MAAM;0BACxB,MAAsC,CAAtClB,mBAAA,CAAsC,cAAA6D,gBAAA,CAA7BzD,MAAA,CAAA8H,YAAY,EAAE3F,KAAK,iB;;UAG9B3C,YAAA,CAOe6H,uBAAA;QAPDvG,KAAK,EAAC,MAAM;QAACwG,IAAI,EAAC;;0BAC9B,MAKE,CALF9H,YAAA,CAKE+H,mBAAA;sBAJSvH,MAAA,CAAA8I,UAAU,CAACnF,OAAO;uEAAlB3D,MAAA,CAAA8I,UAAU,CAACnF,OAAO,GAAAzD,MAAA;UAC3BkB,IAAI,EAAC,UAAU;UACdoG,IAAI,EAAE,CAAC;UACRrH,WAAW,EAAC;;;UAIhBX,YAAA,CAqBe6H,uBAAA;QArBDvG,KAAK,EAAC;MAAM;0BACxB,MAmBY,CAnBZtB,YAAA,CAmBYwJ,oBAAA;UAlBVhC,GAAG,EAAC,WAAW;UACd,WAAS,EAAEhH,MAAA,CAAA8I,UAAU,CAACG,QAAQ;UAC9B,WAAS,EAAEjJ,MAAA,CAAAkJ,gBAAgB;UAC3B,WAAS,EAAElJ,MAAA,CAAAmJ,gBAAgB;UAC3B,eAAa,EAAEnJ,MAAA,CAAAoJ,YAAY;UAC3B,aAAW,EAAE,KAAK;UACnBC,QAAQ,EAAR,EAAQ;UACRC,IAAI,EAAJ;;UAMWC,GAAG,EAAA5J,QAAA,CACZ,MAEM+B,MAAA,SAAAA,MAAA,QAFN9B,mBAAA,CAEM;YAFDR,KAAK,EAAC;UAAgB,GAAC,0BAE5B,mB;4BAPF,MAA4D,CAA5DI,YAAA,CAA4DgF,kBAAA;YAAnDpF,KAAK,EAAC;UAAiB;8BAAC,MAAiB,CAAjBI,YAAA,CAAiBgK,wBAAA,E;;0CAClD5J,mBAAA,CAEM;YAFDR,KAAK,EAAC;UAAiB,I,iBAAC,YAClB,GAAAQ,mBAAA,CAAa,YAAT,MAAI,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}