{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { createElementVNode as _createElementVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, resolveComponent as _resolveComponent, createBlock as _createBlock, withCtx as _withCtx, createCommentVNode as _createCommentVNode, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, createVNode as _createVNode, resolveDynamicComponent as _resolveDynamicComponent, normalizeClass as _normalizeClass, withModifiers as _withModifiers, resolveDirective as _resolveDirective, withDirectives as _withDirectives } from \"vue\";\nconst _hoisted_1 = {\n  class: \"collaboration-space\"\n};\nconst _hoisted_2 = {\n  class: \"card-header\"\n};\nconst _hoisted_3 = {\n  class: \"header-actions\"\n};\nconst _hoisted_4 = {\n  key: 1,\n  class: \"current-team-info\"\n};\nconst _hoisted_5 = {\n  class: \"team-name\"\n};\nconst _hoisted_6 = {\n  key: 2,\n  class: \"no-team-info\"\n};\nconst _hoisted_7 = {\n  key: 0\n};\nconst _hoisted_8 = {\n  class: \"toolbar\"\n};\nconst _hoisted_9 = {\n  class: \"toolbar-left\"\n};\nconst _hoisted_10 = {\n  class: \"file-content\"\n};\nconst _hoisted_11 = {\n  class: \"file-grid\"\n};\nconst _hoisted_12 = [\"onClick\"];\nconst _hoisted_13 = {\n  class: \"file-preview\"\n};\nconst _hoisted_14 = {\n  class: \"file-info\"\n};\nconst _hoisted_15 = [\"title\"];\nconst _hoisted_16 = {\n  class: \"file-meta\"\n};\nconst _hoisted_17 = {\n  class: \"file-size\"\n};\nconst _hoisted_18 = {\n  class: \"file-time\"\n};\nconst _hoisted_19 = {\n  class: \"file-uploader\"\n};\nconst _hoisted_20 = {\n  key: 0,\n  class: \"file-source\"\n};\nconst _hoisted_21 = {\n  class: \"file-actions\"\n};\nconst _hoisted_22 = {\n  class: \"pagination-wrapper\"\n};\nconst _hoisted_23 = {\n  class: \"empty-state\"\n};\nconst _hoisted_24 = {\n  key: 0\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_option = _resolveComponent(\"el-option\");\n  const _component_el_select = _resolveComponent(\"el-select\");\n  const _component_el_tag = _resolveComponent(\"el-tag\");\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_Search = _resolveComponent(\"Search\");\n  const _component_el_icon = _resolveComponent(\"el-icon\");\n  const _component_el_input = _resolveComponent(\"el-input\");\n  const _component_Download = _resolveComponent(\"Download\");\n  const _component_Delete = _resolveComponent(\"Delete\");\n  const _component_el_pagination = _resolveComponent(\"el-pagination\");\n  const _component_el_empty = _resolveComponent(\"el-empty\");\n  const _component_el_card = _resolveComponent(\"el-card\");\n  const _component_FileUploadDialog = _resolveComponent(\"FileUploadDialog\");\n  const _component_FileDetailDialog = _resolveComponent(\"FileDetailDialog\");\n  const _directive_loading = _resolveDirective(\"loading\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_el_card, null, {\n    header: _withCtx(() => [_createElementVNode(\"div\", _hoisted_2, [_cache[13] || (_cache[13] = _createElementVNode(\"h3\", null, \"协作空间\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_3, [$setup.isTeacher ? (_openBlock(), _createBlock(_component_el_select, {\n      key: 0,\n      modelValue: $setup.currentTeamId,\n      \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $setup.currentTeamId = $event),\n      placeholder: \"选择团队\",\n      onChange: $setup.handleTeamChange,\n      style: {\n        \"width\": \"200px\"\n      }\n    }, {\n      default: _withCtx(() => [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.myTeams, team => {\n        return _openBlock(), _createBlock(_component_el_option, {\n          key: team.id,\n          label: `${team.name} (${team.projectName || '未知项目'})`,\n          value: team.id\n        }, null, 8 /* PROPS */, [\"label\", \"value\"]);\n      }), 128 /* KEYED_FRAGMENT */))]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\", \"onChange\"])) : $setup.currentTeam ? (_openBlock(), _createElementBlock(\"div\", _hoisted_4, [_createElementVNode(\"span\", _hoisted_5, _toDisplayString($setup.currentTeam.name), 1 /* TEXT */), _createVNode(_component_el_tag, {\n      size: \"small\",\n      type: \"info\"\n    }, {\n      default: _withCtx(() => _cache[9] || (_cache[9] = [_createTextVNode(\"我的团队\")])),\n      _: 1 /* STABLE */,\n      __: [9]\n    })])) : (_openBlock(), _createElementBlock(\"div\", _hoisted_6, [_createVNode(_component_el_tag, {\n      size: \"small\",\n      type: \"warning\"\n    }, {\n      default: _withCtx(() => _cache[10] || (_cache[10] = [_createTextVNode(\"未加入团队\")])),\n      _: 1 /* STABLE */,\n      __: [10]\n    })])), $setup.currentTeamId ? (_openBlock(), _createBlock(_component_el_button, {\n      key: 3,\n      type: \"primary\",\n      onClick: _cache[1] || (_cache[1] = $event => $setup.showUploadDialog = true),\n      icon: $setup.Upload\n    }, {\n      default: _withCtx(() => _cache[11] || (_cache[11] = [_createTextVNode(\" 上传文件 \")])),\n      _: 1 /* STABLE */,\n      __: [11]\n    }, 8 /* PROPS */, [\"icon\"])) : _createCommentVNode(\"v-if\", true), _createVNode(_component_el_button, {\n      onClick: $setup.loadFiles,\n      icon: $setup.Refresh\n    }, {\n      default: _withCtx(() => _cache[12] || (_cache[12] = [_createTextVNode(\" 刷新 \")])),\n      _: 1 /* STABLE */,\n      __: [12]\n    }, 8 /* PROPS */, [\"onClick\", \"icon\"])])])]),\n    default: _withCtx(() => [$setup.currentTeamId ? (_openBlock(), _createElementBlock(\"div\", _hoisted_7, [_createCommentVNode(\" 工具栏 \"), _createElementVNode(\"div\", _hoisted_8, [_createElementVNode(\"div\", _hoisted_9, [_createVNode(_component_el_input, {\n      modelValue: $setup.searchKeyword,\n      \"onUpdate:modelValue\": _cache[2] || (_cache[2] = $event => $setup.searchKeyword = $event),\n      placeholder: \"搜索文件...\",\n      class: \"search-input\",\n      clearable: \"\",\n      onInput: $setup.handleSearch\n    }, {\n      prefix: _withCtx(() => [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [_createVNode(_component_Search)]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\", \"onInput\"]), _createVNode(_component_el_select, {\n      modelValue: $setup.selectedFileType,\n      \"onUpdate:modelValue\": _cache[3] || (_cache[3] = $event => $setup.selectedFileType = $event),\n      placeholder: \"文件类型\",\n      clearable: \"\",\n      class: \"type-filter\",\n      onChange: $setup.handleFilterChange\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_option, {\n        label: \"全部类型\",\n        value: \"\"\n      }), _createVNode(_component_el_option, {\n        label: \"文档\",\n        value: \"DOCUMENT\"\n      }), _createVNode(_component_el_option, {\n        label: \"图片\",\n        value: \"IMAGE\"\n      }), _createVNode(_component_el_option, {\n        label: \"视频\",\n        value: \"VIDEO\"\n      }), _createVNode(_component_el_option, {\n        label: \"音频\",\n        value: \"AUDIO\"\n      }), _createVNode(_component_el_option, {\n        label: \"压缩包\",\n        value: \"ARCHIVE\"\n      }), _createVNode(_component_el_option, {\n        label: \"代码\",\n        value: \"CODE\"\n      }), _createVNode(_component_el_option, {\n        label: \"其他\",\n        value: \"OTHER\"\n      })]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\", \"onChange\"])])]), _createCommentVNode(\" 文件列表 \"), _withDirectives((_openBlock(), _createElementBlock(\"div\", _hoisted_10, [_createCommentVNode(\" 文件网格 \"), _createElementVNode(\"div\", _hoisted_11, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.files, file => {\n      return _openBlock(), _createElementBlock(\"div\", {\n        key: file.id,\n        class: \"file-card\",\n        onClick: $event => $setup.handleFileClick(file)\n      }, [_createElementVNode(\"div\", _hoisted_13, [_createElementVNode(\"div\", {\n        class: _normalizeClass([\"file-icon\", $setup.getFileTypeClass(file.fileType)])\n      }, [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [(_openBlock(), _createBlock(_resolveDynamicComponent($setup.getFileIcon(file.fileType))))]),\n        _: 2 /* DYNAMIC */\n      }, 1024 /* DYNAMIC_SLOTS */)], 2 /* CLASS */)]), _createElementVNode(\"div\", _hoisted_14, [_createElementVNode(\"div\", {\n        class: \"file-name\",\n        title: file.originalName\n      }, _toDisplayString(file.originalName), 9 /* TEXT, PROPS */, _hoisted_15), _createElementVNode(\"div\", _hoisted_16, [_createElementVNode(\"span\", _hoisted_17, _toDisplayString($setup.formatFileSize(file.fileSize)), 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_18, _toDisplayString($setup.formatDate(file.uploadTime)), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_19, \" 上传者: \" + _toDisplayString(file.uploaderName || '未知'), 1 /* TEXT */), file.recordId ? (_openBlock(), _createElementBlock(\"div\", _hoisted_20, [_createVNode(_component_el_tag, {\n        size: \"small\",\n        type: \"info\"\n      }, {\n        default: _withCtx(() => [...(_cache[14] || (_cache[14] = [_createTextVNode(\"任务提交\")]))]),\n        _: 1 /* STABLE */,\n        __: [14]\n      })])) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"div\", _hoisted_21, [_createVNode(_component_el_button, {\n        size: \"small\",\n        type: \"primary\",\n        onClick: _withModifiers($event => $setup.downloadFile(file), [\"stop\"]),\n        loading: $setup.downloadingFiles.includes(file.id),\n        class: \"download-btn\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n          default: _withCtx(() => [_createVNode(_component_Download)]),\n          _: 1 /* STABLE */\n        })]),\n        _: 2 /* DYNAMIC */\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\", \"loading\"]), file.canDelete ? (_openBlock(), _createBlock(_component_el_button, {\n        key: 0,\n        size: \"small\",\n        type: \"danger\",\n        onClick: _withModifiers($event => $setup.deleteFile(file), [\"stop\"]),\n        class: \"delete-btn\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n          default: _withCtx(() => [_createVNode(_component_Delete)]),\n          _: 1 /* STABLE */\n        })]),\n        _: 2 /* DYNAMIC */\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"])) : _createCommentVNode(\"v-if\", true)])], 8 /* PROPS */, _hoisted_12);\n    }), 128 /* KEYED_FRAGMENT */))]), _createCommentVNode(\" 分页 \"), _createElementVNode(\"div\", _hoisted_22, [_createVNode(_component_el_pagination, {\n      \"current-page\": $setup.currentPage,\n      \"onUpdate:currentPage\": _cache[4] || (_cache[4] = $event => $setup.currentPage = $event),\n      \"page-size\": $setup.pageSize,\n      \"onUpdate:pageSize\": _cache[5] || (_cache[5] = $event => $setup.pageSize = $event),\n      \"page-sizes\": [10, 20, 50, 100],\n      total: $setup.total,\n      layout: \"total, sizes, prev, pager, next, jumper\",\n      onSizeChange: $setup.handleSizeChange,\n      onCurrentChange: $setup.handleCurrentChange\n    }, null, 8 /* PROPS */, [\"current-page\", \"page-size\", \"total\", \"onSizeChange\", \"onCurrentChange\"])])])), [[_directive_loading, $setup.loading]])])) : (_openBlock(), _createElementBlock(_Fragment, {\n      key: 1\n    }, [_createCommentVNode(\" 空状态 \"), _createElementVNode(\"div\", _hoisted_23, [_createVNode(_component_el_empty, {\n      description: \"暂无文件\"\n    }, {\n      default: _withCtx(() => [$setup.isTeacher ? (_openBlock(), _createElementBlock(\"p\", _hoisted_24, \"请从上方下拉菜单中选择要管理的团队\")) : (_openBlock(), _createElementBlock(_Fragment, {\n        key: 1\n      }, [_cache[16] || (_cache[16] = _createElementVNode(\"p\", null, \"您还没有加入任何团队，无法访问协作空间\", -1 /* CACHED */)), _createVNode(_component_el_button, {\n        type: \"primary\",\n        onClick: _cache[6] || (_cache[6] = $event => _ctx.$router.push('/dashboard/teams'))\n      }, {\n        default: _withCtx(() => _cache[15] || (_cache[15] = [_createTextVNode(\" 浏览团队 \")])),\n        _: 1 /* STABLE */,\n        __: [15]\n      })], 64 /* STABLE_FRAGMENT */))]),\n      _: 1 /* STABLE */\n    })])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */))]),\n    _: 1 /* STABLE */\n  }), _createCommentVNode(\" 上传对话框 \"), _createVNode(_component_FileUploadDialog, {\n    modelValue: $setup.showUploadDialog,\n    \"onUpdate:modelValue\": _cache[7] || (_cache[7] = $event => $setup.showUploadDialog = $event),\n    \"team-id\": $setup.currentTeamId,\n    onUploadSuccess: $setup.handleUploadSuccess\n  }, null, 8 /* PROPS */, [\"modelValue\", \"team-id\", \"onUploadSuccess\"]), _createCommentVNode(\" 文件详情对话框 \"), _createVNode(_component_FileDetailDialog, {\n    modelValue: $setup.showDetailDialog,\n    \"onUpdate:modelValue\": _cache[8] || (_cache[8] = $event => $setup.showDetailDialog = $event),\n    file: $setup.selectedFile\n  }, null, 8 /* PROPS */, [\"modelValue\", \"file\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_card", "header", "_withCtx", "_createElementVNode", "_hoisted_2", "_hoisted_3", "$setup", "<PERSON><PERSON><PERSON>er", "_createBlock", "_component_el_select", "currentTeamId", "$event", "placeholder", "onChange", "handleTeamChange", "style", "_Fragment", "_renderList", "myTeams", "team", "_component_el_option", "key", "id", "label", "name", "projectName", "value", "currentTeam", "_hoisted_4", "_hoisted_5", "_toDisplayString", "_component_el_tag", "size", "type", "_cache", "_hoisted_6", "_component_el_button", "onClick", "showUploadDialog", "icon", "Upload", "loadFiles", "Refresh", "_hoisted_7", "_createCommentVNode", "_hoisted_8", "_hoisted_9", "_component_el_input", "searchKeyword", "clearable", "onInput", "handleSearch", "prefix", "_component_el_icon", "_component_Search", "selectedFileType", "handleFilterChange", "_hoisted_10", "_hoisted_11", "files", "file", "handleFileClick", "_hoisted_13", "_normalizeClass", "getFileTypeClass", "fileType", "_resolveDynamicComponent", "getFileIcon", "_hoisted_14", "title", "originalName", "_hoisted_15", "_hoisted_16", "_hoisted_17", "formatFileSize", "fileSize", "_hoisted_18", "formatDate", "uploadTime", "_hoisted_19", "uploaderName", "recordId", "_hoisted_20", "_hoisted_21", "_withModifiers", "downloadFile", "loading", "downloadingFiles", "includes", "_component_Download", "canDelete", "deleteFile", "_component_Delete", "_hoisted_22", "_component_el_pagination", "currentPage", "pageSize", "total", "layout", "onSizeChange", "handleSizeChange", "onCurrentChange", "handleCurrentChange", "_hoisted_23", "_component_el_empty", "description", "_hoisted_24", "_ctx", "$router", "push", "_component_FileUploadDialog", "onUploadSuccess", "handleUploadSuccess", "_component_FileDetailDialog", "showDetailDialog", "selectedFile"], "sources": ["D:\\workspace\\idea\\worker\\work_cli\\src\\views\\collaboration\\CollaborationSpaceView.vue"], "sourcesContent": ["<template>\n  <div class=\"collaboration-space\">\n    <el-card>\n      <template #header>\n        <div class=\"card-header\">\n          <h3>协作空间</h3>\n          <div class=\"header-actions\">\n            <el-select\n              v-if=\"isTeacher\"\n              v-model=\"currentTeamId\"\n              placeholder=\"选择团队\"\n              @change=\"handleTeamChange\"\n              style=\"width: 200px;\"\n            >\n              <el-option\n                v-for=\"team in myTeams\"\n                :key=\"team.id\"\n                :label=\"`${team.name} (${team.projectName || '未知项目'})`\"\n                :value=\"team.id\"\n              />\n            </el-select>\n            <div v-else-if=\"currentTeam\" class=\"current-team-info\">\n              <span class=\"team-name\">{{ currentTeam.name }}</span>\n              <el-tag size=\"small\" type=\"info\">我的团队</el-tag>\n            </div>\n            <div v-else class=\"no-team-info\">\n              <el-tag size=\"small\" type=\"warning\">未加入团队</el-tag>\n            </div>\n\n            <el-button\n              v-if=\"currentTeamId\"\n              type=\"primary\"\n              @click=\"showUploadDialog = true\"\n              :icon=\"Upload\"\n            >\n              上传文件\n            </el-button>\n            <el-button @click=\"loadFiles\" :icon=\"Refresh\">\n              刷新\n            </el-button>\n          </div>\n        </div>\n      </template>\n\n      <!-- 有团队时显示内容 -->\n      <div v-if=\"currentTeamId\">\n        <!-- 工具栏 -->\n        <div class=\"toolbar\">\n          <div class=\"toolbar-left\">\n        <el-input\n          v-model=\"searchKeyword\"\n          placeholder=\"搜索文件...\"\n          class=\"search-input\"\n          clearable\n          @input=\"handleSearch\"\n        >\n          <template #prefix>\n            <el-icon><Search /></el-icon>\n          </template>\n        </el-input>\n        \n        <el-select\n          v-model=\"selectedFileType\"\n          placeholder=\"文件类型\"\n          clearable\n          class=\"type-filter\"\n          @change=\"handleFilterChange\"\n        >\n          <el-option label=\"全部类型\" value=\"\" />\n          <el-option label=\"文档\" value=\"DOCUMENT\" />\n          <el-option label=\"图片\" value=\"IMAGE\" />\n          <el-option label=\"视频\" value=\"VIDEO\" />\n          <el-option label=\"音频\" value=\"AUDIO\" />\n          <el-option label=\"压缩包\" value=\"ARCHIVE\" />\n          <el-option label=\"代码\" value=\"CODE\" />\n          <el-option label=\"其他\" value=\"OTHER\" />\n          </el-select>\n          </div>\n\n\n        </div>\n\n        <!-- 文件列表 -->\n        <div class=\"file-content\" v-loading=\"loading\">\n          <!-- 文件网格 -->\n          <div class=\"file-grid\">\n            <div\n              v-for=\"file in files\"\n              :key=\"file.id\"\n              class=\"file-card\"\n              @click=\"handleFileClick(file)\"\n            >\n          <div class=\"file-preview\">\n            <div class=\"file-icon\" :class=\"getFileTypeClass(file.fileType)\">\n              <el-icon>\n                <component :is=\"getFileIcon(file.fileType)\" />\n              </el-icon>\n            </div>\n          </div>\n          <div class=\"file-info\">\n            <div class=\"file-name\" :title=\"file.originalName\">\n              {{ file.originalName }}\n            </div>\n            <div class=\"file-meta\">\n              <span class=\"file-size\">{{ formatFileSize(file.fileSize) }}</span>\n              <span class=\"file-time\">{{ formatDate(file.uploadTime) }}</span>\n            </div>\n            <div class=\"file-uploader\">\n              上传者: {{ file.uploaderName || '未知' }}\n            </div>\n            <div class=\"file-source\" v-if=\"file.recordId\">\n              <el-tag size=\"small\" type=\"info\">任务提交</el-tag>\n            </div>\n          </div>\n          <div class=\"file-actions\">\n            <el-button\n              size=\"small\"\n              type=\"primary\"\n              @click.stop=\"downloadFile(file)\"\n              :loading=\"downloadingFiles.includes(file.id)\"\n              class=\"download-btn\"\n            >\n              <el-icon><Download /></el-icon>\n            </el-button>\n            <el-button\n              v-if=\"file.canDelete\"\n              size=\"small\"\n              type=\"danger\"\n              @click.stop=\"deleteFile(file)\"\n              class=\"delete-btn\"\n            >\n              <el-icon><Delete /></el-icon>\n            </el-button>\n          </div>\n        </div>\n      </div>\n\n\n\n        <!-- 分页 -->\n        <div class=\"pagination-wrapper\">\n          <el-pagination\n            v-model:current-page=\"currentPage\"\n            v-model:page-size=\"pageSize\"\n            :page-sizes=\"[10, 20, 50, 100]\"\n            :total=\"total\"\n            layout=\"total, sizes, prev, pager, next, jumper\"\n            @size-change=\"handleSizeChange\"\n            @current-change=\"handleCurrentChange\"\n          />\n        </div>\n        </div>\n      </div>\n\n      <!-- 空状态 -->\n      <div v-else class=\"empty-state\">\n        <el-empty description=\"暂无文件\">\n          <template v-if=\"isTeacher\">\n            <p>请从上方下拉菜单中选择要管理的团队</p>\n          </template>\n          <template v-else>\n            <p>您还没有加入任何团队，无法访问协作空间</p>\n            <el-button type=\"primary\" @click=\"$router.push('/dashboard/teams')\">\n              浏览团队\n            </el-button>\n          </template>\n        </el-empty>\n      </div>\n    </el-card>\n\n    <!-- 上传对话框 -->\n    <FileUploadDialog\n      v-model=\"showUploadDialog\"\n      :team-id=\"currentTeamId\"\n      @upload-success=\"handleUploadSuccess\"\n    />\n\n    <!-- 文件详情对话框 -->\n    <FileDetailDialog\n      v-model=\"showDetailDialog\"\n      :file=\"selectedFile\"\n    />\n  </div>\n</template>\n\n<script>\nimport { ref, reactive, computed, onMounted, watch } from 'vue'\nimport { useStore } from 'vuex'\nimport { useRoute } from 'vue-router'\nimport { ElMessage, ElMessageBox } from 'element-plus'\nimport {\n  FolderOpened, Upload, Document, Folder, Clock,\n  Search, Download, Delete, Picture, VideoPlay, Headphones,\n  Files, DocumentCopy, Refresh\n} from '@element-plus/icons-vue'\nimport { fileAPI, teamAPI, projectAPI } from '@/api'\nimport FileUploadDialog from '@/components/FileUploadDialog.vue'\nimport FileDetailDialog from '@/components/FileDetailDialog.vue'\n\nexport default {\n  name: 'CollaborationSpaceView',\n  components: {\n    FolderOpened, Upload, Document, Folder, Clock,\n    Search, Download, Delete, Picture, VideoPlay, Headphones,\n    Files, DocumentCopy,\n    FileUploadDialog,\n    FileDetailDialog\n  },\n  setup() {\n    const store = useStore()\n    const route = useRoute()\n    \n    // 响应式数据\n    const loading = ref(false)\n    const files = ref([])\n    const downloadingFiles = ref([])\n    \n    // 分页\n    const currentPage = ref(1)\n    const pageSize = ref(20)\n    const total = ref(0)\n    \n    // 筛选和搜索\n    const searchKeyword = ref('')\n    const selectedFileType = ref('')\n    \n    // 对话框\n    const showUploadDialog = ref(false)\n    const showDetailDialog = ref(false)\n    const selectedFile = ref(null)\n    \n    // 计算属性\n    const currentUser = computed(() => store.getters.currentUser)\n    const isTeacher = computed(() => store.getters.userRole === 'TEACHER')\n    const isStudent = computed(() => store.getters.userRole === 'STUDENT')\n\n    // 团队相关数据\n    const currentTeamId = ref(null)\n    const currentTeam = ref(null)\n    const myTeams = ref([])\n\n    // 获取团队信息\n    const getCurrentTeamId = async () => {\n      try {\n        console.log('开始获取团队信息...')\n        console.log('用户角色:', store.getters.userRole)\n\n        // 优先使用路由参数中的团队ID\n        if (route.params.teamId) {\n          currentTeamId.value = parseInt(route.params.teamId)\n          console.log('使用路由参数中的团队ID:', currentTeamId.value)\n          return\n        }\n\n        if (isTeacher.value) {\n          // 教师端：获取所有管理的团队\n          await loadTeacherTeams()\n        } else if (isStudent.value) {\n          // 学生端：获取自己的团队\n          await loadStudentTeam()\n        }\n      } catch (error) {\n        console.error('获取团队信息失败:', error)\n        ElMessage.error('获取团队信息失败')\n      }\n    }\n\n    // 加载教师的团队列表\n    const loadTeacherTeams = async () => {\n      try {\n        // 1. 先获取教师的项目列表\n        const projectsResponse = await projectAPI.getMyProjects()\n        const myProjects = projectsResponse?.records || []\n        console.log('教师的项目列表:', myProjects)\n\n        // 2. 获取每个项目的团队列表\n        const allTeams = []\n        for (const project of myProjects) {\n          try {\n            console.log('处理项目:', project)\n            const teamsResponse = await teamAPI.getProjectTeams(project.id)\n            const projectTeams = teamsResponse?.records || []\n            console.log(`项目 ${project.title || project.name} 的团队列表:`, projectTeams)\n\n            // 为每个团队添加项目信息，尝试多个可能的项目名称字段\n            const teamsWithProject = projectTeams.map(team => ({\n              ...team,\n              projectName: project.title || project.name || project.projectName || '未知项目',\n              projectId: project.id\n            }))\n\n            allTeams.push(...teamsWithProject)\n          } catch (error) {\n            console.error(`获取项目 ${project.title} 的团队失败:`, error)\n          }\n        }\n\n        myTeams.value = allTeams\n        console.log('教师可管理的团队列表:', myTeams.value)\n\n        // 如果有团队，默认选择第一个\n        if (myTeams.value.length > 0) {\n          currentTeamId.value = myTeams.value[0].id\n        }\n      } catch (error) {\n        console.error('获取教师团队列表失败:', error)\n        ElMessage.error('获取团队列表失败')\n      }\n    }\n\n    // 加载学生的团队信息\n    const loadStudentTeam = async () => {\n      try {\n        const response = await teamAPI.getMyTeam()\n        const teamData = response?.data || response\n        if (teamData && teamData.id) {\n          currentTeam.value = teamData\n          currentTeamId.value = teamData.id\n          console.log('学生团队信息:', currentTeam.value)\n        } else {\n          console.log('学生还没有加入任何团队')\n          ElMessage.warning('您还没有加入任何团队，无法访问协作空间')\n        }\n      } catch (error) {\n        console.error('获取学生团队信息失败:', error)\n        ElMessage.warning('您还没有加入任何团队，无法访问协作空间')\n      }\n    }\n\n    // 处理团队切换\n    const handleTeamChange = (teamId) => {\n      console.log('切换到团队:', teamId)\n      currentTeamId.value = teamId\n      if (teamId) {\n        loadFiles()\n      }\n    }\n    \n    // 方法\n    const loadFiles = async () => {\n      console.log('=== 开始加载协作空间文件 ===')\n      console.log('当前团队ID:', currentTeamId.value)\n\n      if (!currentTeamId.value) {\n        console.warn('没有团队ID，无法加载文件')\n        return\n      }\n\n      loading.value = true\n      try {\n        const params = {\n          page: currentPage.value - 1,\n          size: pageSize.value,\n          fileType: selectedFileType.value || undefined,\n          keyword: searchKeyword.value || undefined\n        }\n\n        console.log('请求参数:', params)\n        console.log('请求URL:', `/files/collaboration/team/${currentTeamId.value}`)\n\n        const response = await fileAPI.getCollaborationFiles(currentTeamId.value, params)\n        console.log('API响应:', response)\n        console.log('响应类型:', typeof response)\n        console.log('响应内容详情:', JSON.stringify(response, null, 2))\n\n        // 检查响应是否存在\n        if (!response) {\n          console.error('API响应为空')\n          ElMessage.error('服务器响应异常')\n          return\n        }\n\n        // 由于响应拦截器已经处理了Result格式，这里直接是PageResult数据\n        // 后端返回的字段是 records 而不是 content\n        const content = response.records || response.content || []\n        const totalElements = response.total || response.totalElements || 0\n\n        console.log('解析的文件列表:', content)\n        console.log('总文件数:', totalElements)\n\n        files.value = content\n        total.value = totalElements\n\n        console.log('设置后的files.value:', files.value)\n        console.log('设置后的total.value:', total.value)\n\n      } catch (error) {\n        console.error('加载协作空间文件失败:', error)\n        console.error('错误详情:', error.response || error.message || error)\n        ElMessage.error('加载文件列表失败')\n      } finally {\n        loading.value = false\n      }\n    }\n    \n\n    \n    const downloadFile = async (file) => {\n      downloadingFiles.value.push(file.id)\n      try {\n        console.log('开始下载文件:', file.originalName, 'ID:', file.id)\n\n        // 检查是否有token\n        const token = localStorage.getItem('token')\n        console.log('当前token:', token ? '存在' : '不存在')\n\n        if (!token) {\n          ElMessage.error('请先登录')\n          return\n        }\n\n        // 使用API调用下载文件，这样会自动携带认证信息\n        const response = await fileAPI.downloadFile(file.id)\n\n        // 检查响应类型\n        console.log('下载响应:', response)\n\n        // 创建Blob对象\n        const blob = new Blob([response], {\n          type: file.contentType || 'application/octet-stream'\n        })\n\n        // 创建下载链接\n        const url = window.URL.createObjectURL(blob)\n        const link = document.createElement('a')\n        link.href = url\n        link.download = file.originalName\n        link.style.display = 'none'\n        document.body.appendChild(link)\n        link.click()\n        document.body.removeChild(link)\n        window.URL.revokeObjectURL(url)\n\n        ElMessage.success('文件下载成功')\n      } catch (error) {\n        console.error('文件下载失败:', error)\n        if (error.response?.status === 401) {\n          ElMessage.error('下载失败：需要登录认证')\n        } else if (error.response?.status === 403) {\n          ElMessage.error('下载失败：没有权限')\n        } else {\n          ElMessage.error('文件下载失败')\n        }\n      } finally {\n        downloadingFiles.value = downloadingFiles.value.filter(id => id !== file.id)\n      }\n    }\n\n    const deleteFile = async (file) => {\n      try {\n        await ElMessageBox.confirm(\n          `确定要删除文件 \"${file.originalName}\" 吗？`,\n          '确认删除',\n          {\n            confirmButtonText: '确定',\n            cancelButtonText: '取消',\n            type: 'warning'\n          }\n        )\n\n        await fileAPI.deleteFile(file.id)\n        ElMessage.success('文件删除成功')\n        await loadFiles()\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('文件删除失败:', error)\n          ElMessage.error('文件删除失败')\n        }\n      }\n    }\n\n    const handleFileClick = (file) => {\n      selectedFile.value = file\n      showDetailDialog.value = true\n    }\n\n    const handleSearch = () => {\n      currentPage.value = 1\n      loadFiles()\n    }\n\n    const handleFilterChange = () => {\n      currentPage.value = 1\n      loadFiles()\n    }\n\n    const handleSizeChange = (newSize) => {\n      pageSize.value = newSize\n      currentPage.value = 1\n      loadFiles()\n    }\n\n    const handleCurrentChange = (newPage) => {\n      currentPage.value = newPage\n      loadFiles()\n    }\n\n    const handleUploadSuccess = () => {\n      showUploadDialog.value = false\n      loadFiles()\n      ElMessage.success('文件上传成功')\n    }\n\n    // 工具方法\n    const formatFileSize = (bytes) => {\n      if (bytes === 0) return '0 B'\n      const k = 1024\n      const sizes = ['B', 'KB', 'MB', 'GB']\n      const i = Math.floor(Math.log(bytes) / Math.log(k))\n      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]\n    }\n\n    const formatDate = (dateString) => {\n      if (!dateString) return ''\n      const date = new Date(dateString)\n      return date.toLocaleString('zh-CN')\n    }\n\n    const getFileIcon = (fileType) => {\n      const iconMap = {\n        DOCUMENT: DocumentCopy,\n        IMAGE: Picture,\n        VIDEO: VideoPlay,\n        AUDIO: Headphones,\n        ARCHIVE: Files,\n        CODE: Document,\n        OTHER: Document\n      }\n      return iconMap[fileType] || Document\n    }\n\n    const getFileTypeClass = (fileType) => {\n      return `file-type-${fileType?.toLowerCase() || 'other'}`\n    }\n\n    const getFileTypeLabel = (fileType) => {\n      const labelMap = {\n        DOCUMENT: '文档',\n        IMAGE: '图片',\n        VIDEO: '视频',\n        AUDIO: '音频',\n        ARCHIVE: '压缩包',\n        CODE: '代码',\n        OTHER: '其他'\n      }\n      return labelMap[fileType] || '其他'\n    }\n\n    const getFileTypeTagType = (fileType) => {\n      const typeMap = {\n        DOCUMENT: 'primary',\n        IMAGE: 'success',\n        VIDEO: 'warning',\n        AUDIO: 'info',\n        ARCHIVE: 'danger',\n        CODE: '',\n        OTHER: 'info'\n      }\n      return typeMap[fileType] || 'info'\n    }\n\n\n\n    // 生命周期\n    onMounted(async () => {\n      await getCurrentTeamId()\n      if (currentTeamId.value) {\n        loadFiles()\n      }\n    })\n\n    // 监听团队ID变化\n    watch(currentTeamId, (newTeamId) => {\n      if (newTeamId) {\n        loadFiles()\n      }\n    })\n\n    return {\n      loading,\n      files,\n      downloadingFiles,\n      currentPage,\n      pageSize,\n      total,\n      searchKeyword,\n      selectedFileType,\n      showUploadDialog,\n      showDetailDialog,\n      selectedFile,\n      currentTeamId,\n      currentTeam,\n      myTeams,\n      isTeacher,\n      isStudent,\n      getCurrentTeamId,\n      loadFiles,\n      handleTeamChange,\n      downloadFile,\n      deleteFile,\n      handleFileClick,\n      handleSearch,\n      handleFilterChange,\n      handleSizeChange,\n      handleCurrentChange,\n      handleUploadSuccess,\n      formatFileSize,\n      formatDate,\n      getFileIcon,\n      getFileTypeClass,\n      getFileTypeLabel,\n      getFileTypeTagType,\n      // 图标\n      Upload,\n      Refresh\n    }\n  }\n}\n</script>\n\n<style scoped>\n.collaboration-space {\n  padding: 0;\n}\n\n/* 卡片头部样式 */\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.card-header h3 {\n  margin: 0;\n  font-size: var(--text-lg);\n  font-weight: var(--font-weight-semibold);\n  color: var(--text-primary);\n}\n\n.header-actions {\n  display: flex;\n  align-items: center;\n  gap: var(--space-3);\n}\n\n.current-team-info {\n  display: flex;\n  align-items: center;\n  gap: var(--space-2);\n}\n\n.team-name {\n  font-weight: var(--font-weight-medium);\n  color: var(--text-primary);\n}\n\n.no-team-info {\n  display: flex;\n  align-items: center;\n}\n\n/* 空状态 */\n.empty-state {\n  text-align: center;\n  padding: var(--space-8);\n}\n\n.empty-state p {\n  color: var(--text-secondary);\n  margin: var(--space-4) 0;\n}\n\n\n\n/* 工具栏 */\n.toolbar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: var(--space-4);\n  background: var(--gray-50);\n  border-bottom: 1px solid var(--gray-200);\n  margin: 0 -20px;\n  margin-bottom: var(--space-4);\n}\n\n.toolbar-left {\n  display: flex;\n  gap: var(--space-3);\n  align-items: center;\n}\n\n.search-input {\n  width: 300px;\n}\n\n.type-filter {\n  width: 120px;\n}\n\n.toolbar-right {\n  display: flex;\n  gap: var(--space-3);\n  align-items: center;\n}\n\n/* 文件内容区域 */\n.file-content {\n  min-height: 400px;\n  padding: var(--space-4);\n}\n\n/* 网格视图 */\n.file-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));\n  gap: var(--space-4);\n  margin-bottom: var(--space-6);\n}\n\n.file-card {\n  background: var(--surface-color);\n  border: 1px solid var(--gray-200);\n  border-radius: var(--radius-lg);\n  padding: var(--space-4);\n  cursor: pointer;\n  transition: all var(--transition-base);\n  box-shadow: var(--shadow-sm);\n  position: relative;\n}\n\n.file-card:hover {\n  transform: translateY(-2px);\n  box-shadow: var(--shadow-md);\n  border-color: var(--primary-200);\n}\n\n.file-preview {\n  display: flex;\n  justify-content: center;\n  margin-bottom: var(--space-3);\n}\n\n.file-icon {\n  width: 64px;\n  height: 64px;\n  border-radius: var(--radius-lg);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 28px;\n  color: white;\n}\n\n.file-type-document {\n  background: linear-gradient(135deg, var(--primary-500), var(--primary-600));\n}\n\n.file-type-image {\n  background: linear-gradient(135deg, var(--success-500), var(--success-600));\n}\n\n.file-type-video {\n  background: linear-gradient(135deg, var(--warning-500), var(--warning-600));\n}\n\n.file-type-audio {\n  background: linear-gradient(135deg, var(--info-500), var(--info-600));\n}\n\n.file-type-archive {\n  background: linear-gradient(135deg, var(--error-500), var(--error-600));\n}\n\n.file-type-code {\n  background: linear-gradient(135deg, var(--gray-600), var(--gray-700));\n}\n\n.file-type-other {\n  background: linear-gradient(135deg, var(--gray-500), var(--gray-600));\n}\n\n.file-info {\n  text-align: center;\n  margin-bottom: var(--space-3);\n}\n\n.file-name {\n  font-weight: var(--font-weight-medium);\n  color: var(--text-primary);\n  margin-bottom: var(--space-2);\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.file-meta {\n  display: flex;\n  justify-content: space-between;\n  font-size: var(--text-xs);\n  color: var(--text-tertiary);\n  margin-bottom: var(--space-1);\n}\n\n.file-uploader {\n  font-size: var(--text-xs);\n  color: var(--text-secondary);\n}\n\n.file-actions {\n  display: flex;\n  justify-content: center;\n  gap: var(--space-2);\n  opacity: 0;\n  transition: opacity var(--transition-base);\n}\n\n.file-card:hover .file-actions {\n  opacity: 1;\n}\n\n.file-actions .download-btn {\n  background: var(--primary-500);\n  border-color: var(--primary-500);\n  color: white;\n}\n\n.file-actions .download-btn:hover {\n  background: var(--primary-600);\n  border-color: var(--primary-600);\n}\n\n.file-actions .delete-btn {\n  background: var(--error-500);\n  border-color: var(--error-500);\n  color: white;\n}\n\n.file-actions .delete-btn:hover {\n  background: var(--error-600);\n  border-color: var(--error-600);\n}\n\n\n\n/* 分页 */\n.pagination-wrapper {\n  display: flex;\n  justify-content: center;\n  padding: var(--space-4);\n  background: var(--gray-50);\n  border-top: 1px solid var(--gray-200);\n  margin: var(--space-4) -20px 0;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .header-content {\n    flex-direction: column;\n    gap: var(--space-4);\n  }\n\n  .toolbar {\n    flex-direction: column;\n    gap: var(--space-3);\n  }\n\n  .toolbar-left {\n    width: 100%;\n    flex-direction: column;\n  }\n\n  .search-input {\n    width: 100%;\n  }\n\n\n\n  .file-grid {\n    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\n  }\n}\n</style>\n"], "mappings": ";;;EACOA,KAAK,EAAC;AAAqB;;EAGrBA,KAAK,EAAC;AAAa;;EAEjBA,KAAK,EAAC;AAAgB;;;EAeIA,KAAK,EAAC;;;EAC3BA,KAAK,EAAC;AAAW;;;EAGbA,KAAK,EAAC;;;;;;EAsBjBA,KAAK,EAAC;AAAS;;EACbA,KAAK,EAAC;AAAc;;EAmCtBA,KAAK,EAAC;AAAc;;EAElBA,KAAK,EAAC;AAAW;;;EAOjBA,KAAK,EAAC;AAAc;;EAOpBA,KAAK,EAAC;AAAW;;;EAIfA,KAAK,EAAC;AAAW;;EACdA,KAAK,EAAC;AAAW;;EACjBA,KAAK,EAAC;AAAW;;EAEpBA,KAAK,EAAC;AAAe;;;EAGrBA,KAAK,EAAC;;;EAIRA,KAAK,EAAC;AAAc;;EA0BtBA,KAAK,EAAC;AAAoB;;EAerBA,KAAK,EAAC;AAAa;;;;;;;;;;;;;;;;;;;;uBA1JnCC,mBAAA,CAqLM,OArLNC,UAqLM,GApLJC,YAAA,CAsKUC,kBAAA;IArKGC,MAAM,EAAAC,QAAA,CACf,MAqCM,CArCNC,mBAAA,CAqCM,OArCNC,UAqCM,G,4BApCJD,mBAAA,CAAa,YAAT,MAAI,qBACRA,mBAAA,CAkCM,OAlCNE,UAkCM,GAhCIC,MAAA,CAAAC,SAAS,I,cADjBC,YAAA,CAaYC,oBAAA;;kBAXDH,MAAA,CAAAI,aAAa;iEAAbJ,MAAA,CAAAI,aAAa,GAAAC,MAAA;MACtBC,WAAW,EAAC,MAAM;MACjBC,QAAM,EAAEP,MAAA,CAAAQ,gBAAgB;MACzBC,KAAqB,EAArB;QAAA;MAAA;;wBAGE,MAAuB,E,kBADzBlB,mBAAA,CAKEmB,SAAA,QAAAC,WAAA,CAJeX,MAAA,CAAAY,OAAO,EAAfC,IAAI;6BADbX,YAAA,CAKEY,oBAAA;UAHCC,GAAG,EAAEF,IAAI,CAACG,EAAE;UACZC,KAAK,KAAKJ,IAAI,CAACK,IAAI,KAAKL,IAAI,CAACM,WAAW;UACxCC,KAAK,EAAEP,IAAI,CAACG;;;;qDAGDhB,MAAA,CAAAqB,WAAW,I,cAA3B9B,mBAAA,CAGM,OAHN+B,UAGM,GAFJzB,mBAAA,CAAqD,QAArD0B,UAAqD,EAAAC,gBAAA,CAA1BxB,MAAA,CAAAqB,WAAW,CAACH,IAAI,kBAC3CzB,YAAA,CAA8CgC,iBAAA;MAAtCC,IAAI,EAAC,OAAO;MAACC,IAAI,EAAC;;wBAAO,MAAIC,MAAA,QAAAA,MAAA,O,iBAAJ,MAAI,E;;;2BAEvCrC,mBAAA,CAEM,OAFNsC,UAEM,GADJpC,YAAA,CAAkDgC,iBAAA;MAA1CC,IAAI,EAAC,OAAO;MAACC,IAAI,EAAC;;wBAAU,MAAKC,MAAA,SAAAA,MAAA,Q,iBAAL,OAAK,E;;;WAInC5B,MAAA,CAAAI,aAAa,I,cADrBF,YAAA,CAOY4B,oBAAA;;MALVH,IAAI,EAAC,SAAS;MACbI,OAAK,EAAAH,MAAA,QAAAA,MAAA,MAAAvB,MAAA,IAAEL,MAAA,CAAAgC,gBAAgB;MACvBC,IAAI,EAAEjC,MAAA,CAAAkC;;wBACR,MAEDN,MAAA,SAAAA,MAAA,Q,iBAFC,QAED,E;;;sEACAnC,YAAA,CAEYqC,oBAAA;MAFAC,OAAK,EAAE/B,MAAA,CAAAmC,SAAS;MAAGF,IAAI,EAAEjC,MAAA,CAAAoC;;wBAAS,MAE9CR,MAAA,SAAAA,MAAA,Q,iBAF8C,MAE9C,E;;;;sBAD4B,MAyHZ,CAlHX5B,MAAA,CAAAI,aAAa,I,cAAxBb,mBAAA,CA2GM,OAAA8C,UAAA,GA1GJC,mBAAA,SAAY,EACZzC,mBAAA,CAiCM,OAjCN0C,UAiCM,GAhCJ1C,mBAAA,CA6BM,OA7BN2C,UA6BM,GA5BR/C,YAAA,CAUWgD,mBAAA;kBATAzC,MAAA,CAAA0C,aAAa;iEAAb1C,MAAA,CAAA0C,aAAa,GAAArC,MAAA;MACtBC,WAAW,EAAC,SAAS;MACrBhB,KAAK,EAAC,cAAc;MACpBqD,SAAS,EAAT,EAAS;MACRC,OAAK,EAAE5C,MAAA,CAAA6C;;MAEGC,MAAM,EAAAlD,QAAA,CACf,MAA6B,CAA7BH,YAAA,CAA6BsD,kBAAA;0BAApB,MAAU,CAAVtD,YAAA,CAAUuD,iBAAA,E;;;;kDAIvBvD,YAAA,CAecU,oBAAA;kBAdHH,MAAA,CAAAiD,gBAAgB;iEAAhBjD,MAAA,CAAAiD,gBAAgB,GAAA5C,MAAA;MACzBC,WAAW,EAAC,MAAM;MAClBqC,SAAS,EAAT,EAAS;MACTrD,KAAK,EAAC,aAAa;MAClBiB,QAAM,EAAEP,MAAA,CAAAkD;;wBAET,MAAmC,CAAnCzD,YAAA,CAAmCqB,oBAAA;QAAxBG,KAAK,EAAC,MAAM;QAACG,KAAK,EAAC;UAC9B3B,YAAA,CAAyCqB,oBAAA;QAA9BG,KAAK,EAAC,IAAI;QAACG,KAAK,EAAC;UAC5B3B,YAAA,CAAsCqB,oBAAA;QAA3BG,KAAK,EAAC,IAAI;QAACG,KAAK,EAAC;UAC5B3B,YAAA,CAAsCqB,oBAAA;QAA3BG,KAAK,EAAC,IAAI;QAACG,KAAK,EAAC;UAC5B3B,YAAA,CAAsCqB,oBAAA;QAA3BG,KAAK,EAAC,IAAI;QAACG,KAAK,EAAC;UAC5B3B,YAAA,CAAyCqB,oBAAA;QAA9BG,KAAK,EAAC,KAAK;QAACG,KAAK,EAAC;UAC7B3B,YAAA,CAAqCqB,oBAAA;QAA1BG,KAAK,EAAC,IAAI;QAACG,KAAK,EAAC;UAC5B3B,YAAA,CAAsCqB,oBAAA;QAA3BG,KAAK,EAAC,IAAI;QAACG,KAAK,EAAC;;;uDAO9BkB,mBAAA,UAAa,E,+BACb/C,mBAAA,CAoEM,OApEN4D,WAoEM,GAnEJb,mBAAA,UAAa,EACbzC,mBAAA,CAkDE,OAlDFuD,WAkDE,I,kBAjDA7D,mBAAA,CAgDEmB,SAAA,QAAAC,WAAA,CA/CeX,MAAA,CAAAqD,KAAK,EAAbC,IAAI;2BADb/D,mBAAA,CAgDE;QA9CCwB,GAAG,EAAEuC,IAAI,CAACtC,EAAE;QACb1B,KAAK,EAAC,WAAW;QAChByC,OAAK,EAAA1B,MAAA,IAAEL,MAAA,CAAAuD,eAAe,CAACD,IAAI;UAEhCzD,mBAAA,CAMM,OANN2D,WAMM,GALJ3D,mBAAA,CAIM;QAJDP,KAAK,EAAAmE,eAAA,EAAC,WAAW,EAASzD,MAAA,CAAA0D,gBAAgB,CAACJ,IAAI,CAACK,QAAQ;UAC3DlE,YAAA,CAEUsD,kBAAA;0BADR,MAA8C,E,cAA9C7C,YAAA,CAA8C0D,wBAAA,CAA9B5D,MAAA,CAAA6D,WAAW,CAACP,IAAI,CAACK,QAAQ,K;;uDAI/C9D,mBAAA,CAcM,OAdNiE,WAcM,GAbJjE,mBAAA,CAEM;QAFDP,KAAK,EAAC,WAAW;QAAEyE,KAAK,EAAET,IAAI,CAACU;0BAC/BV,IAAI,CAACU,YAAY,wBAAAC,WAAA,GAEtBpE,mBAAA,CAGM,OAHNqE,WAGM,GAFJrE,mBAAA,CAAkE,QAAlEsE,WAAkE,EAAA3C,gBAAA,CAAvCxB,MAAA,CAAAoE,cAAc,CAACd,IAAI,CAACe,QAAQ,mBACvDxE,mBAAA,CAAgE,QAAhEyE,WAAgE,EAAA9C,gBAAA,CAArCxB,MAAA,CAAAuE,UAAU,CAACjB,IAAI,CAACkB,UAAU,kB,GAEvD3E,mBAAA,CAEM,OAFN4E,WAEM,EAFqB,QACpB,GAAAjD,gBAAA,CAAG8B,IAAI,CAACoB,YAAY,0BAEIpB,IAAI,CAACqB,QAAQ,I,cAA5CpF,mBAAA,CAEM,OAFNqF,WAEM,GADJnF,YAAA,CAA8CgC,iBAAA;QAAtCC,IAAI,EAAC,OAAO;QAACC,IAAI,EAAC;;0BAAO,MAAI,KAAAC,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;mDAGzC/B,mBAAA,CAmBM,OAnBNgF,WAmBM,GAlBJpF,YAAA,CAQYqC,oBAAA;QAPVJ,IAAI,EAAC,OAAO;QACZC,IAAI,EAAC,SAAS;QACbI,OAAK,EAAA+C,cAAA,CAAAzE,MAAA,IAAOL,MAAA,CAAA+E,YAAY,CAACzB,IAAI;QAC7B0B,OAAO,EAAEhF,MAAA,CAAAiF,gBAAgB,CAACC,QAAQ,CAAC5B,IAAI,CAACtC,EAAE;QAC3C1B,KAAK,EAAC;;0BAEN,MAA+B,CAA/BG,YAAA,CAA+BsD,kBAAA;4BAAtB,MAAY,CAAZtD,YAAA,CAAY0F,mBAAA,E;;;;mEAGf7B,IAAI,CAAC8B,SAAS,I,cADtBlF,YAAA,CAQY4B,oBAAA;;QANVJ,IAAI,EAAC,OAAO;QACZC,IAAI,EAAC,QAAQ;QACZI,OAAK,EAAA+C,cAAA,CAAAzE,MAAA,IAAOL,MAAA,CAAAqF,UAAU,CAAC/B,IAAI;QAC5BhE,KAAK,EAAC;;0BAEN,MAA6B,CAA7BG,YAAA,CAA6BsD,kBAAA;4BAApB,MAAU,CAAVtD,YAAA,CAAU6F,iBAAA,E;;;;;sCAQzBhD,mBAAA,QAAW,EACXzC,mBAAA,CAUM,OAVN0F,WAUM,GATJ9F,YAAA,CAQE+F,wBAAA;MAPQ,cAAY,EAAExF,MAAA,CAAAyF,WAAW;kEAAXzF,MAAA,CAAAyF,WAAW,GAAApF,MAAA;MACzB,WAAS,EAAEL,MAAA,CAAA0F,QAAQ;+DAAR1F,MAAA,CAAA0F,QAAQ,GAAArF,MAAA;MAC1B,YAAU,EAAE,iBAAiB;MAC7BsF,KAAK,EAAE3F,MAAA,CAAA2F,KAAK;MACbC,MAAM,EAAC,yCAAyC;MAC/CC,YAAW,EAAE7F,MAAA,CAAA8F,gBAAgB;MAC7BC,eAAc,EAAE/F,MAAA,CAAAgG;mIAjEgBhG,MAAA,CAAAgF,OAAO,E,sBAwE9CzF,mBAAA,CAYMmB,SAAA;MAAAK,GAAA;IAAA,IAbNuB,mBAAA,SAAY,EACZzC,mBAAA,CAYM,OAZNoG,WAYM,GAXJxG,YAAA,CAUWyG,mBAAA;MAVDC,WAAW,EAAC;IAAM;wBAOF,MAGQ,CAThBnG,MAAA,CAAAC,SAAS,I,cACvBV,mBAAA,CAAwB,KAAA6G,WAAA,EAArB,mBAAiB,M,cAEtB7G,mBAAA,CAKWmB,SAAA;QAAAK,GAAA;MAAA,I,4BAJTlB,mBAAA,CAA0B,WAAvB,qBAAmB,qBACtBJ,YAAA,CAEYqC,oBAAA;QAFDH,IAAI,EAAC,SAAS;QAAEI,OAAK,EAAAH,MAAA,QAAAA,MAAA,MAAAvB,MAAA,IAAEgG,IAAA,CAAAC,OAAO,CAACC,IAAI;;0BAAsB,MAEpE3E,MAAA,SAAAA,MAAA,Q,iBAFoE,QAEpE,E;;;;;;;MAMRU,mBAAA,WAAc,EACd7C,YAAA,CAIE+G,2BAAA;gBAHSxG,MAAA,CAAAgC,gBAAgB;+DAAhBhC,MAAA,CAAAgC,gBAAgB,GAAA3B,MAAA;IACxB,SAAO,EAAEL,MAAA,CAAAI,aAAa;IACtBqG,eAAc,EAAEzG,MAAA,CAAA0G;yEAGnBpE,mBAAA,aAAgB,EAChB7C,YAAA,CAGEkH,2BAAA;gBAFS3G,MAAA,CAAA4G,gBAAgB;+DAAhB5G,MAAA,CAAA4G,gBAAgB,GAAAvG,MAAA;IACxBiD,IAAI,EAAEtD,MAAA,CAAA6G", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}