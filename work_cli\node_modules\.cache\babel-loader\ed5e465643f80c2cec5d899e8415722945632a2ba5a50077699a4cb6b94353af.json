{"ast": null, "code": "import { createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, createTextVNode as _createTextVNode, createCommentVNode as _createCommentVNode, withKeys as _withKeys, toDisplayString as _toDisplayString, resolveDirective as _resolveDirective, openBlock as _openBlock, createBlock as _createBlock, withDirectives as _withDirectives, renderList as _renderList, Fragment as _Fragment, createElementBlock as _createElementBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"project-management\"\n};\nconst _hoisted_2 = {\n  class: \"card-header\"\n};\nconst _hoisted_3 = {\n  class: \"search-bar\"\n};\nconst _hoisted_4 = {\n  class: \"table-container\"\n};\nconst _hoisted_5 = {\n  class: \"pagination-container\"\n};\nconst _hoisted_6 = {\n  key: 0\n};\nconst _hoisted_7 = {\n  style: {\n    \"margin-bottom\": \"15px\"\n  }\n};\nconst _hoisted_8 = {\n  style: {\n    \"margin-bottom\": \"15px\"\n  }\n};\nconst _hoisted_9 = {\n  class: \"dialog-footer\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_Refresh = _resolveComponent(\"Refresh\");\n  const _component_el_icon = _resolveComponent(\"el-icon\");\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_Search = _resolveComponent(\"Search\");\n  const _component_el_input = _resolveComponent(\"el-input\");\n  const _component_el_option = _resolveComponent(\"el-option\");\n  const _component_el_select = _resolveComponent(\"el-select\");\n  const _component_RefreshLeft = _resolveComponent(\"RefreshLeft\");\n  const _component_el_table_column = _resolveComponent(\"el-table-column\");\n  const _component_el_tag = _resolveComponent(\"el-tag\");\n  const _component_el_table = _resolveComponent(\"el-table\");\n  const _component_el_pagination = _resolveComponent(\"el-pagination\");\n  const _component_el_card = _resolveComponent(\"el-card\");\n  const _component_el_dialog = _resolveComponent(\"el-dialog\");\n  const _directive_loading = _resolveDirective(\"loading\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_el_card, null, {\n    header: _withCtx(() => [_createElementVNode(\"div\", _hoisted_2, [_cache[7] || (_cache[7] = _createElementVNode(\"h3\", null, \"项目管理\", -1 /* CACHED */)), _createVNode(_component_el_button, {\n      type: \"primary\",\n      onClick: _ctx.loadProjects\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [_createVNode(_component_Refresh)]),\n        _: 1 /* STABLE */\n      }), _cache[6] || (_cache[6] = _createTextVNode(\" 刷新 \"))]),\n      _: 1 /* STABLE */,\n      __: [6]\n    }, 8 /* PROPS */, [\"onClick\"])])]),\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_3, [_createVNode(_component_el_input, {\n      modelValue: $setup.searchKeyword,\n      \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $setup.searchKeyword = $event),\n      placeholder: \"搜索项目名称或描述...\",\n      style: {\n        \"width\": \"200px\"\n      },\n      clearable: \"\",\n      onKeyup: _withKeys($setup.handleSearch, [\"enter\"])\n    }, {\n      append: _withCtx(() => [_createVNode(_component_el_button, {\n        onClick: $setup.handleSearch\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n          default: _withCtx(() => [_createVNode(_component_Search)]),\n          _: 1 /* STABLE */\n        })]),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"onClick\"])]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\", \"onKeyup\"]), _createVNode(_component_el_select, {\n      modelValue: $setup.statusFilter,\n      \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $setup.statusFilter = $event),\n      placeholder: \"状态筛选\",\n      style: {\n        \"width\": \"120px\",\n        \"margin-left\": \"10px\"\n      },\n      onChange: $setup.handleFilter\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_option, {\n        label: \"全部\",\n        value: \"\"\n      }), _createVNode(_component_el_option, {\n        label: \"已发布\",\n        value: \"PUBLISHED\"\n      }), _createVNode(_component_el_option, {\n        label: \"进行中\",\n        value: \"IN_PROGRESS\"\n      }), _createVNode(_component_el_option, {\n        label: \"已完成\",\n        value: \"COMPLETED\"\n      })]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\", \"onChange\"]), _createVNode(_component_el_button, {\n      onClick: _ctx.handleReset,\n      style: {\n        \"margin-left\": \"10px\"\n      }\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [_createVNode(_component_RefreshLeft)]),\n        _: 1 /* STABLE */\n      }), _cache[8] || (_cache[8] = _createTextVNode(\" 重置 \"))]),\n      _: 1 /* STABLE */,\n      __: [8]\n    }, 8 /* PROPS */, [\"onClick\"])]), _createElementVNode(\"div\", _hoisted_4, [_withDirectives((_openBlock(), _createBlock(_component_el_table, {\n      data: $setup.projects,\n      stripe: \"\",\n      style: {\n        \"width\": \"100%\",\n        \"min-width\": \"1000px\"\n      }\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_table_column, {\n        prop: \"id\",\n        label: \"ID\",\n        width: \"100\",\n        \"show-overflow-tooltip\": \"\"\n      }), _createVNode(_component_el_table_column, {\n        prop: \"name\",\n        label: \"项目名称\",\n        \"min-width\": \"220\"\n      }), _createVNode(_component_el_table_column, {\n        prop: \"teacherName\",\n        label: \"负责教师\",\n        \"min-width\": \"120\"\n      }), _createVNode(_component_el_table_column, {\n        prop: \"status\",\n        label: \"状态\",\n        width: \"100\"\n      }, {\n        default: _withCtx(({\n          row\n        }) => [_createVNode(_component_el_tag, {\n          type: $setup.getStatusType(row.status)\n        }, {\n          default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.getStatusText(row.status)), 1 /* TEXT */)]),\n          _: 2 /* DYNAMIC */\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"type\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_table_column, {\n        prop: \"maxTeams\",\n        label: \"最大团队数\",\n        width: \"120\"\n      }), _createVNode(_component_el_table_column, {\n        prop: \"currentTeamCount\",\n        label: \"当前团队数\",\n        width: \"120\"\n      }), _createVNode(_component_el_table_column, {\n        prop: \"createTime\",\n        label: \"创建时间\",\n        \"min-width\": \"180\"\n      }), _createVNode(_component_el_table_column, {\n        label: \"操作\",\n        width: \"240\",\n        fixed: \"right\"\n      }, {\n        default: _withCtx(({\n          row\n        }) => [_createVNode(_component_el_button, {\n          size: \"small\",\n          onClick: $event => $setup.viewProject(row)\n        }, {\n          default: _withCtx(() => _cache[9] || (_cache[9] = [_createTextVNode(\"查看\")])),\n          _: 2 /* DYNAMIC */,\n          __: [9]\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"]), _createVNode(_component_el_button, {\n          size: \"small\",\n          type: \"warning\",\n          onClick: $event => $setup.editStatus(row)\n        }, {\n          default: _withCtx(() => _cache[10] || (_cache[10] = [_createTextVNode(\"状态\")])),\n          _: 2 /* DYNAMIC */,\n          __: [10]\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"]), _createVNode(_component_el_button, {\n          size: \"small\",\n          type: \"danger\",\n          onClick: $event => $setup.deleteProject(row)\n        }, {\n          default: _withCtx(() => _cache[11] || (_cache[11] = [_createTextVNode(\"删除\")])),\n          _: 2 /* DYNAMIC */,\n          __: [11]\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"])]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"data\"])), [[_directive_loading, $setup.loading]])]), _createElementVNode(\"div\", _hoisted_5, [_createVNode(_component_el_pagination, {\n      \"current-page\": $setup.currentPage,\n      \"onUpdate:currentPage\": _cache[2] || (_cache[2] = $event => $setup.currentPage = $event),\n      \"page-size\": $setup.pageSize,\n      \"onUpdate:pageSize\": _cache[3] || (_cache[3] = $event => $setup.pageSize = $event),\n      total: $setup.total,\n      \"page-sizes\": [10, 20, 50, 100],\n      layout: \"total, sizes, prev, pager, next, jumper\",\n      onSizeChange: $setup.handleSizeChange,\n      onCurrentChange: $setup.handleCurrentChange\n    }, null, 8 /* PROPS */, [\"current-page\", \"page-size\", \"total\", \"onSizeChange\", \"onCurrentChange\"])])]),\n    _: 1 /* STABLE */\n  }), _createCommentVNode(\" 状态编辑对话框 \"), _createVNode(_component_el_dialog, {\n    modelValue: $setup.statusEditDialog,\n    \"onUpdate:modelValue\": _cache[5] || (_cache[5] = $event => $setup.statusEditDialog = $event),\n    title: \"修改项目状态\",\n    width: \"400px\",\n    \"before-close\": $setup.cancelStatusEdit\n  }, {\n    footer: _withCtx(() => [_createElementVNode(\"span\", _hoisted_9, [_createVNode(_component_el_button, {\n      onClick: $setup.cancelStatusEdit\n    }, {\n      default: _withCtx(() => _cache[15] || (_cache[15] = [_createTextVNode(\"取消\")])),\n      _: 1 /* STABLE */,\n      __: [15]\n    }, 8 /* PROPS */, [\"onClick\"]), _createVNode(_component_el_button, {\n      type: \"primary\",\n      onClick: $setup.confirmStatusEdit,\n      disabled: !$setup.selectedStatus || $setup.selectedStatus === $setup.currentEditProject?.status\n    }, {\n      default: _withCtx(() => _cache[16] || (_cache[16] = [_createTextVNode(\" 确定 \")])),\n      _: 1 /* STABLE */,\n      __: [16]\n    }, 8 /* PROPS */, [\"onClick\", \"disabled\"])])]),\n    default: _withCtx(() => [$setup.currentEditProject ? (_openBlock(), _createElementBlock(\"div\", _hoisted_6, [_createElementVNode(\"p\", _hoisted_7, [_cache[12] || (_cache[12] = _createElementVNode(\"strong\", null, \"项目名称：\", -1 /* CACHED */)), _createTextVNode(_toDisplayString($setup.currentEditProject?.name), 1 /* TEXT */)]), _createElementVNode(\"p\", _hoisted_8, [_cache[13] || (_cache[13] = _createElementVNode(\"strong\", null, \"当前状态：\", -1 /* CACHED */)), _createVNode(_component_el_tag, {\n      type: $setup.getStatusType($setup.currentEditProject?.status)\n    }, {\n      default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.getStatusText($setup.currentEditProject?.status)), 1 /* TEXT */)]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"type\"])]), _cache[14] || (_cache[14] = _createElementVNode(\"p\", {\n      style: {\n        \"margin-bottom\": \"10px\"\n      }\n    }, [_createElementVNode(\"strong\", null, \"选择新状态：\")], -1 /* CACHED */)), _createVNode(_component_el_select, {\n      modelValue: $setup.selectedStatus,\n      \"onUpdate:modelValue\": _cache[4] || (_cache[4] = $event => $setup.selectedStatus = $event),\n      placeholder: \"请选择状态\",\n      style: {\n        \"width\": \"100%\"\n      }\n    }, {\n      default: _withCtx(() => [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.statusOptions, option => {\n        return _openBlock(), _createBlock(_component_el_option, {\n          key: option.value,\n          label: option.label,\n          value: option.value\n        }, null, 8 /* PROPS */, [\"label\", \"value\"]);\n      }), 128 /* KEYED_FRAGMENT */))]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"])])) : _createCommentVNode(\"v-if\", true)]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"before-close\"])]);\n}", "map": {"version": 3, "names": ["class", "style", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_card", "header", "_withCtx", "_createElementVNode", "_hoisted_2", "_component_el_button", "type", "onClick", "_ctx", "loadProjects", "_component_el_icon", "_component_Refresh", "_hoisted_3", "_component_el_input", "$setup", "searchKeyword", "$event", "placeholder", "clearable", "onKeyup", "_with<PERSON><PERSON><PERSON>", "handleSearch", "append", "_component_Search", "_component_el_select", "statusFilter", "onChange", "handleFilter", "_component_el_option", "label", "value", "handleReset", "_component_RefreshLeft", "_hoisted_4", "_createBlock", "_component_el_table", "data", "projects", "stripe", "_component_el_table_column", "prop", "width", "default", "row", "_component_el_tag", "getStatusType", "status", "getStatusText", "fixed", "size", "viewProject", "_cache", "editStatus", "deleteProject", "loading", "_hoisted_5", "_component_el_pagination", "currentPage", "pageSize", "total", "layout", "onSizeChange", "handleSizeChange", "onCurrentChange", "handleCurrentChange", "_createCommentVNode", "_component_el_dialog", "statusEditDialog", "title", "cancelStatusEdit", "footer", "_hoisted_9", "confirmStatusEdit", "disabled", "selectedStatus", "currentEditProject", "_hoisted_6", "_hoisted_7", "name", "_hoisted_8", "_Fragment", "_renderList", "statusOptions", "option", "key"], "sources": ["D:\\workspace\\idea\\worker\\work_cli\\src\\views\\admin\\ProjectManagement.vue"], "sourcesContent": ["<template>\n  <div class=\"project-management\">\n    <el-card>\n      <template #header>\n        <div class=\"card-header\">\n          <h3>项目管理</h3>\n          <el-button type=\"primary\" @click=\"loadProjects\">\n            <el-icon><Refresh /></el-icon>\n            刷新\n          </el-button>\n        </div>\n      </template>\n      <!-- 搜索栏 -->\n      <div class=\"search-bar\">\n        <el-input\n          v-model=\"searchKeyword\"\n          placeholder=\"搜索项目名称或描述...\"\n          style=\"width: 200px\"\n          clearable\n          @keyup.enter=\"handleSearch\"\n        >\n          <template #append>\n            <el-button @click=\"handleSearch\">\n              <el-icon><Search /></el-icon>\n            </el-button>\n          </template>\n        </el-input>\n\n        <el-select\n          v-model=\"statusFilter\"\n          placeholder=\"状态筛选\"\n          style=\"width: 120px; margin-left: 10px\"\n          @change=\"handleFilter\"\n        >\n          <el-option label=\"全部\" value=\"\" />\n          <el-option label=\"已发布\" value=\"PUBLISHED\" />\n          <el-option label=\"进行中\" value=\"IN_PROGRESS\" />\n          <el-option label=\"已完成\" value=\"COMPLETED\" />\n        </el-select>\n\n        <el-button @click=\"handleReset\" style=\"margin-left: 10px\">\n          <el-icon><RefreshLeft /></el-icon>\n          重置\n        </el-button>\n      </div>\n\n      <div class=\"table-container\">\n        <el-table :data=\"projects\" v-loading=\"loading\" stripe style=\"width: 100%; min-width: 1000px;\">\n          <el-table-column prop=\"id\" label=\"ID\" width=\"100\" show-overflow-tooltip />\n          <el-table-column prop=\"name\" label=\"项目名称\" min-width=\"220\" />\n          <el-table-column prop=\"teacherName\" label=\"负责教师\" min-width=\"120\" />\n          <el-table-column prop=\"status\" label=\"状态\" width=\"100\">\n            <template #default=\"{ row }\">\n              <el-tag :type=\"getStatusType(row.status)\">\n                {{ getStatusText(row.status) }}\n              </el-tag>\n            </template>\n          </el-table-column>\n          <el-table-column prop=\"maxTeams\" label=\"最大团队数\" width=\"120\" />\n          <el-table-column prop=\"currentTeamCount\" label=\"当前团队数\" width=\"120\" />\n          <el-table-column prop=\"createTime\" label=\"创建时间\" min-width=\"180\" />\n          <el-table-column label=\"操作\" width=\"240\" fixed=\"right\">\n            <template #default=\"{ row }\">\n              <el-button size=\"small\" @click=\"viewProject(row)\">查看</el-button>\n              <el-button size=\"small\" type=\"warning\" @click=\"editStatus(row)\">状态</el-button>\n              <el-button size=\"small\" type=\"danger\" @click=\"deleteProject(row)\">删除</el-button>\n            </template>\n          </el-table-column>\n        </el-table>\n      </div>\n\n      <div class=\"pagination-container\">\n        <el-pagination\n          v-model:current-page=\"currentPage\"\n          v-model:page-size=\"pageSize\"\n          :total=\"total\"\n          :page-sizes=\"[10, 20, 50, 100]\"\n          layout=\"total, sizes, prev, pager, next, jumper\"\n          @size-change=\"handleSizeChange\"\n          @current-change=\"handleCurrentChange\"\n        />\n      </div>\n    </el-card>\n\n    <!-- 状态编辑对话框 -->\n    <el-dialog\n      v-model=\"statusEditDialog\"\n      title=\"修改项目状态\"\n      width=\"400px\"\n      :before-close=\"cancelStatusEdit\"\n    >\n      <div v-if=\"currentEditProject\">\n        <p style=\"margin-bottom: 15px;\">\n          <strong>项目名称：</strong>{{ currentEditProject?.name }}\n        </p>\n        <p style=\"margin-bottom: 15px;\">\n          <strong>当前状态：</strong>\n          <el-tag :type=\"getStatusType(currentEditProject?.status)\">\n            {{ getStatusText(currentEditProject?.status) }}\n          </el-tag>\n        </p>\n        <p style=\"margin-bottom: 10px;\"><strong>选择新状态：</strong></p>\n        <el-select\n          v-model=\"selectedStatus\"\n          placeholder=\"请选择状态\"\n          style=\"width: 100%\"\n        >\n          <el-option\n            v-for=\"option in statusOptions\"\n            :key=\"option.value\"\n            :label=\"option.label\"\n            :value=\"option.value\"\n          />\n        </el-select>\n      </div>\n\n      <template #footer>\n        <span class=\"dialog-footer\">\n          <el-button @click=\"cancelStatusEdit\">取消</el-button>\n          <el-button\n            type=\"primary\"\n            @click=\"confirmStatusEdit\"\n            :disabled=\"!selectedStatus || selectedStatus === currentEditProject?.status\"\n          >\n            确定\n          </el-button>\n        </span>\n      </template>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { ref, reactive, onMounted, h } from 'vue'\nimport { ElMessage, ElMessageBox } from 'element-plus'\nimport { Search, Refresh, RefreshLeft } from '@element-plus/icons-vue'\nimport { adminAPI } from '@/api/admin'\n\nexport default {\n  name: 'ProjectManagement',\n  components: {\n    Search\n  },\n  setup() {\n    const loading = ref(false)\n    const projects = ref([])\n    const searchKeyword = ref('')\n    const statusFilter = ref('')\n    const currentPage = ref(1)\n    const pageSize = ref(20)\n    const total = ref(0)\n\n    const getStatusType = (status) => {\n      const typeMap = {\n        'PUBLISHED': 'success',\n        'IN_PROGRESS': 'warning',\n        'COMPLETED': 'info'\n      }\n      return typeMap[status] || 'info'\n    }\n\n    const getStatusText = (status) => {\n      const textMap = {\n        'PUBLISHED': '已发布',\n        'IN_PROGRESS': '进行中',\n        'COMPLETED': '已完成'\n      }\n      return textMap[status] || status\n    }\n\n    const loadProjects = async () => {\n      loading.value = true\n      try {\n        const params = {\n          page: currentPage.value,\n          size: pageSize.value\n        }\n\n        // 添加搜索关键词\n        if (searchKeyword.value && searchKeyword.value.trim()) {\n          params.keyword = searchKeyword.value.trim()\n        }\n\n        // 添加状态筛选\n        if (statusFilter.value) {\n          params.status = statusFilter.value\n        }\n\n        const response = await adminAPI.getAllProjects(params)\n\n        if (response && response.records) {\n          projects.value = response.records || []\n          total.value = response.total || 0\n\n          // 处理项目数据，添加教师姓名等信息\n          projects.value = projects.value.map(project => ({\n            ...project,\n            teacherName: project.teacher?.realName || project.teacher?.username || '未知',\n            createTime: formatDateTime(project.createTime),\n            updateTime: formatDateTime(project.updateTime)\n          }))\n        } else {\n          console.error('API返回失败:', response)\n          ElMessage.error(response.message || '获取项目列表失败')\n        }\n      } catch (error) {\n        console.error('加载项目列表失败:', error)\n        console.error('错误详情:', error.response?.data || error)\n        ElMessage.error('加载项目列表失败: ' + (error.response?.data?.message || error.message || '网络错误'))\n      } finally {\n        loading.value = false\n      }\n    }\n\n    // 格式化日期时间\n    const formatDateTime = (dateTime) => {\n      if (!dateTime) return '-'\n      try {\n        return new Date(dateTime).toLocaleString('zh-CN', {\n          year: 'numeric',\n          month: '2-digit',\n          day: '2-digit',\n          hour: '2-digit',\n          minute: '2-digit'\n        })\n      } catch (e) {\n        return dateTime\n      }\n    }\n\n    const handleSearch = () => {\n      currentPage.value = 1\n      loadProjects()\n    }\n\n    const handleFilter = () => {\n      currentPage.value = 1\n      loadProjects()\n    }\n\n    const handleSizeChange = (size) => {\n      pageSize.value = size\n      loadProjects()\n    }\n\n    const handleCurrentChange = (page) => {\n      currentPage.value = page\n      loadProjects()\n    }\n\n    const viewProject = (project) => {\n      ElMessageBox.alert(\n        `\n        <div style=\"text-align: left;\">\n          <p><strong>项目ID:</strong> ${project.id}</p>\n          <p><strong>项目名称:</strong> ${project.name}</p>\n          <p><strong>负责教师:</strong> ${project.teacherName}</p>\n          <p><strong>项目描述:</strong> ${project.description || '暂无描述'}</p>\n          <p><strong>最大团队数:</strong> ${project.maxTeams || 0}</p>\n          <p><strong>当前团队数:</strong> ${project.currentTeamCount || 0}</p>\n          <p><strong>项目状态:</strong> ${getStatusText(project.status)}</p>\n          <p><strong>创建时间:</strong> ${project.createTime}</p>\n        </div>\n        `,\n        '项目详情',\n        {\n          dangerouslyUseHTMLString: true,\n          confirmButtonText: '关闭'\n        }\n      )\n    }\n\n    // 状态编辑相关的响应式数据\n    const statusEditDialog = ref(false)\n    const currentEditProject = ref(null)\n    const selectedStatus = ref('')\n\n    const statusOptions = [\n      { label: '已发布', value: 'PUBLISHED' },\n      { label: '进行中', value: 'IN_PROGRESS' },\n      { label: '已完成', value: 'COMPLETED' }\n    ]\n\n    const editStatus = (project) => {\n      currentEditProject.value = project\n      selectedStatus.value = project.status\n      statusEditDialog.value = true\n    }\n\n    const confirmStatusEdit = async () => {\n      try {\n        if (selectedStatus.value && selectedStatus.value !== currentEditProject.value.status) {\n          await adminAPI.updateProjectStatus(currentEditProject.value.id, selectedStatus.value)\n          ElMessage.success('项目状态更新成功')\n          loadProjects() // 重新加载列表\n        }\n        statusEditDialog.value = false\n      } catch (error) {\n        console.error('更新项目状态失败:', error)\n        ElMessage.error('更新项目状态失败: ' + (error.message || '操作失败'))\n      }\n    }\n\n    const cancelStatusEdit = () => {\n      statusEditDialog.value = false\n      currentEditProject.value = null\n      selectedStatus.value = ''\n    }\n\n    const deleteProject = async (project) => {\n      try {\n        await ElMessageBox.confirm(\n          `确定要删除项目 \"${project.name}\" 吗？\\n\\n注意：只有当项目没有关联团队时才能删除。\\n此操作不可恢复！`,\n          '删除确认',\n          {\n            confirmButtonText: '确定删除',\n            cancelButtonText: '取消',\n            type: 'warning',\n            dangerouslyUseHTMLString: false\n          }\n        )\n\n        // 调用删除API\n        await adminAPI.deleteProject(project.id)\n        ElMessage.success('项目删除成功')\n        loadProjects()\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('删除项目失败:', error)\n\n          // 处理特定的业务错误\n          let errorMessage = '删除项目失败'\n          if (error.response && error.response.data && error.response.data.message) {\n            errorMessage = error.response.data.message\n          } else if (error.message) {\n            errorMessage = error.message\n          }\n\n          // 如果是团队关联错误，显示更详细的提示\n          if (errorMessage.includes('团队关联')) {\n            ElMessageBox.alert(\n              errorMessage + '\\n\\n解决方案：\\n1. 前往团队管理页面解除团队与项目的关联\\n2. 或者删除相关团队\\n3. 然后再尝试删除项目',\n              '无法删除项目',\n              {\n                confirmButtonText: '我知道了',\n                type: 'warning'\n              }\n            )\n          } else {\n            ElMessage.error(errorMessage)\n          }\n        }\n      }\n    }\n\n    onMounted(() => {\n      loadProjects()\n    })\n\n    return {\n      loading,\n      projects,\n      searchKeyword,\n      statusFilter,\n      currentPage,\n      pageSize,\n      total,\n      getStatusType,\n      getStatusText,\n      handleSearch,\n      handleFilter,\n      handleSizeChange,\n      handleCurrentChange,\n      viewProject,\n      editStatus,\n      deleteProject,\n      formatDateTime,\n      // 状态编辑相关\n      statusEditDialog,\n      currentEditProject,\n      selectedStatus,\n      statusOptions,\n      confirmStatusEdit,\n      cancelStatusEdit\n    }\n  }\n}\n</script>\n\n<style scoped>\n.project-management {\n  padding: 24px;\n}\n\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.card-header h3 {\n  margin: 0;\n  font-size: 18px;\n  font-weight: 600;\n  color: #1f2937;\n}\n\n.search-bar {\n  display: flex;\n  align-items: center;\n  margin-bottom: 20px;\n  flex-wrap: wrap;\n}\n\n.table-container {\n  min-height: 400px;\n  overflow-x: auto;\n}\n\n.pagination-container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  padding: var(--space-4) 0;\n  border-top: 1px solid var(--gray-200);\n  margin-top: var(--space-6);\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAoB;;EAGpBA,KAAK,EAAC;AAAa;;EASrBA,KAAK,EAAC;AAAY;;EAiClBA,KAAK,EAAC;AAAiB;;EAyBvBA,KAAK,EAAC;AAAsB;;;;;EAqB5BC,KAA4B,EAA5B;IAAA;EAAA;AAA4B;;EAG5BA,KAA4B,EAA5B;IAAA;EAAA;AAA4B;;EAsBzBD,KAAK,EAAC;AAAe;;;;;;;;;;;;;;;;;uBApHjCE,mBAAA,CAgIM,OAhINC,UAgIM,GA/HJC,YAAA,CAgFUC,kBAAA;IA/EGC,MAAM,EAAAC,QAAA,CACf,MAMM,CANNC,mBAAA,CAMM,OANNC,UAMM,G,0BALJD,mBAAA,CAAa,YAAT,MAAI,qBACRJ,YAAA,CAGYM,oBAAA;MAHDC,IAAI,EAAC,SAAS;MAAEC,OAAK,EAAEC,IAAA,CAAAC;;wBAChC,MAA8B,CAA9BV,YAAA,CAA8BW,kBAAA;0BAArB,MAAW,CAAXX,YAAA,CAAWY,kBAAA,E;;qDAAU,MAEhC,G;;;;sBAIJ,MA+BM,CA/BNR,mBAAA,CA+BM,OA/BNS,UA+BM,GA9BJb,YAAA,CAYWc,mBAAA;kBAXAC,MAAA,CAAAC,aAAa;iEAAbD,MAAA,CAAAC,aAAa,GAAAC,MAAA;MACtBC,WAAW,EAAC,cAAc;MAC1BrB,KAAoB,EAApB;QAAA;MAAA,CAAoB;MACpBsB,SAAS,EAAT,EAAS;MACRC,OAAK,EAAAC,SAAA,CAAQN,MAAA,CAAAO,YAAY;;MAEfC,MAAM,EAAApB,QAAA,CACf,MAEY,CAFZH,YAAA,CAEYM,oBAAA;QAFAE,OAAK,EAAEO,MAAA,CAAAO;MAAY;0BAC7B,MAA6B,CAA7BtB,YAAA,CAA6BW,kBAAA;4BAApB,MAAU,CAAVX,YAAA,CAAUwB,iBAAA,E;;;;;;kDAKzBxB,YAAA,CAUYyB,oBAAA;kBATDV,MAAA,CAAAW,YAAY;iEAAZX,MAAA,CAAAW,YAAY,GAAAT,MAAA;MACrBC,WAAW,EAAC,MAAM;MAClBrB,KAAuC,EAAvC;QAAA;QAAA;MAAA,CAAuC;MACtC8B,QAAM,EAAEZ,MAAA,CAAAa;;wBAET,MAAiC,CAAjC5B,YAAA,CAAiC6B,oBAAA;QAAtBC,KAAK,EAAC,IAAI;QAACC,KAAK,EAAC;UAC5B/B,YAAA,CAA2C6B,oBAAA;QAAhCC,KAAK,EAAC,KAAK;QAACC,KAAK,EAAC;UAC7B/B,YAAA,CAA6C6B,oBAAA;QAAlCC,KAAK,EAAC,KAAK;QAACC,KAAK,EAAC;UAC7B/B,YAAA,CAA2C6B,oBAAA;QAAhCC,KAAK,EAAC,KAAK;QAACC,KAAK,EAAC;;;mDAG/B/B,YAAA,CAGYM,oBAAA;MAHAE,OAAK,EAAEC,IAAA,CAAAuB,WAAW;MAAEnC,KAAyB,EAAzB;QAAA;MAAA;;wBAC9B,MAAkC,CAAlCG,YAAA,CAAkCW,kBAAA;0BAAzB,MAAe,CAAfX,YAAA,CAAeiC,sBAAA,E;;qDAAU,MAEpC,G;;;sCAGF7B,mBAAA,CAuBM,OAvBN8B,UAuBM,G,+BAtBJC,YAAA,CAqBWC,mBAAA;MArBAC,IAAI,EAAEtB,MAAA,CAAAuB,QAAQ;MAAsBC,MAAM,EAAN,EAAM;MAAC1C,KAAuC,EAAvC;QAAA;QAAA;MAAA;;wBACpD,MAA0E,CAA1EG,YAAA,CAA0EwC,0BAAA;QAAzDC,IAAI,EAAC,IAAI;QAACX,KAAK,EAAC,IAAI;QAACY,KAAK,EAAC,KAAK;QAAC,uBAAqB,EAArB;UAClD1C,YAAA,CAA4DwC,0BAAA;QAA3CC,IAAI,EAAC,MAAM;QAACX,KAAK,EAAC,MAAM;QAAC,WAAS,EAAC;UACpD9B,YAAA,CAAmEwC,0BAAA;QAAlDC,IAAI,EAAC,aAAa;QAACX,KAAK,EAAC,MAAM;QAAC,WAAS,EAAC;UAC3D9B,YAAA,CAMkBwC,0BAAA;QANDC,IAAI,EAAC,QAAQ;QAACX,KAAK,EAAC,IAAI;QAACY,KAAK,EAAC;;QACnCC,OAAO,EAAAxC,QAAA,CAChB,CAES;UAHWyC;QAAG,OACvB5C,YAAA,CAES6C,iBAAA;UAFAtC,IAAI,EAAEQ,MAAA,CAAA+B,aAAa,CAACF,GAAG,CAACG,MAAM;;4BACrC,MAA+B,C,kCAA5BhC,MAAA,CAAAiC,aAAa,CAACJ,GAAG,CAACG,MAAM,kB;;;;UAIjC/C,YAAA,CAA6DwC,0BAAA;QAA5CC,IAAI,EAAC,UAAU;QAACX,KAAK,EAAC,OAAO;QAACY,KAAK,EAAC;UACrD1C,YAAA,CAAqEwC,0BAAA;QAApDC,IAAI,EAAC,kBAAkB;QAACX,KAAK,EAAC,OAAO;QAACY,KAAK,EAAC;UAC7D1C,YAAA,CAAkEwC,0BAAA;QAAjDC,IAAI,EAAC,YAAY;QAACX,KAAK,EAAC,MAAM;QAAC,WAAS,EAAC;UAC1D9B,YAAA,CAMkBwC,0BAAA;QANDV,KAAK,EAAC,IAAI;QAACY,KAAK,EAAC,KAAK;QAACO,KAAK,EAAC;;QACjCN,OAAO,EAAAxC,QAAA,CAChB,CAAgE;UAD5CyC;QAAG,OACvB5C,YAAA,CAAgEM,oBAAA;UAArD4C,IAAI,EAAC,OAAO;UAAE1C,OAAK,EAAAS,MAAA,IAAEF,MAAA,CAAAoC,WAAW,CAACP,GAAG;;4BAAG,MAAEQ,MAAA,QAAAA,MAAA,O,iBAAF,IAAE,E;;;0DACpDpD,YAAA,CAA8EM,oBAAA;UAAnE4C,IAAI,EAAC,OAAO;UAAC3C,IAAI,EAAC,SAAS;UAAEC,OAAK,EAAAS,MAAA,IAAEF,MAAA,CAAAsC,UAAU,CAACT,GAAG;;4BAAG,MAAEQ,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;0DAClEpD,YAAA,CAAgFM,oBAAA;UAArE4C,IAAI,EAAC,OAAO;UAAC3C,IAAI,EAAC,QAAQ;UAAEC,OAAK,EAAAS,MAAA,IAAEF,MAAA,CAAAuC,aAAa,CAACV,GAAG;;4BAAG,MAAEQ,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;;;;;wDAlBpCrC,MAAA,CAAAwC,OAAO,E,KAwB/CnD,mBAAA,CAUM,OAVNoD,UAUM,GATJxD,YAAA,CAQEyD,wBAAA;MAPQ,cAAY,EAAE1C,MAAA,CAAA2C,WAAW;kEAAX3C,MAAA,CAAA2C,WAAW,GAAAzC,MAAA;MACzB,WAAS,EAAEF,MAAA,CAAA4C,QAAQ;+DAAR5C,MAAA,CAAA4C,QAAQ,GAAA1C,MAAA;MAC1B2C,KAAK,EAAE7C,MAAA,CAAA6C,KAAK;MACZ,YAAU,EAAE,iBAAiB;MAC9BC,MAAM,EAAC,yCAAyC;MAC/CC,YAAW,EAAE/C,MAAA,CAAAgD,gBAAgB;MAC7BC,eAAc,EAAEjD,MAAA,CAAAkD;;;MAKvBC,mBAAA,aAAgB,EAChBlE,YAAA,CA2CYmE,oBAAA;gBA1CDpD,MAAA,CAAAqD,gBAAgB;+DAAhBrD,MAAA,CAAAqD,gBAAgB,GAAAnD,MAAA;IACzBoD,KAAK,EAAC,QAAQ;IACd3B,KAAK,EAAC,OAAO;IACZ,cAAY,EAAE3B,MAAA,CAAAuD;;IA2BJC,MAAM,EAAApE,QAAA,CACf,MASO,CATPC,mBAAA,CASO,QATPoE,UASO,GARLxE,YAAA,CAAmDM,oBAAA;MAAvCE,OAAK,EAAEO,MAAA,CAAAuD;IAAgB;wBAAE,MAAElB,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;oCACvCpD,YAAA,CAMYM,oBAAA;MALVC,IAAI,EAAC,SAAS;MACbC,OAAK,EAAEO,MAAA,CAAA0D,iBAAiB;MACxBC,QAAQ,GAAG3D,MAAA,CAAA4D,cAAc,IAAI5D,MAAA,CAAA4D,cAAc,KAAK5D,MAAA,CAAA6D,kBAAkB,EAAE7B;;wBACtE,MAEDK,MAAA,SAAAA,MAAA,Q,iBAFC,MAED,E;;;;sBA7B4B,MAyBE,CA9BvBrC,MAAA,CAAA6D,kBAAkB,I,cAA7B9E,mBAAA,CAuBM,OAAA+E,UAAA,GAtBJzE,mBAAA,CAEI,KAFJ0E,UAEI,G,4BADF1E,mBAAA,CAAsB,gBAAd,OAAK,qB,kCAAYW,MAAA,CAAA6D,kBAAkB,EAAEG,IAAI,iB,GAEnD3E,mBAAA,CAKI,KALJ4E,UAKI,G,4BAJF5E,mBAAA,CAAsB,gBAAd,OAAK,qBACbJ,YAAA,CAES6C,iBAAA;MAFAtC,IAAI,EAAEQ,MAAA,CAAA+B,aAAa,CAAC/B,MAAA,CAAA6D,kBAAkB,EAAE7B,MAAM;;wBACrD,MAA+C,C,kCAA5ChC,MAAA,CAAAiC,aAAa,CAACjC,MAAA,CAAA6D,kBAAkB,EAAE7B,MAAM,kB;;+DAG/C3C,mBAAA,CAA2D;MAAxDP,KAA4B,EAA5B;QAAA;MAAA;IAA4B,IAACO,mBAAA,CAAuB,gBAAf,QAAM,E,qBAC9CJ,YAAA,CAWYyB,oBAAA;kBAVDV,MAAA,CAAA4D,cAAc;iEAAd5D,MAAA,CAAA4D,cAAc,GAAA1D,MAAA;MACvBC,WAAW,EAAC,OAAO;MACnBrB,KAAmB,EAAnB;QAAA;MAAA;;wBAGE,MAA+B,E,kBADjCC,mBAAA,CAKEmF,SAAA,QAAAC,WAAA,CAJiBnE,MAAA,CAAAoE,aAAa,EAAvBC,MAAM;6BADfjD,YAAA,CAKEN,oBAAA;UAHCwD,GAAG,EAAED,MAAM,CAACrD,KAAK;UACjBD,KAAK,EAAEsD,MAAM,CAACtD,KAAK;UACnBC,KAAK,EAAEqD,MAAM,CAACrD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}