{"ast": null, "code": "import request from '@/utils/request';\n\n// 文件管理相关API\nexport const fileAPI = {\n  // 上传文件\n  uploadFile(file, options = {}) {\n    const formData = new FormData();\n    formData.append('file', file);\n\n    // 添加可选参数\n    if (options.projectId) formData.append('projectId', options.projectId);\n    if (options.teamId) formData.append('teamId', options.teamId);\n    if (options.recordId) formData.append('recordId', options.recordId);\n    if (options.fileType) formData.append('fileType', options.fileType);\n    if (options.description) formData.append('description', options.description);\n    return request.post('/files/upload', formData, {\n      headers: {\n        'Content-Type': 'multipart/form-data'\n      },\n      onUploadProgress: options.onProgress\n    });\n  },\n  // 下载文件\n  downloadFile(fileId) {\n    return request.get(`/files/${fileId}/download`, {\n      responseType: 'blob'\n    });\n  },\n  // 预览文件\n  previewFile(fileId) {\n    return request.get(`/files/${fileId}/preview`, {\n      responseType: 'blob'\n    });\n  },\n  // 获取文件详情\n  getFileInfo(fileId) {\n    return request.get(`/files/${fileId}`);\n  },\n  // 根据记录ID获取文件列表\n  getFilesByRecord(recordId) {\n    return request.get(`/files/record/${recordId}`);\n  },\n  // 更新文件信息\n  updateFileInfo(fileId, data) {\n    const formData = new FormData();\n    if (data.description) formData.append('description', data.description);\n    return request.put(`/files/${fileId}`, formData);\n  },\n  // 删除文件\n  deleteFile(fileId) {\n    return request.delete(`/files/${fileId}`);\n  },\n  // 获取所有文件\n  getAllFiles(params = {}) {\n    return request.get('/files', {\n      params\n    });\n  },\n  // 获取我的文件\n  getMyFiles(params = {}) {\n    return request.get('/files/my', {\n      params\n    });\n  },\n  // 根据记录ID获取文件\n  getFilesByRecord(recordId) {\n    return request.get(`/files/record/${recordId}`);\n  },\n  // 获取项目文件\n  getProjectFiles(projectId, params = {}) {\n    return request.get(`/files/project/${projectId}`, {\n      params\n    });\n  },\n  // 获取团队文件\n  getTeamFiles(teamId, params = {}) {\n    return request.get(`/files/team/${teamId}`, {\n      params\n    });\n  },\n  // 获取记录附件\n  getRecordFiles(recordId) {\n    return request.get(`/files/record/${recordId}`);\n  },\n  // 根据类型获取文件\n  getFilesByType(fileType, params = {}) {\n    return request.get(`/files/type/${fileType}`, {\n      params\n    });\n  },\n  // 搜索文件\n  searchFiles(keyword, params = {}) {\n    return request.get('/files/search', {\n      params: {\n        keyword,\n        ...params\n      }\n    });\n  },\n  // 高级搜索文件\n  advancedSearchFiles(searchParams = {}) {\n    return request.get('/files/advanced-search', {\n      params: searchParams\n    });\n  },\n  // 协作空间相关API\n  // 上传文件到协作空间\n  uploadCollaborationFile(file, options = {}) {\n    const formData = new FormData();\n    formData.append('file', file);\n    formData.append('teamId', options.teamId);\n    if (options.description) formData.append('description', options.description);\n    return request.post('/files/collaboration/upload', formData, {\n      headers: {\n        'Content-Type': 'multipart/form-data'\n      },\n      onUploadProgress: options.onProgress\n    });\n  },\n  // 获取协作空间文件列表\n  getCollaborationFiles(teamId, params = {}) {\n    return request.get(`/files/collaboration/team/${teamId}`, {\n      params\n    });\n  },\n  // 获取协作空间统计信息\n  getCollaborationStats(teamId) {\n    return request.get(`/files/collaboration/team/${teamId}/stats`);\n  },\n  // 调试 - 获取所有文件信息\n  getAllFilesForDebug() {\n    return request.get('/files/debug/all-files');\n  },\n  // 获取最近文件\n  getRecentFiles(limit = 10) {\n    return request.get('/files/recent', {\n      params: {\n        limit\n      }\n    });\n  },\n  // 获取热门文件\n  getPopularFiles(limit = 10) {\n    return request.get('/files/popular', {\n      params: {\n        limit\n      }\n    });\n  },\n  // 批量删除文件\n  batchDeleteFiles(fileIds) {\n    return request.delete('/files/batch', {\n      data: fileIds\n    });\n  },\n  // 获取文件统计\n  getFileStatistics() {\n    return request.get('/files/statistics');\n  },\n  // 获取项目文件统计\n  getProjectFileStatistics(projectId) {\n    return request.get(`/files/statistics/project/${projectId}`);\n  },\n  // 获取团队文件统计\n  getTeamFileStatistics(teamId) {\n    return request.get(`/files/statistics/team/${teamId}`);\n  },\n  // 清理孤儿文件\n  cleanupOrphanFiles() {\n    return request.post('/files/cleanup');\n  }\n};\n\n// 仪表盘统计相关API\nexport const dashboardAPI = {\n  // 获取仪表盘统计数据\n  getDashboardStats() {\n    return request.get('/api/dashboard/stats');\n  },\n  // 测试用户信息获取\n  testUserInfo() {\n    return request.get('/api/dashboard/test-user');\n  },\n  // 获取最新项目\n  getLatestProjects(limit = 5) {\n    return request.get('/projects/latest', {\n      params: {\n        limit\n      }\n    });\n  },\n  // 获取最新活动\n  getLatestActivities(limit = 10) {\n    return request.get('/records/latest', {\n      params: {\n        limit\n      }\n    });\n  }\n};\n\n// 用户认证相关API\nexport const authAPI = {\n  // 用户注册\n  register(userData) {\n    return request.post('/auth/register', userData);\n  },\n  // 用户登录\n  login(credentials) {\n    return request.post('/auth/login', credentials);\n  },\n  // 刷新token\n  refreshToken() {\n    return request.post('/auth/refresh');\n  },\n  // 获取当前用户信息\n  getCurrentUser() {\n    return request.get('/auth/me');\n  },\n  // 用户登出\n  logout() {\n    return request.post('/auth/logout');\n  },\n  // 更新个人资料\n  updateProfile(profileData) {\n    return request.put('/users/me', profileData);\n  },\n  // 修改密码\n  changePassword(passwordData) {\n    return request.put('/users/me/password', passwordData);\n  }\n};\n\n// 项目管理相关API\nexport const projectAPI = {\n  // 获取项目列表\n  getProjects(params = {}) {\n    return request.get('/projects', {\n      params\n    });\n  },\n  // 获取我的项目列表\n  getMyProjects(params = {}) {\n    return request.get('/projects/my', {\n      params\n    });\n  },\n  // 获取项目详情\n  getProject(id) {\n    return request.get(`/projects/${id}`);\n  },\n  // 创建项目\n  createProject(projectData) {\n    return request.post('/projects', projectData);\n  },\n  // 更新项目\n  updateProject(id, projectData) {\n    return request.put(`/projects/${id}`, projectData);\n  },\n  // 删除项目\n  deleteProject(id) {\n    return request.delete(`/projects/${id}`);\n  },\n  // 启动项目\n  startProject(id) {\n    return request.put(`/projects/${id}/status`, {\n      status: 'IN_PROGRESS'\n    });\n  },\n  // 终止项目\n  terminateProject(id) {\n    return request.post(`/projects/${id}/terminate`);\n  },\n  // 搜索项目\n  searchProjects(keyword, params = {}) {\n    return request.get('/projects/search', {\n      params: {\n        keyword,\n        ...params\n      }\n    });\n  },\n  // 获取热门项目\n  getPopularProjects(params = {}) {\n    return request.get('/projects/popular', {\n      params\n    });\n  },\n  // 获取我的项目\n  getMyProjects(params = {}) {\n    return request.get('/projects/my', {\n      params\n    });\n  },\n  // 获取项目统计\n  getProjectStats(id) {\n    return request.get(`/projects/${id}/stats`);\n  },\n  // 获取全局项目统计\n  getGlobalProjectStats() {\n    return request.get('/projects/stats');\n  },\n  // 获取最新项目\n  getLatestProjects(limit = 10) {\n    return request.get('/projects/latest', {\n      params: {\n        limit\n      }\n    });\n  }\n};\n\n// 团队管理相关API\nexport const teamAPI = {\n  // 获取团队列表\n  getTeams(params = {}) {\n    return request.get('/teams', {\n      params\n    });\n  },\n  // 获取团队详情\n  getTeam(id) {\n    return request.get(`/teams/${id}`);\n  },\n  // 创建团队\n  createTeam(teamData) {\n    return request.post('/teams', teamData);\n  },\n  // 更新团队\n  updateTeam(id, teamData) {\n    return request.put(`/teams/${id}`, teamData);\n  },\n  // 停止招募\n  stopRecruiting(id) {\n    return request.put(`/teams/${id}/stop-recruiting`);\n  },\n  // 解散团队\n  deleteTeam(id) {\n    return request.delete(`/teams/${id}`);\n  },\n  // 解散团队（别名）\n  disbandTeam(id) {\n    return request.delete(`/teams/${id}`);\n  },\n  // 申请加入团队\n  joinTeam(teamId, reason = '') {\n    return request.post(`/teams/${teamId}/join`, {\n      reason\n    });\n  },\n  // 离开团队\n  leaveTeam(teamId) {\n    return request.post(`/teams/${teamId}/leave`);\n  },\n  // 获取我的团队（单个团队）\n  getMyTeam() {\n    return request.get('/teams/my');\n  },\n  // 获取我参与的团队\n  getJoinedTeams(params = {}) {\n    return request.get('/teams/joined', {\n      params\n    });\n  },\n  // 获取项目的团队列表（教师专用）\n  getProjectTeams(projectId, params = {}) {\n    return request.get(`/teams/project/${projectId}`, {\n      params\n    });\n  },\n  // 申请项目\n  applyProject(teamId, projectId, applicationData) {\n    return request.post(`/teams/${teamId}/apply/${projectId}`, applicationData);\n  },\n  // 获取团队成员\n  getTeamMembers(teamId) {\n    return request.get(`/teams/${teamId}/members`);\n  },\n  // 移除团队成员\n  removeTeamMember(teamId, userId) {\n    return request.delete(`/teams/${teamId}/members/${userId}`);\n  },\n  // 兼容性方法（保持向后兼容）\n  removeMember(teamId, userId) {\n    return this.removeTeamMember(teamId, userId);\n  },\n  // 获取团队统计\n  getTeamStats() {\n    return request.get('/teams/stats');\n  },\n  // 获取团队申请列表（队长）\n  getTeamApplications(teamId, params = {}) {\n    return request.get(`/teams/${teamId}/applications`, {\n      params\n    });\n  },\n  // 审核团队申请\n  reviewApplication(applicationId, data) {\n    return request.put(`/teams/applications/${applicationId}/review`, data);\n  },\n  // 获取待审核申请列表（队长）\n  getPendingApplications(params = {}) {\n    return request.get('/teams/applications/pending', {\n      params\n    });\n  },\n  // 取消申请\n  cancelApplication(applicationId) {\n    return request.delete(`/teams/applications/${applicationId}`);\n  },\n  // 获取我的申请状态\n  getMyApplication() {\n    return request.get('/teams/applications/my');\n  },\n  // 教师移除团队\n  removeTeamByTeacher(teamId) {\n    return request.delete(`/teams/${teamId}/remove`);\n  }\n};\n\n// 记录管理相关API\nexport const recordAPI = {\n  // 获取记录列表\n  getRecords(params = {}) {\n    return request.get('/records', {\n      params\n    });\n  },\n  // 获取项目记录\n  getProjectRecords(projectId, params = {}) {\n    return request.get(`/records/projects/${projectId}`, {\n      params\n    });\n  },\n  // 获取团队记录\n  getTeamRecords(teamId, params = {}) {\n    return request.get(`/records/teams/${teamId}`, {\n      params\n    });\n  },\n  // 获取团队讨论\n  getTeamDiscussions(teamId, params = {}) {\n    return request.get(`/records/teams/${teamId}/discussions`, {\n      params\n    });\n  },\n  // 创建记录\n  createRecord(recordData) {\n    return request.post('/records', recordData);\n  },\n  // 更新记录\n  updateRecord(id, recordData) {\n    return request.put(`/records/${id}`, recordData);\n  },\n  // 删除记录\n  deleteRecord(id) {\n    return request.delete(`/records/${id}`);\n  },\n  // 学生提交任务\n  submitTask(taskId, data) {\n    return request.post(`/records/tasks/${taskId}/submit`, data);\n  },\n  // 学生提交任务（支持文件上传）\n  submitTaskWithFiles(taskId, formData) {\n    return request.post(`/records/tasks/${taskId}/submit`, formData, {\n      headers: {\n        'Content-Type': 'multipart/form-data'\n      }\n    });\n  },\n  // 更新任务状态\n  updateTaskStatus(taskId, status) {\n    return request.put(`/records/tasks/${taskId}/status`, {\n      status\n    });\n  },\n  // 获取单个记录\n  getRecord(recordId) {\n    return request.get(`/records/${recordId}`);\n  },\n  // 教师审核任务\n  reviewTask(taskId, data) {\n    return request.post(`/records/tasks/${taskId}/review`, data);\n  },\n  // 获取项目讨论\n  getProjectDiscussions(projectId, params = {}) {\n    return request.get(`/records/projects/${projectId}/discussions`, {\n      params\n    });\n  },\n  // 获取项目进度\n  getProjectProgress(projectId, params = {}) {\n    return request.get(`/records/projects/${projectId}/progress`, {\n      params\n    });\n  },\n  // 获取记录统计\n  getRecordStats() {\n    return request.get('/records/stats');\n  },\n  // 获取最新记录\n  getLatestRecords(limit = 10) {\n    return request.get('/records/latest', {\n      params: {\n        limit\n      }\n    });\n  },\n  // 获取讨论回复\n  getDiscussionReplies(discussionId) {\n    return request.get(`/records/${discussionId}/replies`);\n  }\n};\n\n// 用户管理相关API\nexport const userAPI = {\n  // 搜索用户\n  searchUsers(params = {}) {\n    return request.get('/users', {\n      params\n    });\n  },\n  // 获取用户详情\n  getUser(id) {\n    return request.get(`/users/${id}`);\n  },\n  // 获取用户列表\n  getUsers(params = {}) {\n    return request.get('/users', {\n      params\n    });\n  }\n};\n\n// 审核管理相关API\nexport const reviewAPI = {\n  // 获取申请列表\n  getApplications(params = {}) {\n    return request.get('/applications', {\n      params\n    });\n  },\n  // 获取待审核申请\n  getPendingApplications(params = {}) {\n    return request.get('/applications/pending', {\n      params\n    });\n  },\n  // 通过申请\n  approveApplication(id) {\n    return request.put(`/applications/${id}/approve`);\n  },\n  // 拒绝申请\n  rejectApplication(id) {\n    return request.put(`/applications/${id}/reject`);\n  },\n  // 获取申请详情\n  getApplication(id) {\n    return request.get(`/applications/${id}`);\n  },\n  // 审核团队申请\n  reviewApplication(teamId, reviewData) {\n    return request.put(`/teams/${teamId}/review`, reviewData);\n  },\n  // 批量审核\n  batchReview(reviewData) {\n    return request.post('/teams/batch-review', reviewData);\n  }\n};\n\n// 公告管理相关API（普通用户）\nexport const announcementAPI = {\n  // 获取公告列表（普通用户）\n  getAnnouncements(params = {}) {\n    return request.get('/users/announcements/dashboard', {\n      params\n    });\n  },\n  // 获取公告详情\n  getAnnouncement(id) {\n    return request.get(`/users/announcements/${id}`);\n  },\n  // 增加公告查看次数\n  incrementViewCount(id) {\n    return request.post(`/users/announcements/${id}/view`);\n  },\n  // 获取最新公告（仪表盘用）\n  getLatestAnnouncements(limit = 5) {\n    return request.get('/users/announcements/dashboard', {\n      params: {\n        size: limit\n      }\n    });\n  }\n};\n\n// 导入新的API模块\nexport { default as authApi } from './auth';\nexport { default as projectApi } from './project';\nexport { default as teamApi } from './team';\nexport { default as userApi } from './user';\nexport { default as evaluationApi } from './evaluation';\nexport { default as fileApi } from './file';\nexport { default as recordApi } from './record';\nexport { default as notificationApi } from './notification';\nexport { default as adminApi } from './admin';\nexport default {\n  authAPI,\n  projectAPI,\n  teamAPI,\n  recordAPI,\n  reviewAPI,\n  userAPI,\n  fileAPI,\n  dashboardAPI,\n  announcementAPI\n};", "map": {"version": 3, "names": ["request", "fileAPI", "uploadFile", "file", "options", "formData", "FormData", "append", "projectId", "teamId", "recordId", "fileType", "description", "post", "headers", "onUploadProgress", "onProgress", "downloadFile", "fileId", "get", "responseType", "previewFile", "getFileInfo", "getFilesByRecord", "updateFileInfo", "data", "put", "deleteFile", "delete", "getAllFiles", "params", "getMyFiles", "getProjectFiles", "getTeamFiles", "getRecordFiles", "getFilesByType", "searchFiles", "keyword", "advancedSearchFiles", "searchParams", "uploadCollaborationFile", "getCollaborationFiles", "getCollaborationStats", "getAllFilesForDebug", "getRecentFiles", "limit", "getPopularFiles", "batchDeleteFiles", "fileIds", "getFileStatistics", "getProjectFileStatistics", "getTeamFileStatistics", "cleanupOrphanFiles", "dashboardAPI", "getDashboardStats", "testUserInfo", "getLatestProjects", "getLatestActivities", "authAPI", "register", "userData", "login", "credentials", "refreshToken", "getCurrentUser", "logout", "updateProfile", "profileData", "changePassword", "passwordData", "projectAPI", "getProjects", "getMyProjects", "getProject", "id", "createProject", "projectData", "updateProject", "deleteProject", "startProject", "status", "terminateProject", "searchProjects", "getPopularProjects", "getProjectStats", "getGlobalProjectStats", "teamAPI", "getTeams", "getTeam", "createTeam", "teamData", "updateTeam", "stopRecruiting", "deleteTeam", "disbandTeam", "joinTeam", "reason", "leaveTeam", "getMyTeam", "getJoinedTeams", "getProjectTeams", "applyProject", "applicationData", "getTeamMembers", "removeTeamMember", "userId", "removeMember", "getTeamStats", "getTeamApplications", "reviewApplication", "applicationId", "getPendingApplications", "cancelApplication", "getMyApplication", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recordAPI", "getRecords", "getProjectRecords", "getTeamRecords", "getTeamDiscussions", "createRecord", "recordData", "updateRecord", "deleteRecord", "submitTask", "taskId", "submitTaskWithFiles", "updateTaskStatus", "getRecord", "reviewTask", "getProjectDiscussions", "getProjectProgress", "getRecordStats", "getLatestRecords", "getDiscussionReplies", "discussionId", "userAPI", "searchUsers", "getUser", "getUsers", "reviewAPI", "getApplications", "approveApplication", "rejectApplication", "getApplication", "reviewData", "batchReview", "announcementAPI", "getAnnouncements", "getAnnouncement", "incrementViewCount", "getLatestAnnouncements", "size", "default", "authApi", "projectApi", "teamApi", "userApi", "evaluationApi", "fileApi", "recordApi", "notificationApi", "adminApi"], "sources": ["D:/workspace/idea/worker/work_cli/src/api/index.js"], "sourcesContent": ["import request from '@/utils/request'\n\n// 文件管理相关API\nexport const fileAPI = {\n  // 上传文件\n  uploadFile(file, options = {}) {\n    const formData = new FormData()\n    formData.append('file', file)\n\n    // 添加可选参数\n    if (options.projectId) formData.append('projectId', options.projectId)\n    if (options.teamId) formData.append('teamId', options.teamId)\n    if (options.recordId) formData.append('recordId', options.recordId)\n    if (options.fileType) formData.append('fileType', options.fileType)\n    if (options.description) formData.append('description', options.description)\n\n    return request.post('/files/upload', formData, {\n      headers: {\n        'Content-Type': 'multipart/form-data'\n      },\n      onUploadProgress: options.onProgress\n    })\n  },\n\n  // 下载文件\n  downloadFile(fileId) {\n    return request.get(`/files/${fileId}/download`, {\n      responseType: 'blob'\n    })\n  },\n\n  // 预览文件\n  previewFile(fileId) {\n    return request.get(`/files/${fileId}/preview`, {\n      responseType: 'blob'\n    })\n  },\n\n  // 获取文件详情\n  getFileInfo(fileId) {\n    return request.get(`/files/${fileId}`)\n  },\n\n  // 根据记录ID获取文件列表\n  getFilesByRecord(recordId) {\n    return request.get(`/files/record/${recordId}`)\n  },\n\n  // 更新文件信息\n  updateFileInfo(fileId, data) {\n    const formData = new FormData()\n    if (data.description) formData.append('description', data.description)\n\n    return request.put(`/files/${fileId}`, formData)\n  },\n\n  // 删除文件\n  deleteFile(fileId) {\n    return request.delete(`/files/${fileId}`)\n  },\n\n  // 获取所有文件\n  getAllFiles(params = {}) {\n    return request.get('/files', { params })\n  },\n\n  // 获取我的文件\n  getMyFiles(params = {}) {\n    return request.get('/files/my', { params })\n  },\n\n  // 根据记录ID获取文件\n  getFilesByRecord(recordId) {\n    return request.get(`/files/record/${recordId}`)\n  },\n\n  // 获取项目文件\n  getProjectFiles(projectId, params = {}) {\n    return request.get(`/files/project/${projectId}`, { params })\n  },\n\n  // 获取团队文件\n  getTeamFiles(teamId, params = {}) {\n    return request.get(`/files/team/${teamId}`, { params })\n  },\n\n  // 获取记录附件\n  getRecordFiles(recordId) {\n    return request.get(`/files/record/${recordId}`)\n  },\n\n  // 根据类型获取文件\n  getFilesByType(fileType, params = {}) {\n    return request.get(`/files/type/${fileType}`, { params })\n  },\n\n  // 搜索文件\n  searchFiles(keyword, params = {}) {\n    return request.get('/files/search', {\n      params: { keyword, ...params }\n    })\n  },\n\n  // 高级搜索文件\n  advancedSearchFiles(searchParams = {}) {\n    return request.get('/files/advanced-search', { params: searchParams })\n  },\n\n  // 协作空间相关API\n  // 上传文件到协作空间\n  uploadCollaborationFile(file, options = {}) {\n    const formData = new FormData()\n    formData.append('file', file)\n    formData.append('teamId', options.teamId)\n    if (options.description) formData.append('description', options.description)\n\n    return request.post('/files/collaboration/upload', formData, {\n      headers: {\n        'Content-Type': 'multipart/form-data'\n      },\n      onUploadProgress: options.onProgress\n    })\n  },\n\n  // 获取协作空间文件列表\n  getCollaborationFiles(teamId, params = {}) {\n    return request.get(`/files/collaboration/team/${teamId}`, { params })\n  },\n\n  // 获取协作空间统计信息\n  getCollaborationStats(teamId) {\n    return request.get(`/files/collaboration/team/${teamId}/stats`)\n  },\n\n  // 调试 - 获取所有文件信息\n  getAllFilesForDebug() {\n    return request.get('/files/debug/all-files')\n  },\n\n  // 获取最近文件\n  getRecentFiles(limit = 10) {\n    return request.get('/files/recent', { params: { limit } })\n  },\n\n  // 获取热门文件\n  getPopularFiles(limit = 10) {\n    return request.get('/files/popular', { params: { limit } })\n  },\n\n  // 批量删除文件\n  batchDeleteFiles(fileIds) {\n    return request.delete('/files/batch', { data: fileIds })\n  },\n\n  // 获取文件统计\n  getFileStatistics() {\n    return request.get('/files/statistics')\n  },\n\n  // 获取项目文件统计\n  getProjectFileStatistics(projectId) {\n    return request.get(`/files/statistics/project/${projectId}`)\n  },\n\n  // 获取团队文件统计\n  getTeamFileStatistics(teamId) {\n    return request.get(`/files/statistics/team/${teamId}`)\n  },\n\n  // 清理孤儿文件\n  cleanupOrphanFiles() {\n    return request.post('/files/cleanup')\n  }\n}\n\n// 仪表盘统计相关API\nexport const dashboardAPI = {\n  // 获取仪表盘统计数据\n  getDashboardStats() {\n    return request.get('/api/dashboard/stats')\n  },\n\n  // 测试用户信息获取\n  testUserInfo() {\n    return request.get('/api/dashboard/test-user')\n  },\n\n  // 获取最新项目\n  getLatestProjects(limit = 5) {\n    return request.get('/projects/latest', { params: { limit } })\n  },\n\n  // 获取最新活动\n  getLatestActivities(limit = 10) {\n    return request.get('/records/latest', { params: { limit } })\n  }\n}\n\n// 用户认证相关API\nexport const authAPI = {\n  // 用户注册\n  register(userData) {\n    return request.post('/auth/register', userData)\n  },\n\n  // 用户登录\n  login(credentials) {\n    return request.post('/auth/login', credentials)\n  },\n\n  // 刷新token\n  refreshToken() {\n    return request.post('/auth/refresh')\n  },\n\n  // 获取当前用户信息\n  getCurrentUser() {\n    return request.get('/auth/me')\n  },\n\n  // 用户登出\n  logout() {\n    return request.post('/auth/logout')\n  },\n\n  // 更新个人资料\n  updateProfile(profileData) {\n    return request.put('/users/me', profileData)\n  },\n\n  // 修改密码\n  changePassword(passwordData) {\n    return request.put('/users/me/password', passwordData)\n  }\n}\n\n// 项目管理相关API\nexport const projectAPI = {\n  // 获取项目列表\n  getProjects(params = {}) {\n    return request.get('/projects', { params })\n  },\n\n  // 获取我的项目列表\n  getMyProjects(params = {}) {\n    return request.get('/projects/my', { params })\n  },\n\n  // 获取项目详情\n  getProject(id) {\n    return request.get(`/projects/${id}`)\n  },\n\n  // 创建项目\n  createProject(projectData) {\n    return request.post('/projects', projectData)\n  },\n\n  // 更新项目\n  updateProject(id, projectData) {\n    return request.put(`/projects/${id}`, projectData)\n  },\n\n  // 删除项目\n  deleteProject(id) {\n    return request.delete(`/projects/${id}`)\n  },\n\n  // 启动项目\n  startProject(id) {\n    return request.put(`/projects/${id}/status`, { status: 'IN_PROGRESS' })\n  },\n\n  // 终止项目\n  terminateProject(id) {\n    return request.post(`/projects/${id}/terminate`)\n  },\n\n  // 搜索项目\n  searchProjects(keyword, params = {}) {\n    return request.get('/projects/search', {\n      params: { keyword, ...params }\n    })\n  },\n\n  // 获取热门项目\n  getPopularProjects(params = {}) {\n    return request.get('/projects/popular', { params })\n  },\n\n  // 获取我的项目\n  getMyProjects(params = {}) {\n    return request.get('/projects/my', { params })\n  },\n\n  // 获取项目统计\n  getProjectStats(id) {\n    return request.get(`/projects/${id}/stats`)\n  },\n\n  // 获取全局项目统计\n  getGlobalProjectStats() {\n    return request.get('/projects/stats')\n  },\n\n  // 获取最新项目\n  getLatestProjects(limit = 10) {\n    return request.get('/projects/latest', { params: { limit } })\n  }\n}\n\n// 团队管理相关API\nexport const teamAPI = {\n  // 获取团队列表\n  getTeams(params = {}) {\n    return request.get('/teams', { params })\n  },\n\n  // 获取团队详情\n  getTeam(id) {\n    return request.get(`/teams/${id}`)\n  },\n\n  // 创建团队\n  createTeam(teamData) {\n    return request.post('/teams', teamData)\n  },\n\n  // 更新团队\n  updateTeam(id, teamData) {\n    return request.put(`/teams/${id}`, teamData)\n  },\n\n  // 停止招募\n  stopRecruiting(id) {\n    return request.put(`/teams/${id}/stop-recruiting`)\n  },\n\n  // 解散团队\n  deleteTeam(id) {\n    return request.delete(`/teams/${id}`)\n  },\n\n  // 解散团队（别名）\n  disbandTeam(id) {\n    return request.delete(`/teams/${id}`)\n  },\n\n  // 申请加入团队\n  joinTeam(teamId, reason = '') {\n    return request.post(`/teams/${teamId}/join`, { reason })\n  },\n\n  // 离开团队\n  leaveTeam(teamId) {\n    return request.post(`/teams/${teamId}/leave`)\n  },\n\n  // 获取我的团队（单个团队）\n  getMyTeam() {\n    return request.get('/teams/my')\n  },\n\n  // 获取我参与的团队\n  getJoinedTeams(params = {}) {\n    return request.get('/teams/joined', { params })\n  },\n\n  // 获取项目的团队列表（教师专用）\n  getProjectTeams(projectId, params = {}) {\n    return request.get(`/teams/project/${projectId}`, { params })\n  },\n\n  // 申请项目\n  applyProject(teamId, projectId, applicationData) {\n    return request.post(`/teams/${teamId}/apply/${projectId}`, applicationData)\n  },\n\n  // 获取团队成员\n  getTeamMembers(teamId) {\n    return request.get(`/teams/${teamId}/members`)\n  },\n\n  // 移除团队成员\n  removeTeamMember(teamId, userId) {\n    return request.delete(`/teams/${teamId}/members/${userId}`)\n  },\n\n  // 兼容性方法（保持向后兼容）\n  removeMember(teamId, userId) {\n    return this.removeTeamMember(teamId, userId)\n  },\n\n  // 获取团队统计\n  getTeamStats() {\n    return request.get('/teams/stats')\n  },\n\n  // 获取团队申请列表（队长）\n  getTeamApplications(teamId, params = {}) {\n    return request.get(`/teams/${teamId}/applications`, { params })\n  },\n\n  // 审核团队申请\n  reviewApplication(applicationId, data) {\n    return request.put(`/teams/applications/${applicationId}/review`, data)\n  },\n\n  // 获取待审核申请列表（队长）\n  getPendingApplications(params = {}) {\n    return request.get('/teams/applications/pending', { params })\n  },\n\n  // 取消申请\n  cancelApplication(applicationId) {\n    return request.delete(`/teams/applications/${applicationId}`)\n  },\n\n  // 获取我的申请状态\n  getMyApplication() {\n    return request.get('/teams/applications/my')\n  },\n\n  // 教师移除团队\n  removeTeamByTeacher(teamId) {\n    return request.delete(`/teams/${teamId}/remove`)\n  }\n}\n\n// 记录管理相关API\nexport const recordAPI = {\n  // 获取记录列表\n  getRecords(params = {}) {\n    return request.get('/records', { params })\n  },\n\n  // 获取项目记录\n  getProjectRecords(projectId, params = {}) {\n    return request.get(`/records/projects/${projectId}`, { params })\n  },\n\n  // 获取团队记录\n  getTeamRecords(teamId, params = {}) {\n    return request.get(`/records/teams/${teamId}`, { params })\n  },\n\n  // 获取团队讨论\n  getTeamDiscussions(teamId, params = {}) {\n    return request.get(`/records/teams/${teamId}/discussions`, { params })\n  },\n\n  // 创建记录\n  createRecord(recordData) {\n    return request.post('/records', recordData)\n  },\n\n  // 更新记录\n  updateRecord(id, recordData) {\n    return request.put(`/records/${id}`, recordData)\n  },\n\n  // 删除记录\n  deleteRecord(id) {\n    return request.delete(`/records/${id}`)\n  },\n\n  // 学生提交任务\n  submitTask(taskId, data) {\n    return request.post(`/records/tasks/${taskId}/submit`, data)\n  },\n\n  // 学生提交任务（支持文件上传）\n  submitTaskWithFiles(taskId, formData) {\n    return request.post(`/records/tasks/${taskId}/submit`, formData, {\n      headers: {\n        'Content-Type': 'multipart/form-data'\n      }\n    })\n  },\n\n  // 更新任务状态\n  updateTaskStatus(taskId, status) {\n    return request.put(`/records/tasks/${taskId}/status`, { status })\n  },\n\n  // 获取单个记录\n  getRecord(recordId) {\n    return request.get(`/records/${recordId}`)\n  },\n\n  // 教师审核任务\n  reviewTask(taskId, data) {\n    return request.post(`/records/tasks/${taskId}/review`, data)\n  },\n\n  // 获取项目讨论\n  getProjectDiscussions(projectId, params = {}) {\n    return request.get(`/records/projects/${projectId}/discussions`, { params })\n  },\n\n\n\n  // 获取项目进度\n  getProjectProgress(projectId, params = {}) {\n    return request.get(`/records/projects/${projectId}/progress`, { params })\n  },\n\n  // 获取记录统计\n  getRecordStats() {\n    return request.get('/records/stats')\n  },\n\n  // 获取最新记录\n  getLatestRecords(limit = 10) {\n    return request.get('/records/latest', { params: { limit } })\n  },\n\n  // 获取讨论回复\n  getDiscussionReplies(discussionId) {\n    return request.get(`/records/${discussionId}/replies`)\n  }\n}\n\n// 用户管理相关API\nexport const userAPI = {\n  // 搜索用户\n  searchUsers(params = {}) {\n    return request.get('/users', { params })\n  },\n\n  // 获取用户详情\n  getUser(id) {\n    return request.get(`/users/${id}`)\n  },\n\n  // 获取用户列表\n  getUsers(params = {}) {\n    return request.get('/users', { params })\n  }\n}\n\n// 审核管理相关API\nexport const reviewAPI = {\n  // 获取申请列表\n  getApplications(params = {}) {\n    return request.get('/applications', { params })\n  },\n\n  // 获取待审核申请\n  getPendingApplications(params = {}) {\n    return request.get('/applications/pending', { params })\n  },\n\n  // 通过申请\n  approveApplication(id) {\n    return request.put(`/applications/${id}/approve`)\n  },\n\n  // 拒绝申请\n  rejectApplication(id) {\n    return request.put(`/applications/${id}/reject`)\n  },\n\n  // 获取申请详情\n  getApplication(id) {\n    return request.get(`/applications/${id}`)\n  },\n\n  // 审核团队申请\n  reviewApplication(teamId, reviewData) {\n    return request.put(`/teams/${teamId}/review`, reviewData)\n  },\n\n  // 批量审核\n  batchReview(reviewData) {\n    return request.post('/teams/batch-review', reviewData)\n  }\n}\n\n// 公告管理相关API（普通用户）\nexport const announcementAPI = {\n  // 获取公告列表（普通用户）\n  getAnnouncements(params = {}) {\n    return request.get('/users/announcements/dashboard', { params })\n  },\n\n  // 获取公告详情\n  getAnnouncement(id) {\n    return request.get(`/users/announcements/${id}`)\n  },\n\n  // 增加公告查看次数\n  incrementViewCount(id) {\n    return request.post(`/users/announcements/${id}/view`)\n  },\n\n  // 获取最新公告（仪表盘用）\n  getLatestAnnouncements(limit = 5) {\n    return request.get('/users/announcements/dashboard', { params: { size: limit } })\n  }\n}\n\n// 导入新的API模块\nexport { default as authApi } from './auth'\nexport { default as projectApi } from './project'\nexport { default as teamApi } from './team'\nexport { default as userApi } from './user'\nexport { default as evaluationApi } from './evaluation'\n\nexport { default as fileApi } from './file'\nexport { default as recordApi } from './record'\nexport { default as notificationApi } from './notification'\nexport { default as adminApi } from './admin'\n\nexport default {\n  authAPI,\n  projectAPI,\n  teamAPI,\n  recordAPI,\n  reviewAPI,\n  userAPI,\n  fileAPI,\n  dashboardAPI,\n  announcementAPI\n}\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,iBAAiB;;AAErC;AACA,OAAO,MAAMC,OAAO,GAAG;EACrB;EACAC,UAAUA,CAACC,IAAI,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IAC7B,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;IAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEJ,IAAI,CAAC;;IAE7B;IACA,IAAIC,OAAO,CAACI,SAAS,EAAEH,QAAQ,CAACE,MAAM,CAAC,WAAW,EAAEH,OAAO,CAACI,SAAS,CAAC;IACtE,IAAIJ,OAAO,CAACK,MAAM,EAAEJ,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAEH,OAAO,CAACK,MAAM,CAAC;IAC7D,IAAIL,OAAO,CAACM,QAAQ,EAAEL,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAEH,OAAO,CAACM,QAAQ,CAAC;IACnE,IAAIN,OAAO,CAACO,QAAQ,EAAEN,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAEH,OAAO,CAACO,QAAQ,CAAC;IACnE,IAAIP,OAAO,CAACQ,WAAW,EAAEP,QAAQ,CAACE,MAAM,CAAC,aAAa,EAAEH,OAAO,CAACQ,WAAW,CAAC;IAE5E,OAAOZ,OAAO,CAACa,IAAI,CAAC,eAAe,EAAER,QAAQ,EAAE;MAC7CS,OAAO,EAAE;QACP,cAAc,EAAE;MAClB,CAAC;MACDC,gBAAgB,EAAEX,OAAO,CAACY;IAC5B,CAAC,CAAC;EACJ,CAAC;EAED;EACAC,YAAYA,CAACC,MAAM,EAAE;IACnB,OAAOlB,OAAO,CAACmB,GAAG,CAAC,UAAUD,MAAM,WAAW,EAAE;MAC9CE,YAAY,EAAE;IAChB,CAAC,CAAC;EACJ,CAAC;EAED;EACAC,WAAWA,CAACH,MAAM,EAAE;IAClB,OAAOlB,OAAO,CAACmB,GAAG,CAAC,UAAUD,MAAM,UAAU,EAAE;MAC7CE,YAAY,EAAE;IAChB,CAAC,CAAC;EACJ,CAAC;EAED;EACAE,WAAWA,CAACJ,MAAM,EAAE;IAClB,OAAOlB,OAAO,CAACmB,GAAG,CAAC,UAAUD,MAAM,EAAE,CAAC;EACxC,CAAC;EAED;EACAK,gBAAgBA,CAACb,QAAQ,EAAE;IACzB,OAAOV,OAAO,CAACmB,GAAG,CAAC,iBAAiBT,QAAQ,EAAE,CAAC;EACjD,CAAC;EAED;EACAc,cAAcA,CAACN,MAAM,EAAEO,IAAI,EAAE;IAC3B,MAAMpB,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;IAC/B,IAAImB,IAAI,CAACb,WAAW,EAAEP,QAAQ,CAACE,MAAM,CAAC,aAAa,EAAEkB,IAAI,CAACb,WAAW,CAAC;IAEtE,OAAOZ,OAAO,CAAC0B,GAAG,CAAC,UAAUR,MAAM,EAAE,EAAEb,QAAQ,CAAC;EAClD,CAAC;EAED;EACAsB,UAAUA,CAACT,MAAM,EAAE;IACjB,OAAOlB,OAAO,CAAC4B,MAAM,CAAC,UAAUV,MAAM,EAAE,CAAC;EAC3C,CAAC;EAED;EACAW,WAAWA,CAACC,MAAM,GAAG,CAAC,CAAC,EAAE;IACvB,OAAO9B,OAAO,CAACmB,GAAG,CAAC,QAAQ,EAAE;MAAEW;IAAO,CAAC,CAAC;EAC1C,CAAC;EAED;EACAC,UAAUA,CAACD,MAAM,GAAG,CAAC,CAAC,EAAE;IACtB,OAAO9B,OAAO,CAACmB,GAAG,CAAC,WAAW,EAAE;MAAEW;IAAO,CAAC,CAAC;EAC7C,CAAC;EAED;EACAP,gBAAgBA,CAACb,QAAQ,EAAE;IACzB,OAAOV,OAAO,CAACmB,GAAG,CAAC,iBAAiBT,QAAQ,EAAE,CAAC;EACjD,CAAC;EAED;EACAsB,eAAeA,CAACxB,SAAS,EAAEsB,MAAM,GAAG,CAAC,CAAC,EAAE;IACtC,OAAO9B,OAAO,CAACmB,GAAG,CAAC,kBAAkBX,SAAS,EAAE,EAAE;MAAEsB;IAAO,CAAC,CAAC;EAC/D,CAAC;EAED;EACAG,YAAYA,CAACxB,MAAM,EAAEqB,MAAM,GAAG,CAAC,CAAC,EAAE;IAChC,OAAO9B,OAAO,CAACmB,GAAG,CAAC,eAAeV,MAAM,EAAE,EAAE;MAAEqB;IAAO,CAAC,CAAC;EACzD,CAAC;EAED;EACAI,cAAcA,CAACxB,QAAQ,EAAE;IACvB,OAAOV,OAAO,CAACmB,GAAG,CAAC,iBAAiBT,QAAQ,EAAE,CAAC;EACjD,CAAC;EAED;EACAyB,cAAcA,CAACxB,QAAQ,EAAEmB,MAAM,GAAG,CAAC,CAAC,EAAE;IACpC,OAAO9B,OAAO,CAACmB,GAAG,CAAC,eAAeR,QAAQ,EAAE,EAAE;MAAEmB;IAAO,CAAC,CAAC;EAC3D,CAAC;EAED;EACAM,WAAWA,CAACC,OAAO,EAAEP,MAAM,GAAG,CAAC,CAAC,EAAE;IAChC,OAAO9B,OAAO,CAACmB,GAAG,CAAC,eAAe,EAAE;MAClCW,MAAM,EAAE;QAAEO,OAAO;QAAE,GAAGP;MAAO;IAC/B,CAAC,CAAC;EACJ,CAAC;EAED;EACAQ,mBAAmBA,CAACC,YAAY,GAAG,CAAC,CAAC,EAAE;IACrC,OAAOvC,OAAO,CAACmB,GAAG,CAAC,wBAAwB,EAAE;MAAEW,MAAM,EAAES;IAAa,CAAC,CAAC;EACxE,CAAC;EAED;EACA;EACAC,uBAAuBA,CAACrC,IAAI,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IAC1C,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;IAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEJ,IAAI,CAAC;IAC7BE,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAEH,OAAO,CAACK,MAAM,CAAC;IACzC,IAAIL,OAAO,CAACQ,WAAW,EAAEP,QAAQ,CAACE,MAAM,CAAC,aAAa,EAAEH,OAAO,CAACQ,WAAW,CAAC;IAE5E,OAAOZ,OAAO,CAACa,IAAI,CAAC,6BAA6B,EAAER,QAAQ,EAAE;MAC3DS,OAAO,EAAE;QACP,cAAc,EAAE;MAClB,CAAC;MACDC,gBAAgB,EAAEX,OAAO,CAACY;IAC5B,CAAC,CAAC;EACJ,CAAC;EAED;EACAyB,qBAAqBA,CAAChC,MAAM,EAAEqB,MAAM,GAAG,CAAC,CAAC,EAAE;IACzC,OAAO9B,OAAO,CAACmB,GAAG,CAAC,6BAA6BV,MAAM,EAAE,EAAE;MAAEqB;IAAO,CAAC,CAAC;EACvE,CAAC;EAED;EACAY,qBAAqBA,CAACjC,MAAM,EAAE;IAC5B,OAAOT,OAAO,CAACmB,GAAG,CAAC,6BAA6BV,MAAM,QAAQ,CAAC;EACjE,CAAC;EAED;EACAkC,mBAAmBA,CAAA,EAAG;IACpB,OAAO3C,OAAO,CAACmB,GAAG,CAAC,wBAAwB,CAAC;EAC9C,CAAC;EAED;EACAyB,cAAcA,CAACC,KAAK,GAAG,EAAE,EAAE;IACzB,OAAO7C,OAAO,CAACmB,GAAG,CAAC,eAAe,EAAE;MAAEW,MAAM,EAAE;QAAEe;MAAM;IAAE,CAAC,CAAC;EAC5D,CAAC;EAED;EACAC,eAAeA,CAACD,KAAK,GAAG,EAAE,EAAE;IAC1B,OAAO7C,OAAO,CAACmB,GAAG,CAAC,gBAAgB,EAAE;MAAEW,MAAM,EAAE;QAAEe;MAAM;IAAE,CAAC,CAAC;EAC7D,CAAC;EAED;EACAE,gBAAgBA,CAACC,OAAO,EAAE;IACxB,OAAOhD,OAAO,CAAC4B,MAAM,CAAC,cAAc,EAAE;MAAEH,IAAI,EAAEuB;IAAQ,CAAC,CAAC;EAC1D,CAAC;EAED;EACAC,iBAAiBA,CAAA,EAAG;IAClB,OAAOjD,OAAO,CAACmB,GAAG,CAAC,mBAAmB,CAAC;EACzC,CAAC;EAED;EACA+B,wBAAwBA,CAAC1C,SAAS,EAAE;IAClC,OAAOR,OAAO,CAACmB,GAAG,CAAC,6BAA6BX,SAAS,EAAE,CAAC;EAC9D,CAAC;EAED;EACA2C,qBAAqBA,CAAC1C,MAAM,EAAE;IAC5B,OAAOT,OAAO,CAACmB,GAAG,CAAC,0BAA0BV,MAAM,EAAE,CAAC;EACxD,CAAC;EAED;EACA2C,kBAAkBA,CAAA,EAAG;IACnB,OAAOpD,OAAO,CAACa,IAAI,CAAC,gBAAgB,CAAC;EACvC;AACF,CAAC;;AAED;AACA,OAAO,MAAMwC,YAAY,GAAG;EAC1B;EACAC,iBAAiBA,CAAA,EAAG;IAClB,OAAOtD,OAAO,CAACmB,GAAG,CAAC,sBAAsB,CAAC;EAC5C,CAAC;EAED;EACAoC,YAAYA,CAAA,EAAG;IACb,OAAOvD,OAAO,CAACmB,GAAG,CAAC,0BAA0B,CAAC;EAChD,CAAC;EAED;EACAqC,iBAAiBA,CAACX,KAAK,GAAG,CAAC,EAAE;IAC3B,OAAO7C,OAAO,CAACmB,GAAG,CAAC,kBAAkB,EAAE;MAAEW,MAAM,EAAE;QAAEe;MAAM;IAAE,CAAC,CAAC;EAC/D,CAAC;EAED;EACAY,mBAAmBA,CAACZ,KAAK,GAAG,EAAE,EAAE;IAC9B,OAAO7C,OAAO,CAACmB,GAAG,CAAC,iBAAiB,EAAE;MAAEW,MAAM,EAAE;QAAEe;MAAM;IAAE,CAAC,CAAC;EAC9D;AACF,CAAC;;AAED;AACA,OAAO,MAAMa,OAAO,GAAG;EACrB;EACAC,QAAQA,CAACC,QAAQ,EAAE;IACjB,OAAO5D,OAAO,CAACa,IAAI,CAAC,gBAAgB,EAAE+C,QAAQ,CAAC;EACjD,CAAC;EAED;EACAC,KAAKA,CAACC,WAAW,EAAE;IACjB,OAAO9D,OAAO,CAACa,IAAI,CAAC,aAAa,EAAEiD,WAAW,CAAC;EACjD,CAAC;EAED;EACAC,YAAYA,CAAA,EAAG;IACb,OAAO/D,OAAO,CAACa,IAAI,CAAC,eAAe,CAAC;EACtC,CAAC;EAED;EACAmD,cAAcA,CAAA,EAAG;IACf,OAAOhE,OAAO,CAACmB,GAAG,CAAC,UAAU,CAAC;EAChC,CAAC;EAED;EACA8C,MAAMA,CAAA,EAAG;IACP,OAAOjE,OAAO,CAACa,IAAI,CAAC,cAAc,CAAC;EACrC,CAAC;EAED;EACAqD,aAAaA,CAACC,WAAW,EAAE;IACzB,OAAOnE,OAAO,CAAC0B,GAAG,CAAC,WAAW,EAAEyC,WAAW,CAAC;EAC9C,CAAC;EAED;EACAC,cAAcA,CAACC,YAAY,EAAE;IAC3B,OAAOrE,OAAO,CAAC0B,GAAG,CAAC,oBAAoB,EAAE2C,YAAY,CAAC;EACxD;AACF,CAAC;;AAED;AACA,OAAO,MAAMC,UAAU,GAAG;EACxB;EACAC,WAAWA,CAACzC,MAAM,GAAG,CAAC,CAAC,EAAE;IACvB,OAAO9B,OAAO,CAACmB,GAAG,CAAC,WAAW,EAAE;MAAEW;IAAO,CAAC,CAAC;EAC7C,CAAC;EAED;EACA0C,aAAaA,CAAC1C,MAAM,GAAG,CAAC,CAAC,EAAE;IACzB,OAAO9B,OAAO,CAACmB,GAAG,CAAC,cAAc,EAAE;MAAEW;IAAO,CAAC,CAAC;EAChD,CAAC;EAED;EACA2C,UAAUA,CAACC,EAAE,EAAE;IACb,OAAO1E,OAAO,CAACmB,GAAG,CAAC,aAAauD,EAAE,EAAE,CAAC;EACvC,CAAC;EAED;EACAC,aAAaA,CAACC,WAAW,EAAE;IACzB,OAAO5E,OAAO,CAACa,IAAI,CAAC,WAAW,EAAE+D,WAAW,CAAC;EAC/C,CAAC;EAED;EACAC,aAAaA,CAACH,EAAE,EAAEE,WAAW,EAAE;IAC7B,OAAO5E,OAAO,CAAC0B,GAAG,CAAC,aAAagD,EAAE,EAAE,EAAEE,WAAW,CAAC;EACpD,CAAC;EAED;EACAE,aAAaA,CAACJ,EAAE,EAAE;IAChB,OAAO1E,OAAO,CAAC4B,MAAM,CAAC,aAAa8C,EAAE,EAAE,CAAC;EAC1C,CAAC;EAED;EACAK,YAAYA,CAACL,EAAE,EAAE;IACf,OAAO1E,OAAO,CAAC0B,GAAG,CAAC,aAAagD,EAAE,SAAS,EAAE;MAAEM,MAAM,EAAE;IAAc,CAAC,CAAC;EACzE,CAAC;EAED;EACAC,gBAAgBA,CAACP,EAAE,EAAE;IACnB,OAAO1E,OAAO,CAACa,IAAI,CAAC,aAAa6D,EAAE,YAAY,CAAC;EAClD,CAAC;EAED;EACAQ,cAAcA,CAAC7C,OAAO,EAAEP,MAAM,GAAG,CAAC,CAAC,EAAE;IACnC,OAAO9B,OAAO,CAACmB,GAAG,CAAC,kBAAkB,EAAE;MACrCW,MAAM,EAAE;QAAEO,OAAO;QAAE,GAAGP;MAAO;IAC/B,CAAC,CAAC;EACJ,CAAC;EAED;EACAqD,kBAAkBA,CAACrD,MAAM,GAAG,CAAC,CAAC,EAAE;IAC9B,OAAO9B,OAAO,CAACmB,GAAG,CAAC,mBAAmB,EAAE;MAAEW;IAAO,CAAC,CAAC;EACrD,CAAC;EAED;EACA0C,aAAaA,CAAC1C,MAAM,GAAG,CAAC,CAAC,EAAE;IACzB,OAAO9B,OAAO,CAACmB,GAAG,CAAC,cAAc,EAAE;MAAEW;IAAO,CAAC,CAAC;EAChD,CAAC;EAED;EACAsD,eAAeA,CAACV,EAAE,EAAE;IAClB,OAAO1E,OAAO,CAACmB,GAAG,CAAC,aAAauD,EAAE,QAAQ,CAAC;EAC7C,CAAC;EAED;EACAW,qBAAqBA,CAAA,EAAG;IACtB,OAAOrF,OAAO,CAACmB,GAAG,CAAC,iBAAiB,CAAC;EACvC,CAAC;EAED;EACAqC,iBAAiBA,CAACX,KAAK,GAAG,EAAE,EAAE;IAC5B,OAAO7C,OAAO,CAACmB,GAAG,CAAC,kBAAkB,EAAE;MAAEW,MAAM,EAAE;QAAEe;MAAM;IAAE,CAAC,CAAC;EAC/D;AACF,CAAC;;AAED;AACA,OAAO,MAAMyC,OAAO,GAAG;EACrB;EACAC,QAAQA,CAACzD,MAAM,GAAG,CAAC,CAAC,EAAE;IACpB,OAAO9B,OAAO,CAACmB,GAAG,CAAC,QAAQ,EAAE;MAAEW;IAAO,CAAC,CAAC;EAC1C,CAAC;EAED;EACA0D,OAAOA,CAACd,EAAE,EAAE;IACV,OAAO1E,OAAO,CAACmB,GAAG,CAAC,UAAUuD,EAAE,EAAE,CAAC;EACpC,CAAC;EAED;EACAe,UAAUA,CAACC,QAAQ,EAAE;IACnB,OAAO1F,OAAO,CAACa,IAAI,CAAC,QAAQ,EAAE6E,QAAQ,CAAC;EACzC,CAAC;EAED;EACAC,UAAUA,CAACjB,EAAE,EAAEgB,QAAQ,EAAE;IACvB,OAAO1F,OAAO,CAAC0B,GAAG,CAAC,UAAUgD,EAAE,EAAE,EAAEgB,QAAQ,CAAC;EAC9C,CAAC;EAED;EACAE,cAAcA,CAAClB,EAAE,EAAE;IACjB,OAAO1E,OAAO,CAAC0B,GAAG,CAAC,UAAUgD,EAAE,kBAAkB,CAAC;EACpD,CAAC;EAED;EACAmB,UAAUA,CAACnB,EAAE,EAAE;IACb,OAAO1E,OAAO,CAAC4B,MAAM,CAAC,UAAU8C,EAAE,EAAE,CAAC;EACvC,CAAC;EAED;EACAoB,WAAWA,CAACpB,EAAE,EAAE;IACd,OAAO1E,OAAO,CAAC4B,MAAM,CAAC,UAAU8C,EAAE,EAAE,CAAC;EACvC,CAAC;EAED;EACAqB,QAAQA,CAACtF,MAAM,EAAEuF,MAAM,GAAG,EAAE,EAAE;IAC5B,OAAOhG,OAAO,CAACa,IAAI,CAAC,UAAUJ,MAAM,OAAO,EAAE;MAAEuF;IAAO,CAAC,CAAC;EAC1D,CAAC;EAED;EACAC,SAASA,CAACxF,MAAM,EAAE;IAChB,OAAOT,OAAO,CAACa,IAAI,CAAC,UAAUJ,MAAM,QAAQ,CAAC;EAC/C,CAAC;EAED;EACAyF,SAASA,CAAA,EAAG;IACV,OAAOlG,OAAO,CAACmB,GAAG,CAAC,WAAW,CAAC;EACjC,CAAC;EAED;EACAgF,cAAcA,CAACrE,MAAM,GAAG,CAAC,CAAC,EAAE;IAC1B,OAAO9B,OAAO,CAACmB,GAAG,CAAC,eAAe,EAAE;MAAEW;IAAO,CAAC,CAAC;EACjD,CAAC;EAED;EACAsE,eAAeA,CAAC5F,SAAS,EAAEsB,MAAM,GAAG,CAAC,CAAC,EAAE;IACtC,OAAO9B,OAAO,CAACmB,GAAG,CAAC,kBAAkBX,SAAS,EAAE,EAAE;MAAEsB;IAAO,CAAC,CAAC;EAC/D,CAAC;EAED;EACAuE,YAAYA,CAAC5F,MAAM,EAAED,SAAS,EAAE8F,eAAe,EAAE;IAC/C,OAAOtG,OAAO,CAACa,IAAI,CAAC,UAAUJ,MAAM,UAAUD,SAAS,EAAE,EAAE8F,eAAe,CAAC;EAC7E,CAAC;EAED;EACAC,cAAcA,CAAC9F,MAAM,EAAE;IACrB,OAAOT,OAAO,CAACmB,GAAG,CAAC,UAAUV,MAAM,UAAU,CAAC;EAChD,CAAC;EAED;EACA+F,gBAAgBA,CAAC/F,MAAM,EAAEgG,MAAM,EAAE;IAC/B,OAAOzG,OAAO,CAAC4B,MAAM,CAAC,UAAUnB,MAAM,YAAYgG,MAAM,EAAE,CAAC;EAC7D,CAAC;EAED;EACAC,YAAYA,CAACjG,MAAM,EAAEgG,MAAM,EAAE;IAC3B,OAAO,IAAI,CAACD,gBAAgB,CAAC/F,MAAM,EAAEgG,MAAM,CAAC;EAC9C,CAAC;EAED;EACAE,YAAYA,CAAA,EAAG;IACb,OAAO3G,OAAO,CAACmB,GAAG,CAAC,cAAc,CAAC;EACpC,CAAC;EAED;EACAyF,mBAAmBA,CAACnG,MAAM,EAAEqB,MAAM,GAAG,CAAC,CAAC,EAAE;IACvC,OAAO9B,OAAO,CAACmB,GAAG,CAAC,UAAUV,MAAM,eAAe,EAAE;MAAEqB;IAAO,CAAC,CAAC;EACjE,CAAC;EAED;EACA+E,iBAAiBA,CAACC,aAAa,EAAErF,IAAI,EAAE;IACrC,OAAOzB,OAAO,CAAC0B,GAAG,CAAC,uBAAuBoF,aAAa,SAAS,EAAErF,IAAI,CAAC;EACzE,CAAC;EAED;EACAsF,sBAAsBA,CAACjF,MAAM,GAAG,CAAC,CAAC,EAAE;IAClC,OAAO9B,OAAO,CAACmB,GAAG,CAAC,6BAA6B,EAAE;MAAEW;IAAO,CAAC,CAAC;EAC/D,CAAC;EAED;EACAkF,iBAAiBA,CAACF,aAAa,EAAE;IAC/B,OAAO9G,OAAO,CAAC4B,MAAM,CAAC,uBAAuBkF,aAAa,EAAE,CAAC;EAC/D,CAAC;EAED;EACAG,gBAAgBA,CAAA,EAAG;IACjB,OAAOjH,OAAO,CAACmB,GAAG,CAAC,wBAAwB,CAAC;EAC9C,CAAC;EAED;EACA+F,mBAAmBA,CAACzG,MAAM,EAAE;IAC1B,OAAOT,OAAO,CAAC4B,MAAM,CAAC,UAAUnB,MAAM,SAAS,CAAC;EAClD;AACF,CAAC;;AAED;AACA,OAAO,MAAM0G,SAAS,GAAG;EACvB;EACAC,UAAUA,CAACtF,MAAM,GAAG,CAAC,CAAC,EAAE;IACtB,OAAO9B,OAAO,CAACmB,GAAG,CAAC,UAAU,EAAE;MAAEW;IAAO,CAAC,CAAC;EAC5C,CAAC;EAED;EACAuF,iBAAiBA,CAAC7G,SAAS,EAAEsB,MAAM,GAAG,CAAC,CAAC,EAAE;IACxC,OAAO9B,OAAO,CAACmB,GAAG,CAAC,qBAAqBX,SAAS,EAAE,EAAE;MAAEsB;IAAO,CAAC,CAAC;EAClE,CAAC;EAED;EACAwF,cAAcA,CAAC7G,MAAM,EAAEqB,MAAM,GAAG,CAAC,CAAC,EAAE;IAClC,OAAO9B,OAAO,CAACmB,GAAG,CAAC,kBAAkBV,MAAM,EAAE,EAAE;MAAEqB;IAAO,CAAC,CAAC;EAC5D,CAAC;EAED;EACAyF,kBAAkBA,CAAC9G,MAAM,EAAEqB,MAAM,GAAG,CAAC,CAAC,EAAE;IACtC,OAAO9B,OAAO,CAACmB,GAAG,CAAC,kBAAkBV,MAAM,cAAc,EAAE;MAAEqB;IAAO,CAAC,CAAC;EACxE,CAAC;EAED;EACA0F,YAAYA,CAACC,UAAU,EAAE;IACvB,OAAOzH,OAAO,CAACa,IAAI,CAAC,UAAU,EAAE4G,UAAU,CAAC;EAC7C,CAAC;EAED;EACAC,YAAYA,CAAChD,EAAE,EAAE+C,UAAU,EAAE;IAC3B,OAAOzH,OAAO,CAAC0B,GAAG,CAAC,YAAYgD,EAAE,EAAE,EAAE+C,UAAU,CAAC;EAClD,CAAC;EAED;EACAE,YAAYA,CAACjD,EAAE,EAAE;IACf,OAAO1E,OAAO,CAAC4B,MAAM,CAAC,YAAY8C,EAAE,EAAE,CAAC;EACzC,CAAC;EAED;EACAkD,UAAUA,CAACC,MAAM,EAAEpG,IAAI,EAAE;IACvB,OAAOzB,OAAO,CAACa,IAAI,CAAC,kBAAkBgH,MAAM,SAAS,EAAEpG,IAAI,CAAC;EAC9D,CAAC;EAED;EACAqG,mBAAmBA,CAACD,MAAM,EAAExH,QAAQ,EAAE;IACpC,OAAOL,OAAO,CAACa,IAAI,CAAC,kBAAkBgH,MAAM,SAAS,EAAExH,QAAQ,EAAE;MAC/DS,OAAO,EAAE;QACP,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;EACJ,CAAC;EAED;EACAiH,gBAAgBA,CAACF,MAAM,EAAE7C,MAAM,EAAE;IAC/B,OAAOhF,OAAO,CAAC0B,GAAG,CAAC,kBAAkBmG,MAAM,SAAS,EAAE;MAAE7C;IAAO,CAAC,CAAC;EACnE,CAAC;EAED;EACAgD,SAASA,CAACtH,QAAQ,EAAE;IAClB,OAAOV,OAAO,CAACmB,GAAG,CAAC,YAAYT,QAAQ,EAAE,CAAC;EAC5C,CAAC;EAED;EACAuH,UAAUA,CAACJ,MAAM,EAAEpG,IAAI,EAAE;IACvB,OAAOzB,OAAO,CAACa,IAAI,CAAC,kBAAkBgH,MAAM,SAAS,EAAEpG,IAAI,CAAC;EAC9D,CAAC;EAED;EACAyG,qBAAqBA,CAAC1H,SAAS,EAAEsB,MAAM,GAAG,CAAC,CAAC,EAAE;IAC5C,OAAO9B,OAAO,CAACmB,GAAG,CAAC,qBAAqBX,SAAS,cAAc,EAAE;MAAEsB;IAAO,CAAC,CAAC;EAC9E,CAAC;EAID;EACAqG,kBAAkBA,CAAC3H,SAAS,EAAEsB,MAAM,GAAG,CAAC,CAAC,EAAE;IACzC,OAAO9B,OAAO,CAACmB,GAAG,CAAC,qBAAqBX,SAAS,WAAW,EAAE;MAAEsB;IAAO,CAAC,CAAC;EAC3E,CAAC;EAED;EACAsG,cAAcA,CAAA,EAAG;IACf,OAAOpI,OAAO,CAACmB,GAAG,CAAC,gBAAgB,CAAC;EACtC,CAAC;EAED;EACAkH,gBAAgBA,CAACxF,KAAK,GAAG,EAAE,EAAE;IAC3B,OAAO7C,OAAO,CAACmB,GAAG,CAAC,iBAAiB,EAAE;MAAEW,MAAM,EAAE;QAAEe;MAAM;IAAE,CAAC,CAAC;EAC9D,CAAC;EAED;EACAyF,oBAAoBA,CAACC,YAAY,EAAE;IACjC,OAAOvI,OAAO,CAACmB,GAAG,CAAC,YAAYoH,YAAY,UAAU,CAAC;EACxD;AACF,CAAC;;AAED;AACA,OAAO,MAAMC,OAAO,GAAG;EACrB;EACAC,WAAWA,CAAC3G,MAAM,GAAG,CAAC,CAAC,EAAE;IACvB,OAAO9B,OAAO,CAACmB,GAAG,CAAC,QAAQ,EAAE;MAAEW;IAAO,CAAC,CAAC;EAC1C,CAAC;EAED;EACA4G,OAAOA,CAAChE,EAAE,EAAE;IACV,OAAO1E,OAAO,CAACmB,GAAG,CAAC,UAAUuD,EAAE,EAAE,CAAC;EACpC,CAAC;EAED;EACAiE,QAAQA,CAAC7G,MAAM,GAAG,CAAC,CAAC,EAAE;IACpB,OAAO9B,OAAO,CAACmB,GAAG,CAAC,QAAQ,EAAE;MAAEW;IAAO,CAAC,CAAC;EAC1C;AACF,CAAC;;AAED;AACA,OAAO,MAAM8G,SAAS,GAAG;EACvB;EACAC,eAAeA,CAAC/G,MAAM,GAAG,CAAC,CAAC,EAAE;IAC3B,OAAO9B,OAAO,CAACmB,GAAG,CAAC,eAAe,EAAE;MAAEW;IAAO,CAAC,CAAC;EACjD,CAAC;EAED;EACAiF,sBAAsBA,CAACjF,MAAM,GAAG,CAAC,CAAC,EAAE;IAClC,OAAO9B,OAAO,CAACmB,GAAG,CAAC,uBAAuB,EAAE;MAAEW;IAAO,CAAC,CAAC;EACzD,CAAC;EAED;EACAgH,kBAAkBA,CAACpE,EAAE,EAAE;IACrB,OAAO1E,OAAO,CAAC0B,GAAG,CAAC,iBAAiBgD,EAAE,UAAU,CAAC;EACnD,CAAC;EAED;EACAqE,iBAAiBA,CAACrE,EAAE,EAAE;IACpB,OAAO1E,OAAO,CAAC0B,GAAG,CAAC,iBAAiBgD,EAAE,SAAS,CAAC;EAClD,CAAC;EAED;EACAsE,cAAcA,CAACtE,EAAE,EAAE;IACjB,OAAO1E,OAAO,CAACmB,GAAG,CAAC,iBAAiBuD,EAAE,EAAE,CAAC;EAC3C,CAAC;EAED;EACAmC,iBAAiBA,CAACpG,MAAM,EAAEwI,UAAU,EAAE;IACpC,OAAOjJ,OAAO,CAAC0B,GAAG,CAAC,UAAUjB,MAAM,SAAS,EAAEwI,UAAU,CAAC;EAC3D,CAAC;EAED;EACAC,WAAWA,CAACD,UAAU,EAAE;IACtB,OAAOjJ,OAAO,CAACa,IAAI,CAAC,qBAAqB,EAAEoI,UAAU,CAAC;EACxD;AACF,CAAC;;AAED;AACA,OAAO,MAAME,eAAe,GAAG;EAC7B;EACAC,gBAAgBA,CAACtH,MAAM,GAAG,CAAC,CAAC,EAAE;IAC5B,OAAO9B,OAAO,CAACmB,GAAG,CAAC,gCAAgC,EAAE;MAAEW;IAAO,CAAC,CAAC;EAClE,CAAC;EAED;EACAuH,eAAeA,CAAC3E,EAAE,EAAE;IAClB,OAAO1E,OAAO,CAACmB,GAAG,CAAC,wBAAwBuD,EAAE,EAAE,CAAC;EAClD,CAAC;EAED;EACA4E,kBAAkBA,CAAC5E,EAAE,EAAE;IACrB,OAAO1E,OAAO,CAACa,IAAI,CAAC,wBAAwB6D,EAAE,OAAO,CAAC;EACxD,CAAC;EAED;EACA6E,sBAAsBA,CAAC1G,KAAK,GAAG,CAAC,EAAE;IAChC,OAAO7C,OAAO,CAACmB,GAAG,CAAC,gCAAgC,EAAE;MAAEW,MAAM,EAAE;QAAE0H,IAAI,EAAE3G;MAAM;IAAE,CAAC,CAAC;EACnF;AACF,CAAC;;AAED;AACA,SAAS4G,OAAO,IAAIC,OAAO,QAAQ,QAAQ;AAC3C,SAASD,OAAO,IAAIE,UAAU,QAAQ,WAAW;AACjD,SAASF,OAAO,IAAIG,OAAO,QAAQ,QAAQ;AAC3C,SAASH,OAAO,IAAII,OAAO,QAAQ,QAAQ;AAC3C,SAASJ,OAAO,IAAIK,aAAa,QAAQ,cAAc;AAEvD,SAASL,OAAO,IAAIM,OAAO,QAAQ,QAAQ;AAC3C,SAASN,OAAO,IAAIO,SAAS,QAAQ,UAAU;AAC/C,SAASP,OAAO,IAAIQ,eAAe,QAAQ,gBAAgB;AAC3D,SAASR,OAAO,IAAIS,QAAQ,QAAQ,SAAS;AAE7C,eAAe;EACbxG,OAAO;EACPY,UAAU;EACVgB,OAAO;EACP6B,SAAS;EACTyB,SAAS;EACTJ,OAAO;EACPvI,OAAO;EACPoD,YAAY;EACZ8F;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}