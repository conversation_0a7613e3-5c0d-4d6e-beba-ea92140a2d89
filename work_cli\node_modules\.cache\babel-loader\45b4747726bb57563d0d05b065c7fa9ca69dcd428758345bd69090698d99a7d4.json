{"ast": null, "code": "import { createElementVNode as _createElementVNode, createCommentVNode as _createCommentVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, createTextVNode as _createTextVNode, withKeys as _withKeys, openBlock as _openBlock, createElement<PERSON>lock as _createElementBlock, renderList as _renderList, Fragment as _Fragment, createBlock as _createBlock, toDisplayString as _toDisplayString, withModifiers as _withModifiers, resolveDirective as _resolveDirective, withDirectives as _withDirectives } from \"vue\";\nconst _hoisted_1 = {\n  class: \"file-management\"\n};\nconst _hoisted_2 = {\n  class: \"content-container\"\n};\nconst _hoisted_3 = {\n  class: \"action-bar\"\n};\nconst _hoisted_4 = {\n  class: \"upload-section\"\n};\nconst _hoisted_5 = {\n  class: \"filter-controls\"\n};\nconst _hoisted_6 = {\n  class: \"file-list\"\n};\nconst _hoisted_7 = {\n  key: 0,\n  class: \"empty-state\"\n};\nconst _hoisted_8 = {\n  key: 1,\n  class: \"file-grid\"\n};\nconst _hoisted_9 = [\"onClick\"];\nconst _hoisted_10 = {\n  class: \"file-icon\"\n};\nconst _hoisted_11 = {\n  class: \"file-info\"\n};\nconst _hoisted_12 = [\"title\"];\nconst _hoisted_13 = {\n  class: \"file-meta\"\n};\nconst _hoisted_14 = {\n  class: \"file-size\"\n};\nconst _hoisted_15 = {\n  class: \"file-time\"\n};\nconst _hoisted_16 = {\n  class: \"file-uploader\"\n};\nconst _hoisted_17 = {\n  class: \"pagination-container\"\n};\nconst _hoisted_18 = {\n  key: 0,\n  class: \"file-detail\"\n};\nconst _hoisted_19 = {\n  class: \"detail-header\"\n};\nconst _hoisted_20 = {\n  class: \"file-preview\"\n};\nconst _hoisted_21 = [\"src\", \"alt\"];\nconst _hoisted_22 = {\n  key: 1,\n  class: \"preview-placeholder\"\n};\nconst _hoisted_23 = {\n  class: \"file-details\"\n};\nconst _hoisted_24 = {\n  class: \"detail-item\"\n};\nconst _hoisted_25 = {\n  class: \"detail-item\"\n};\nconst _hoisted_26 = {\n  class: \"detail-item\"\n};\nconst _hoisted_27 = {\n  class: \"detail-item\"\n};\nconst _hoisted_28 = {\n  key: 0,\n  class: \"detail-item\"\n};\nconst _hoisted_29 = {\n  class: \"detail-actions\"\n};\nconst _hoisted_30 = {\n  class: \"upload-progress\"\n};\nconst _hoisted_31 = {\n  class: \"progress-info\"\n};\nconst _hoisted_32 = {\n  class: \"file-name\"\n};\nconst _hoisted_33 = {\n  class: \"progress-percent\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_Upload = _resolveComponent(\"Upload\");\n  const _component_el_icon = _resolveComponent(\"el-icon\");\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_el_upload = _resolveComponent(\"el-upload\");\n  const _component_el_option = _resolveComponent(\"el-option\");\n  const _component_el_select = _resolveComponent(\"el-select\");\n  const _component_Search = _resolveComponent(\"Search\");\n  const _component_el_input = _resolveComponent(\"el-input\");\n  const _component_Refresh = _resolveComponent(\"Refresh\");\n  const _component_el_empty = _resolveComponent(\"el-empty\");\n  const _component_Picture = _resolveComponent(\"Picture\");\n  const _component_VideoPlay = _resolveComponent(\"VideoPlay\");\n  const _component_Document = _resolveComponent(\"Document\");\n  const _component_Files = _resolveComponent(\"Files\");\n  const _component_Download = _resolveComponent(\"Download\");\n  const _component_Delete = _resolveComponent(\"Delete\");\n  const _component_el_pagination = _resolveComponent(\"el-pagination\");\n  const _component_el_dialog = _resolveComponent(\"el-dialog\");\n  const _component_el_progress = _resolveComponent(\"el-progress\");\n  const _directive_loading = _resolveDirective(\"loading\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_cache[21] || (_cache[21] = _createElementVNode(\"div\", {\n    class: \"page-header\"\n  }, [_createElementVNode(\"h1\", {\n    class: \"page-title\"\n  }, \"文件管理\"), _createElementVNode(\"p\", {\n    class: \"page-description\"\n  }, \"管理项目和团队相关文件\")], -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_2, [_createCommentVNode(\" 操作栏 \"), _createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"div\", _hoisted_4, [_createVNode(_component_el_upload, {\n    ref: \"uploadRef\",\n    action: $setup.uploadUrl,\n    headers: $setup.uploadHeaders,\n    data: $setup.uploadData,\n    \"on-success\": $setup.handleUploadSuccess,\n    \"on-error\": $setup.handleUploadError,\n    \"on-progress\": $setup.handleUploadProgress,\n    \"before-upload\": $setup.beforeUpload,\n    \"show-file-list\": false,\n    multiple: \"\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_button, {\n      type: \"primary\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [_createVNode(_component_Upload)]),\n        _: 1 /* STABLE */\n      }), _cache[10] || (_cache[10] = _createTextVNode(\" 上传文件 \"))]),\n      _: 1 /* STABLE */,\n      __: [10]\n    })]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"action\", \"headers\", \"data\", \"on-success\", \"on-error\", \"on-progress\", \"before-upload\"])]), _createElementVNode(\"div\", _hoisted_5, [_createVNode(_component_el_select, {\n    modelValue: $setup.filterType,\n    \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $setup.filterType = $event),\n    placeholder: \"文件类型\",\n    clearable: \"\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_option, {\n      label: \"全部类型\",\n      value: \"\"\n    }), _createVNode(_component_el_option, {\n      label: \"文档\",\n      value: \"DOCUMENT\"\n    }), _createVNode(_component_el_option, {\n      label: \"图片\",\n      value: \"IMAGE\"\n    }), _createVNode(_component_el_option, {\n      label: \"视频\",\n      value: \"VIDEO\"\n    }), _createVNode(_component_el_option, {\n      label: \"其他\",\n      value: \"OTHER\"\n    })]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_el_select, {\n    modelValue: $setup.filterScope,\n    \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $setup.filterScope = $event),\n    placeholder: \"文件范围\",\n    clearable: \"\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_option, {\n      label: \"全部文件\",\n      value: \"\"\n    }), _createVNode(_component_el_option, {\n      label: \"我的文件\",\n      value: \"MY\"\n    }), _createVNode(_component_el_option, {\n      label: \"项目文件\",\n      value: \"PROJECT\"\n    }), _createVNode(_component_el_option, {\n      label: \"团队文件\",\n      value: \"TEAM\"\n    })]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_el_input, {\n    modelValue: $setup.searchKeyword,\n    \"onUpdate:modelValue\": _cache[2] || (_cache[2] = $event => $setup.searchKeyword = $event),\n    placeholder: \"搜索文件名\",\n    clearable: \"\",\n    onKeyup: _withKeys($setup.loadFiles, [\"enter\"])\n  }, {\n    prefix: _withCtx(() => [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [_createVNode(_component_Search)]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onKeyup\"]), _createVNode(_component_el_button, {\n    onClick: $setup.loadFiles\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [_createVNode(_component_Refresh)]),\n      _: 1 /* STABLE */\n    }), _cache[11] || (_cache[11] = _createTextVNode(\" 刷新 \"))]),\n    _: 1 /* STABLE */,\n    __: [11]\n  }, 8 /* PROPS */, [\"onClick\"])])]), _createCommentVNode(\" 文件列表 \"), _withDirectives((_openBlock(), _createElementBlock(\"div\", _hoisted_6, [$setup.files.length === 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_7, [_createVNode(_component_el_empty, {\n    description: \"暂无文件\"\n  })])) : (_openBlock(), _createElementBlock(\"div\", _hoisted_8, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.files, file => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: file.id,\n      class: \"file-card\",\n      onClick: $event => $setup.viewFileDetail(file)\n    }, [_createElementVNode(\"div\", _hoisted_10, [$setup.isImageFile(file) ? (_openBlock(), _createBlock(_component_el_icon, {\n      key: 0,\n      class: \"image-icon\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_Picture)]),\n      _: 1 /* STABLE */\n    })) : $setup.isVideoFile(file) ? (_openBlock(), _createBlock(_component_el_icon, {\n      key: 1,\n      class: \"video-icon\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_VideoPlay)]),\n      _: 1 /* STABLE */\n    })) : $setup.isDocumentFile(file) ? (_openBlock(), _createBlock(_component_el_icon, {\n      key: 2,\n      class: \"document-icon\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_Document)]),\n      _: 1 /* STABLE */\n    })) : (_openBlock(), _createBlock(_component_el_icon, {\n      key: 3,\n      class: \"default-icon\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_Files)]),\n      _: 1 /* STABLE */\n    }))]), _createElementVNode(\"div\", _hoisted_11, [_createElementVNode(\"div\", {\n      class: \"file-name\",\n      title: file.fileName\n    }, _toDisplayString(file.fileName), 9 /* TEXT, PROPS */, _hoisted_12), _createElementVNode(\"div\", _hoisted_13, [_createElementVNode(\"span\", _hoisted_14, _toDisplayString($setup.formatFileSize(file.fileSize)), 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_15, _toDisplayString($setup.formatDate(file.createTime)), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_16, \"上传者：\" + _toDisplayString(file.uploaderName), 1 /* TEXT */)]), _createElementVNode(\"div\", {\n      class: \"file-actions\",\n      onClick: _cache[3] || (_cache[3] = _withModifiers(() => {}, [\"stop\"]))\n    }, [_createVNode(_component_el_button, {\n      type: \"primary\",\n      size: \"small\",\n      onClick: $event => $setup.downloadFile(file),\n      loading: $setup.downloadingFiles.includes(file.id)\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [_createVNode(_component_Download)]),\n        _: 1 /* STABLE */\n      }), _cache[12] || (_cache[12] = _createTextVNode(\" 下载 \"))]),\n      _: 2 /* DYNAMIC */,\n      __: [12]\n    }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\", \"loading\"]), $setup.canDeleteFile(file) ? (_openBlock(), _createBlock(_component_el_button, {\n      key: 0,\n      type: \"danger\",\n      size: \"small\",\n      onClick: $event => $setup.deleteFile(file)\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [_createVNode(_component_Delete)]),\n        _: 1 /* STABLE */\n      }), _cache[13] || (_cache[13] = _createTextVNode(\" 删除 \"))]),\n      _: 2 /* DYNAMIC */,\n      __: [13]\n    }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"])) : _createCommentVNode(\"v-if\", true)])], 8 /* PROPS */, _hoisted_9);\n  }), 128 /* KEYED_FRAGMENT */))]))])), [[_directive_loading, $setup.loading]]), _createCommentVNode(\" 分页 \"), _createElementVNode(\"div\", _hoisted_17, [_createVNode(_component_el_pagination, {\n    \"current-page\": $setup.currentPage,\n    \"onUpdate:currentPage\": _cache[4] || (_cache[4] = $event => $setup.currentPage = $event),\n    \"page-size\": $setup.pageSize,\n    \"onUpdate:pageSize\": _cache[5] || (_cache[5] = $event => $setup.pageSize = $event),\n    total: $setup.total,\n    \"page-sizes\": [12, 24, 48],\n    layout: \"total, sizes, prev, pager, next, jumper\",\n    onSizeChange: $setup.loadFiles,\n    onCurrentChange: $setup.loadFiles\n  }, null, 8 /* PROPS */, [\"current-page\", \"page-size\", \"total\", \"onSizeChange\", \"onCurrentChange\"])])]), _createCommentVNode(\" 文件详情对话框 \"), _createVNode(_component_el_dialog, {\n    modelValue: $setup.showDetailDialog,\n    \"onUpdate:modelValue\": _cache[8] || (_cache[8] = $event => $setup.showDetailDialog = $event),\n    title: \"文件详情\",\n    width: \"600px\"\n  }, {\n    default: _withCtx(() => [$setup.selectedFile ? (_openBlock(), _createElementBlock(\"div\", _hoisted_18, [_createElementVNode(\"div\", _hoisted_19, [_createElementVNode(\"div\", _hoisted_20, [$setup.isImageFile($setup.selectedFile) ? (_openBlock(), _createElementBlock(\"img\", {\n      key: 0,\n      src: $setup.getPreviewUrl($setup.selectedFile),\n      alt: $setup.selectedFile.fileName,\n      class: \"preview-image\"\n    }, null, 8 /* PROPS */, _hoisted_21)) : (_openBlock(), _createElementBlock(\"div\", _hoisted_22, [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [_createVNode(_component_Document)]),\n      _: 1 /* STABLE */\n    }), _createElementVNode(\"span\", null, _toDisplayString($setup.selectedFile.fileName), 1 /* TEXT */)]))]), _createElementVNode(\"div\", _hoisted_23, [_createElementVNode(\"h3\", null, _toDisplayString($setup.selectedFile.fileName), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_24, [_cache[14] || (_cache[14] = _createElementVNode(\"span\", {\n      class: \"label\"\n    }, \"文件大小：\", -1 /* CACHED */)), _createElementVNode(\"span\", null, _toDisplayString($setup.formatFileSize($setup.selectedFile.fileSize)), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_25, [_cache[15] || (_cache[15] = _createElementVNode(\"span\", {\n      class: \"label\"\n    }, \"文件类型：\", -1 /* CACHED */)), _createElementVNode(\"span\", null, _toDisplayString($setup.selectedFile.fileType), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_26, [_cache[16] || (_cache[16] = _createElementVNode(\"span\", {\n      class: \"label\"\n    }, \"上传时间：\", -1 /* CACHED */)), _createElementVNode(\"span\", null, _toDisplayString($setup.formatDate($setup.selectedFile.createTime)), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_27, [_cache[17] || (_cache[17] = _createElementVNode(\"span\", {\n      class: \"label\"\n    }, \"上传者：\", -1 /* CACHED */)), _createElementVNode(\"span\", null, _toDisplayString($setup.selectedFile.uploaderName), 1 /* TEXT */)]), $setup.selectedFile.description ? (_openBlock(), _createElementBlock(\"div\", _hoisted_28, [_cache[18] || (_cache[18] = _createElementVNode(\"span\", {\n      class: \"label\"\n    }, \"文件描述：\", -1 /* CACHED */)), _createElementVNode(\"span\", null, _toDisplayString($setup.selectedFile.description), 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true)])]), _createElementVNode(\"div\", _hoisted_29, [_createVNode(_component_el_button, {\n      type: \"primary\",\n      onClick: _cache[6] || (_cache[6] = $event => $setup.downloadFile($setup.selectedFile)),\n      loading: $setup.downloadingFiles.includes($setup.selectedFile.id)\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [_createVNode(_component_Download)]),\n        _: 1 /* STABLE */\n      }), _cache[19] || (_cache[19] = _createTextVNode(\" 下载文件 \"))]),\n      _: 1 /* STABLE */,\n      __: [19]\n    }, 8 /* PROPS */, [\"loading\"]), $setup.canDeleteFile($setup.selectedFile) ? (_openBlock(), _createBlock(_component_el_button, {\n      key: 0,\n      type: \"danger\",\n      onClick: _cache[7] || (_cache[7] = $event => $setup.deleteFile($setup.selectedFile))\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [_createVNode(_component_Delete)]),\n        _: 1 /* STABLE */\n      }), _cache[20] || (_cache[20] = _createTextVNode(\" 删除文件 \"))]),\n      _: 1 /* STABLE */,\n      __: [20]\n    })) : _createCommentVNode(\"v-if\", true)])])) : _createCommentVNode(\"v-if\", true)]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"]), _createCommentVNode(\" 上传进度对话框 \"), _createVNode(_component_el_dialog, {\n    modelValue: $setup.showUploadDialog,\n    \"onUpdate:modelValue\": _cache[9] || (_cache[9] = $event => $setup.showUploadDialog = $event),\n    title: \"文件上传\",\n    width: \"500px\",\n    \"close-on-click-modal\": false,\n    \"close-on-press-escape\": false\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_30, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.uploadProgresses, (progress, index) => {\n      return _openBlock(), _createElementBlock(\"div\", {\n        key: index,\n        class: \"progress-item\"\n      }, [_createElementVNode(\"div\", _hoisted_31, [_createElementVNode(\"span\", _hoisted_32, _toDisplayString(progress.fileName), 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_33, _toDisplayString(progress.percent) + \"%\", 1 /* TEXT */)]), _createVNode(_component_el_progress, {\n        percentage: progress.percent,\n        status: progress.status\n      }, null, 8 /* PROPS */, [\"percentage\", \"status\"])]);\n    }), 128 /* KEYED_FRAGMENT */))])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_createCommentVNode", "_hoisted_3", "_hoisted_4", "_createVNode", "_component_el_upload", "ref", "action", "$setup", "uploadUrl", "headers", "uploadHeaders", "data", "uploadData", "handleUploadSuccess", "handleUploadError", "handleUploadProgress", "beforeUpload", "multiple", "_component_el_button", "type", "_component_el_icon", "_component_Upload", "_hoisted_5", "_component_el_select", "filterType", "$event", "placeholder", "clearable", "_component_el_option", "label", "value", "filterScope", "_component_el_input", "searchKeyword", "onKeyup", "_with<PERSON><PERSON><PERSON>", "loadFiles", "prefix", "_withCtx", "_component_Search", "onClick", "_component_Refresh", "_hoisted_6", "files", "length", "_hoisted_7", "_component_el_empty", "description", "_hoisted_8", "_Fragment", "_renderList", "file", "key", "id", "viewFileDetail", "_hoisted_10", "isImageFile", "_createBlock", "_component_Picture", "isVideoFile", "_component_VideoPlay", "isDocumentFile", "_component_Document", "_component_Files", "_hoisted_11", "title", "fileName", "_hoisted_12", "_hoisted_13", "_hoisted_14", "_toDisplayString", "formatFileSize", "fileSize", "_hoisted_15", "formatDate", "createTime", "_hoisted_16", "uploaderName", "_cache", "_withModifiers", "size", "downloadFile", "loading", "downloadingFiles", "includes", "_component_Download", "canDeleteFile", "deleteFile", "_component_Delete", "_hoisted_17", "_component_el_pagination", "currentPage", "pageSize", "total", "layout", "onSizeChange", "onCurrentChange", "_component_el_dialog", "showDetailDialog", "width", "selectedFile", "_hoisted_18", "_hoisted_19", "_hoisted_20", "src", "getPreviewUrl", "alt", "_hoisted_22", "_hoisted_23", "_hoisted_24", "_hoisted_25", "fileType", "_hoisted_26", "_hoisted_27", "_hoisted_28", "_hoisted_29", "showUploadDialog", "_hoisted_30", "uploadProgresses", "progress", "index", "_hoisted_31", "_hoisted_32", "_hoisted_33", "percent", "_component_el_progress", "percentage", "status"], "sources": ["D:\\workspace\\idea\\worker\\work_cli\\src\\views\\file\\FileManagementView.vue"], "sourcesContent": ["<template>\n  <div class=\"file-management\">\n    <div class=\"page-header\">\n      <h1 class=\"page-title\">文件管理</h1>\n      <p class=\"page-description\">管理项目和团队相关文件</p>\n    </div>\n\n    <div class=\"content-container\">\n      <!-- 操作栏 -->\n      <div class=\"action-bar\">\n        <div class=\"upload-section\">\n          <el-upload\n            ref=\"uploadRef\"\n            :action=\"uploadUrl\"\n            :headers=\"uploadHeaders\"\n            :data=\"uploadData\"\n            :on-success=\"handleUploadSuccess\"\n            :on-error=\"handleUploadError\"\n            :on-progress=\"handleUploadProgress\"\n            :before-upload=\"beforeUpload\"\n            :show-file-list=\"false\"\n            multiple\n          >\n            <el-button type=\"primary\">\n              <el-icon><Upload /></el-icon>\n              上传文件\n            </el-button>\n          </el-upload>\n        </div>\n        \n        <div class=\"filter-controls\">\n          <el-select v-model=\"filterType\" placeholder=\"文件类型\" clearable>\n            <el-option label=\"全部类型\" value=\"\" />\n            <el-option label=\"文档\" value=\"DOCUMENT\" />\n            <el-option label=\"图片\" value=\"IMAGE\" />\n            <el-option label=\"视频\" value=\"VIDEO\" />\n            <el-option label=\"其他\" value=\"OTHER\" />\n          </el-select>\n          <el-select v-model=\"filterScope\" placeholder=\"文件范围\" clearable>\n            <el-option label=\"全部文件\" value=\"\" />\n            <el-option label=\"我的文件\" value=\"MY\" />\n            <el-option label=\"项目文件\" value=\"PROJECT\" />\n            <el-option label=\"团队文件\" value=\"TEAM\" />\n          </el-select>\n          <el-input\n            v-model=\"searchKeyword\"\n            placeholder=\"搜索文件名\"\n            clearable\n            @keyup.enter=\"loadFiles\"\n          >\n            <template #prefix>\n              <el-icon><Search /></el-icon>\n            </template>\n          </el-input>\n          <el-button @click=\"loadFiles\">\n            <el-icon><Refresh /></el-icon>\n            刷新\n          </el-button>\n        </div>\n      </div>\n\n      <!-- 文件列表 -->\n      <div class=\"file-list\" v-loading=\"loading\">\n        <div v-if=\"files.length === 0\" class=\"empty-state\">\n          <el-empty description=\"暂无文件\" />\n        </div>\n        <div v-else class=\"file-grid\">\n          <div \n            v-for=\"file in files\" \n            :key=\"file.id\" \n            class=\"file-card\"\n            @click=\"viewFileDetail(file)\"\n          >\n            <div class=\"file-icon\">\n              <el-icon v-if=\"isImageFile(file)\" class=\"image-icon\"><Picture /></el-icon>\n              <el-icon v-else-if=\"isVideoFile(file)\" class=\"video-icon\"><VideoPlay /></el-icon>\n              <el-icon v-else-if=\"isDocumentFile(file)\" class=\"document-icon\"><Document /></el-icon>\n              <el-icon v-else class=\"default-icon\"><Files /></el-icon>\n            </div>\n            <div class=\"file-info\">\n              <div class=\"file-name\" :title=\"file.fileName\">{{ file.fileName }}</div>\n              <div class=\"file-meta\">\n                <span class=\"file-size\">{{ formatFileSize(file.fileSize) }}</span>\n                <span class=\"file-time\">{{ formatDate(file.createTime) }}</span>\n              </div>\n              <div class=\"file-uploader\">上传者：{{ file.uploaderName }}</div>\n            </div>\n            <div class=\"file-actions\" @click.stop>\n              <el-button \n                type=\"primary\" \n                size=\"small\" \n                @click=\"downloadFile(file)\"\n                :loading=\"downloadingFiles.includes(file.id)\"\n              >\n                <el-icon><Download /></el-icon>\n                下载\n              </el-button>\n              <el-button \n                v-if=\"canDeleteFile(file)\"\n                type=\"danger\" \n                size=\"small\" \n                @click=\"deleteFile(file)\"\n              >\n                <el-icon><Delete /></el-icon>\n                删除\n              </el-button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 分页 -->\n      <div class=\"pagination-container\">\n        <el-pagination\n          v-model:current-page=\"currentPage\"\n          v-model:page-size=\"pageSize\"\n          :total=\"total\"\n          :page-sizes=\"[12, 24, 48]\"\n          layout=\"total, sizes, prev, pager, next, jumper\"\n          @size-change=\"loadFiles\"\n          @current-change=\"loadFiles\"\n        />\n      </div>\n    </div>\n\n    <!-- 文件详情对话框 -->\n    <el-dialog\n      v-model=\"showDetailDialog\"\n      title=\"文件详情\"\n      width=\"600px\"\n    >\n      <div v-if=\"selectedFile\" class=\"file-detail\">\n        <div class=\"detail-header\">\n          <div class=\"file-preview\">\n            <img \n              v-if=\"isImageFile(selectedFile)\" \n              :src=\"getPreviewUrl(selectedFile)\" \n              :alt=\"selectedFile.fileName\"\n              class=\"preview-image\"\n            />\n            <div v-else class=\"preview-placeholder\">\n              <el-icon><Document /></el-icon>\n              <span>{{ selectedFile.fileName }}</span>\n            </div>\n          </div>\n          <div class=\"file-details\">\n            <h3>{{ selectedFile.fileName }}</h3>\n            <div class=\"detail-item\">\n              <span class=\"label\">文件大小：</span>\n              <span>{{ formatFileSize(selectedFile.fileSize) }}</span>\n            </div>\n            <div class=\"detail-item\">\n              <span class=\"label\">文件类型：</span>\n              <span>{{ selectedFile.fileType }}</span>\n            </div>\n            <div class=\"detail-item\">\n              <span class=\"label\">上传时间：</span>\n              <span>{{ formatDate(selectedFile.createTime) }}</span>\n            </div>\n            <div class=\"detail-item\">\n              <span class=\"label\">上传者：</span>\n              <span>{{ selectedFile.uploaderName }}</span>\n            </div>\n            <div v-if=\"selectedFile.description\" class=\"detail-item\">\n              <span class=\"label\">文件描述：</span>\n              <span>{{ selectedFile.description }}</span>\n            </div>\n          </div>\n        </div>\n        <div class=\"detail-actions\">\n          <el-button \n            type=\"primary\" \n            @click=\"downloadFile(selectedFile)\"\n            :loading=\"downloadingFiles.includes(selectedFile.id)\"\n          >\n            <el-icon><Download /></el-icon>\n            下载文件\n          </el-button>\n          <el-button \n            v-if=\"canDeleteFile(selectedFile)\"\n            type=\"danger\" \n            @click=\"deleteFile(selectedFile)\"\n          >\n            <el-icon><Delete /></el-icon>\n            删除文件\n          </el-button>\n        </div>\n      </div>\n    </el-dialog>\n\n    <!-- 上传进度对话框 -->\n    <el-dialog\n      v-model=\"showUploadDialog\"\n      title=\"文件上传\"\n      width=\"500px\"\n      :close-on-click-modal=\"false\"\n      :close-on-press-escape=\"false\"\n    >\n      <div class=\"upload-progress\">\n        <div v-for=\"(progress, index) in uploadProgresses\" :key=\"index\" class=\"progress-item\">\n          <div class=\"progress-info\">\n            <span class=\"file-name\">{{ progress.fileName }}</span>\n            <span class=\"progress-percent\">{{ progress.percent }}%</span>\n          </div>\n          <el-progress :percentage=\"progress.percent\" :status=\"progress.status\" />\n        </div>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { ref, reactive, onMounted, computed } from 'vue'\nimport { useStore } from 'vuex'\nimport { ElMessage, ElMessageBox } from 'element-plus'\nimport {\n  Upload, Search, Refresh, Picture, VideoPlay, Document, Files,\n  Download, Delete\n} from '@element-plus/icons-vue'\nimport { fileApi } from '@/api/file'\n\nexport default {\n  name: 'FileManagementView',\n  components: {\n    Upload,\n    Search,\n    Refresh,\n    Picture,\n    VideoPlay,\n    Document,\n    Files,\n    Download,\n    Delete\n  },\n  setup() {\n    const store = useStore()\n    const loading = ref(false)\n    const files = ref([])\n    const downloadingFiles = ref([])\n    \n    // 分页\n    const currentPage = ref(1)\n    const pageSize = ref(12)\n    const total = ref(0)\n    \n    // 筛选和搜索\n    const filterType = ref('')\n    const filterScope = ref('')\n    const searchKeyword = ref('')\n    \n    // 对话框\n    const showDetailDialog = ref(false)\n    const showUploadDialog = ref(false)\n    const selectedFile = ref(null)\n    \n    // 上传相关\n    const uploadRef = ref()\n    const uploadProgresses = ref([])\n    \n    const currentUser = computed(() => store.getters.currentUser)\n    \n    // 上传配置\n    const uploadUrl = computed(() => '/api/files/upload')\n    const uploadHeaders = computed(() => ({\n      'Authorization': `Bearer ${store.getters.token}`\n    }))\n    const uploadData = computed(() => ({\n      projectId: null,\n      teamId: null,\n      fileType: 'OTHER'\n    }))\n\n    // 加载文件列表\n    const loadFiles = async () => {\n      try {\n        loading.value = true\n        const params = {\n          page: currentPage.value,\n          size: pageSize.value,\n          fileType: filterType.value || undefined,\n          keyword: searchKeyword.value || undefined\n        }\n        \n        let response\n        if (filterScope.value === 'MY') {\n          response = await fileApi.getMyFiles(params)\n        } else if (filterScope.value === 'PROJECT') {\n          // 这里需要根据当前项目ID获取\n          response = await fileApi.getFiles(params)\n        } else if (filterScope.value === 'TEAM') {\n          // 这里需要根据当前团队ID获取\n          response = await fileApi.getFiles(params)\n        } else {\n          response = await fileApi.getFiles(params)\n        }\n        \n        if (response.code === 200) {\n          files.value = response.data?.records || []\n          total.value = response.data?.total || 0\n        } else {\n          ElMessage.error(response.message || '获取文件列表失败')\n        }\n      } catch (error) {\n        console.error('Failed to load files:', error)\n        ElMessage.error('加载文件列表失败')\n      } finally {\n        loading.value = false\n      }\n    }\n\n    // 文件上传前检查\n    const beforeUpload = (file) => {\n      const isLt50M = file.size / 1024 / 1024 < 50\n      if (!isLt50M) {\n        ElMessage.error('文件大小不能超过 50MB!')\n        return false\n      }\n      \n      // 显示上传进度对话框\n      showUploadDialog.value = true\n      uploadProgresses.value.push({\n        fileName: file.name,\n        percent: 0,\n        status: 'active'\n      })\n      \n      return true\n    }\n\n    // 上传进度\n    const handleUploadProgress = (event, file) => {\n      const progress = uploadProgresses.value.find(p => p.fileName === file.name)\n      if (progress) {\n        progress.percent = Math.round(event.percent)\n      }\n    }\n\n    // 上传成功\n    const handleUploadSuccess = (response, file) => {\n      const progress = uploadProgresses.value.find(p => p.fileName === file.name)\n      if (progress) {\n        progress.percent = 100\n        progress.status = 'success'\n      }\n      \n      ElMessage.success(`${file.name} 上传成功`)\n      \n      // 延迟关闭对话框并刷新列表\n      setTimeout(() => {\n        showUploadDialog.value = false\n        uploadProgresses.value = []\n        loadFiles()\n      }, 1000)\n    }\n\n    // 上传失败\n    const handleUploadError = (error, file) => {\n      const progress = uploadProgresses.value.find(p => p.fileName === file.name)\n      if (progress) {\n        progress.status = 'exception'\n      }\n      \n      ElMessage.error(`${file.name} 上传失败`)\n    }\n\n    // 下载文件\n    const downloadFile = async (file) => {\n      try {\n        downloadingFiles.value.push(file.id)\n        const response = await fileApi.downloadFile(file.id)\n\n        // 创建下载链接\n        const blob = new Blob([response])\n        const url = window.URL.createObjectURL(blob)\n        const link = document.createElement('a')\n        link.href = url\n        link.download = file.fileName\n        document.body.appendChild(link)\n        link.click()\n        document.body.removeChild(link)\n        window.URL.revokeObjectURL(url)\n\n        ElMessage.success('文件下载成功')\n      } catch (error) {\n        console.error('Failed to download file:', error)\n        ElMessage.error('文件下载失败')\n      } finally {\n        downloadingFiles.value = downloadingFiles.value.filter(id => id !== file.id)\n      }\n    }\n\n    // 删除文件\n    const deleteFile = async (file) => {\n      try {\n        await ElMessageBox.confirm(\n          `确定要删除文件 \"${file.fileName}\" 吗？`,\n          '确认删除',\n          {\n            confirmButtonText: '确定',\n            cancelButtonText: '取消',\n            type: 'warning'\n          }\n        )\n\n        const response = await fileApi.deleteFile(file.id)\n        if (response.code === 200) {\n          ElMessage.success('文件删除成功')\n          loadFiles()\n          if (showDetailDialog.value && selectedFile.value?.id === file.id) {\n            showDetailDialog.value = false\n          }\n        } else {\n          ElMessage.error(response.message || '删除文件失败')\n        }\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('Failed to delete file:', error)\n          ElMessage.error('删除文件失败')\n        }\n      }\n    }\n\n    // 查看文件详情\n    const viewFileDetail = (file) => {\n      selectedFile.value = file\n      showDetailDialog.value = true\n    }\n\n    // 检查是否可以删除文件\n    const canDeleteFile = (file) => {\n      return file.uploaderId === currentUser.value?.id || currentUser.value?.role === 'TEACHER'\n    }\n\n    // 检查是否为图片文件\n    const isImageFile = (file) => {\n      const imageTypes = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp']\n      const extension = file.fileName.split('.').pop()?.toLowerCase()\n      return imageTypes.includes(extension)\n    }\n\n    // 检查是否为视频文件\n    const isVideoFile = (file) => {\n      const videoTypes = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm']\n      const extension = file.fileName.split('.').pop()?.toLowerCase()\n      return videoTypes.includes(extension)\n    }\n\n    // 检查是否为文档文件\n    const isDocumentFile = (file) => {\n      const docTypes = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt']\n      const extension = file.fileName.split('.').pop()?.toLowerCase()\n      return docTypes.includes(extension)\n    }\n\n    // 获取预览URL\n    const getPreviewUrl = (file) => {\n      return `/api/files/${file.id}/preview`\n    }\n\n    // 格式化文件大小\n    const formatFileSize = (bytes) => {\n      if (bytes === 0) return '0 B'\n      const k = 1024\n      const sizes = ['B', 'KB', 'MB', 'GB']\n      const i = Math.floor(Math.log(bytes) / Math.log(k))\n      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]\n    }\n\n    // 格式化日期\n    const formatDate = (dateString) => {\n      if (!dateString) return ''\n      const date = new Date(dateString)\n      return date.toLocaleString('zh-CN')\n    }\n\n    onMounted(() => {\n      loadFiles()\n    })\n\n    return {\n      loading,\n      files,\n      downloadingFiles,\n      currentPage,\n      pageSize,\n      total,\n      filterType,\n      filterScope,\n      searchKeyword,\n      showDetailDialog,\n      showUploadDialog,\n      selectedFile,\n      uploadRef,\n      uploadProgresses,\n      currentUser,\n      uploadUrl,\n      uploadHeaders,\n      uploadData,\n      loadFiles,\n      beforeUpload,\n      handleUploadProgress,\n      handleUploadSuccess,\n      handleUploadError,\n      downloadFile,\n      deleteFile,\n      viewFileDetail,\n      canDeleteFile,\n      isImageFile,\n      isVideoFile,\n      isDocumentFile,\n      getPreviewUrl,\n      formatFileSize,\n      formatDate\n    }\n  }\n}\n</script>\n\n<style scoped>\n.file-management {\n  padding: 0;\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n.page-header {\n  margin-bottom: var(--space-6);\n}\n\n.page-title {\n  font-size: var(--font-size-2xl);\n  font-weight: var(--font-weight-bold);\n  color: var(--color-text-primary);\n  margin: 0 0 var(--space-2) 0;\n}\n\n.page-description {\n  color: var(--color-text-secondary);\n  margin: 0;\n}\n\n.content-container {\n  background: var(--color-bg-container);\n  border-radius: var(--border-radius-lg);\n  padding: var(--space-6);\n  box-shadow: var(--shadow-sm);\n}\n\n.action-bar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: var(--space-6);\n  flex-wrap: wrap;\n  gap: var(--space-4);\n}\n\n.filter-controls {\n  display: flex;\n  gap: var(--space-3);\n  align-items: center;\n  flex-wrap: wrap;\n}\n\n.file-list {\n  min-height: 400px;\n}\n\n.file-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));\n  gap: var(--space-4);\n}\n\n.file-card {\n  background: var(--color-bg-elevated);\n  border: 1px solid var(--color-border);\n  border-radius: var(--border-radius-md);\n  padding: var(--space-4);\n  cursor: pointer;\n  transition: all 0.2s ease;\n  display: flex;\n  flex-direction: column;\n  height: 200px;\n}\n\n.file-card:hover {\n  border-color: var(--color-primary);\n  box-shadow: var(--shadow-md);\n}\n\n.file-icon {\n  text-align: center;\n  margin-bottom: var(--space-3);\n  flex-shrink: 0;\n}\n\n.file-icon .el-icon {\n  font-size: 48px;\n}\n\n.image-icon {\n  color: var(--color-success);\n}\n\n.video-icon {\n  color: var(--color-warning);\n}\n\n.document-icon {\n  color: var(--color-primary);\n}\n\n.default-icon {\n  color: var(--color-text-tertiary);\n}\n\n.file-info {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  justify-content: space-between;\n}\n\n.file-name {\n  font-weight: var(--font-weight-semibold);\n  color: var(--color-text-primary);\n  margin-bottom: var(--space-2);\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.file-meta {\n  font-size: var(--font-size-sm);\n  color: var(--color-text-tertiary);\n  margin-bottom: var(--space-2);\n}\n\n.file-uploader {\n  font-size: var(--font-size-sm);\n  color: var(--color-text-secondary);\n  margin-bottom: var(--space-3);\n}\n\n.file-actions {\n  display: flex;\n  gap: var(--space-2);\n  justify-content: center;\n}\n\n.pagination-container {\n  display: flex;\n  justify-content: center;\n  margin-top: var(--space-6);\n}\n\n.empty-state {\n  text-align: center;\n  padding: var(--space-8) 0;\n}\n\n.file-detail {\n  max-height: 60vh;\n  overflow-y: auto;\n}\n\n.detail-header {\n  display: flex;\n  gap: var(--space-4);\n  margin-bottom: var(--space-4);\n}\n\n.file-preview {\n  flex-shrink: 0;\n  width: 200px;\n  height: 150px;\n  border: 1px solid var(--color-border);\n  border-radius: var(--border-radius-md);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  overflow: hidden;\n}\n\n.preview-image {\n  max-width: 100%;\n  max-height: 100%;\n  object-fit: cover;\n}\n\n.preview-placeholder {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: var(--space-2);\n  color: var(--color-text-tertiary);\n}\n\n.preview-placeholder .el-icon {\n  font-size: 48px;\n}\n\n.file-details {\n  flex: 1;\n}\n\n.file-details h3 {\n  margin: 0 0 var(--space-3) 0;\n  color: var(--color-text-primary);\n}\n\n.detail-item {\n  margin-bottom: var(--space-2);\n  display: flex;\n  align-items: center;\n}\n\n.detail-item .label {\n  font-weight: var(--font-weight-semibold);\n  color: var(--color-text-secondary);\n  min-width: 80px;\n}\n\n.detail-actions {\n  display: flex;\n  gap: var(--space-3);\n  justify-content: center;\n  padding-top: var(--space-4);\n  border-top: 1px solid var(--color-border);\n}\n\n.upload-progress {\n  max-height: 300px;\n  overflow-y: auto;\n}\n\n.progress-item {\n  margin-bottom: var(--space-4);\n}\n\n.progress-info {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: var(--space-2);\n}\n\n.progress-info .file-name {\n  font-weight: var(--font-weight-semibold);\n  color: var(--color-text-primary);\n}\n\n.progress-percent {\n  font-size: var(--font-size-sm);\n  color: var(--color-text-secondary);\n}\n\n@media (max-width: 768px) {\n  .file-management {\n    padding: 0;\n  }\n\n  .action-bar {\n    flex-direction: column;\n    align-items: stretch;\n  }\n\n  .filter-controls {\n    justify-content: center;\n  }\n\n  .file-grid {\n    grid-template-columns: 1fr;\n  }\n\n  .detail-header {\n    flex-direction: column;\n  }\n\n  .file-preview {\n    width: 100%;\n    height: 200px;\n  }\n\n  .detail-actions {\n    flex-direction: column;\n  }\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAiB;;EAMrBA,KAAK,EAAC;AAAmB;;EAEvBA,KAAK,EAAC;AAAY;;EAChBA,KAAK,EAAC;AAAgB;;EAoBtBA,KAAK,EAAC;AAAiB;;EAgCzBA,KAAK,EAAC;AAAW;;;EACWA,KAAK,EAAC;;;;EAGzBA,KAAK,EAAC;;;;EAOTA,KAAK,EAAC;AAAW;;EAMjBA,KAAK,EAAC;AAAW;;;EAEfA,KAAK,EAAC;AAAW;;EACdA,KAAK,EAAC;AAAW;;EACjBA,KAAK,EAAC;AAAW;;EAEpBA,KAAK,EAAC;AAAe;;EA2B7BA,KAAK,EAAC;AAAsB;;;EAmBRA,KAAK,EAAC;;;EACxBA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAAc;;;;EAOXA,KAAK,EAAC;;;EAKfA,KAAK,EAAC;AAAc;;EAElBA,KAAK,EAAC;AAAa;;EAInBA,KAAK,EAAC;AAAa;;EAInBA,KAAK,EAAC;AAAa;;EAInBA,KAAK,EAAC;AAAa;;;EAIaA,KAAK,EAAC;;;EAM1CA,KAAK,EAAC;AAAgB;;EA6BxBA,KAAK,EAAC;AAAiB;;EAEnBA,KAAK,EAAC;AAAe;;EAClBA,KAAK,EAAC;AAAW;;EACjBA,KAAK,EAAC;AAAkB;;;;;;;;;;;;;;;;;;;;;;uBAzMxCC,mBAAA,CA+MM,OA/MNC,UA+MM,G,4BA9MJC,mBAAA,CAGM;IAHDH,KAAK,EAAC;EAAa,IACtBG,mBAAA,CAAgC;IAA5BH,KAAK,EAAC;EAAY,GAAC,MAAI,GAC3BG,mBAAA,CAA2C;IAAxCH,KAAK,EAAC;EAAkB,GAAC,aAAW,E,qBAGzCG,mBAAA,CAoHM,OApHNC,UAoHM,GAnHJC,mBAAA,SAAY,EACZF,mBAAA,CAkDM,OAlDNG,UAkDM,GAjDJH,mBAAA,CAkBM,OAlBNI,UAkBM,GAjBJC,YAAA,CAgBYC,oBAAA;IAfVC,GAAG,EAAC,WAAW;IACdC,MAAM,EAAEC,MAAA,CAAAC,SAAS;IACjBC,OAAO,EAAEF,MAAA,CAAAG,aAAa;IACtBC,IAAI,EAAEJ,MAAA,CAAAK,UAAU;IAChB,YAAU,EAAEL,MAAA,CAAAM,mBAAmB;IAC/B,UAAQ,EAAEN,MAAA,CAAAO,iBAAiB;IAC3B,aAAW,EAAEP,MAAA,CAAAQ,oBAAoB;IACjC,eAAa,EAAER,MAAA,CAAAS,YAAY;IAC3B,gBAAc,EAAE,KAAK;IACtBC,QAAQ,EAAR;;sBAEA,MAGY,CAHZd,YAAA,CAGYe,oBAAA;MAHDC,IAAI,EAAC;IAAS;wBACvB,MAA6B,CAA7BhB,YAAA,CAA6BiB,kBAAA;0BAApB,MAAU,CAAVjB,YAAA,CAAUkB,iBAAA,E;;uDAAU,QAE/B,G;;;;;gHAIJvB,mBAAA,CA4BM,OA5BNwB,UA4BM,GA3BJnB,YAAA,CAMYoB,oBAAA;gBANQhB,MAAA,CAAAiB,UAAU;+DAAVjB,MAAA,CAAAiB,UAAU,GAAAC,MAAA;IAAEC,WAAW,EAAC,MAAM;IAACC,SAAS,EAAT;;sBACjD,MAAmC,CAAnCxB,YAAA,CAAmCyB,oBAAA;MAAxBC,KAAK,EAAC,MAAM;MAACC,KAAK,EAAC;QAC9B3B,YAAA,CAAyCyB,oBAAA;MAA9BC,KAAK,EAAC,IAAI;MAACC,KAAK,EAAC;QAC5B3B,YAAA,CAAsCyB,oBAAA;MAA3BC,KAAK,EAAC,IAAI;MAACC,KAAK,EAAC;QAC5B3B,YAAA,CAAsCyB,oBAAA;MAA3BC,KAAK,EAAC,IAAI;MAACC,KAAK,EAAC;QAC5B3B,YAAA,CAAsCyB,oBAAA;MAA3BC,KAAK,EAAC,IAAI;MAACC,KAAK,EAAC;;;qCAE9B3B,YAAA,CAKYoB,oBAAA;gBALQhB,MAAA,CAAAwB,WAAW;+DAAXxB,MAAA,CAAAwB,WAAW,GAAAN,MAAA;IAAEC,WAAW,EAAC,MAAM;IAACC,SAAS,EAAT;;sBAClD,MAAmC,CAAnCxB,YAAA,CAAmCyB,oBAAA;MAAxBC,KAAK,EAAC,MAAM;MAACC,KAAK,EAAC;QAC9B3B,YAAA,CAAqCyB,oBAAA;MAA1BC,KAAK,EAAC,MAAM;MAACC,KAAK,EAAC;QAC9B3B,YAAA,CAA0CyB,oBAAA;MAA/BC,KAAK,EAAC,MAAM;MAACC,KAAK,EAAC;QAC9B3B,YAAA,CAAuCyB,oBAAA;MAA5BC,KAAK,EAAC,MAAM;MAACC,KAAK,EAAC;;;qCAEhC3B,YAAA,CASW6B,mBAAA;gBARAzB,MAAA,CAAA0B,aAAa;+DAAb1B,MAAA,CAAA0B,aAAa,GAAAR,MAAA;IACtBC,WAAW,EAAC,OAAO;IACnBC,SAAS,EAAT,EAAS;IACRO,OAAK,EAAAC,SAAA,CAAQ5B,MAAA,CAAA6B,SAAS;;IAEZC,MAAM,EAAAC,QAAA,CACf,MAA6B,CAA7BnC,YAAA,CAA6BiB,kBAAA;wBAApB,MAAU,CAAVjB,YAAA,CAAUoC,iBAAA,E;;;;gDAGvBpC,YAAA,CAGYe,oBAAA;IAHAsB,OAAK,EAAEjC,MAAA,CAAA6B;EAAS;sBAC1B,MAA8B,CAA9BjC,YAAA,CAA8BiB,kBAAA;wBAArB,MAAW,CAAXjB,YAAA,CAAWsC,kBAAA,E;;qDAAU,MAEhC,G;;;sCAIJzC,mBAAA,UAAa,E,+BACbJ,mBAAA,CA+CM,OA/CN8C,UA+CM,GA9COnC,MAAA,CAAAoC,KAAK,CAACC,MAAM,U,cAAvBhD,mBAAA,CAEM,OAFNiD,UAEM,GADJ1C,YAAA,CAA+B2C,mBAAA;IAArBC,WAAW,EAAC;EAAM,G,oBAE9BnD,mBAAA,CA0CM,OA1CNoD,UA0CM,I,kBAzCJpD,mBAAA,CAwCMqD,SAAA,QAAAC,WAAA,CAvCW3C,MAAA,CAAAoC,KAAK,EAAbQ,IAAI;yBADbvD,mBAAA,CAwCM;MAtCHwD,GAAG,EAAED,IAAI,CAACE,EAAE;MACb1D,KAAK,EAAC,WAAW;MAChB6C,OAAK,EAAAf,MAAA,IAAElB,MAAA,CAAA+C,cAAc,CAACH,IAAI;QAE3BrD,mBAAA,CAKM,OALNyD,WAKM,GAJWhD,MAAA,CAAAiD,WAAW,CAACL,IAAI,K,cAA/BM,YAAA,CAA0ErC,kBAAA;;MAAxCzB,KAAK,EAAC;;wBAAa,MAAW,CAAXQ,YAAA,CAAWuD,kBAAA,E;;UAC5CnD,MAAA,CAAAoD,WAAW,CAACR,IAAI,K,cAApCM,YAAA,CAAiFrC,kBAAA;;MAA1CzB,KAAK,EAAC;;wBAAa,MAAa,CAAbQ,YAAA,CAAayD,oBAAA,E;;UACnDrD,MAAA,CAAAsD,cAAc,CAACV,IAAI,K,cAAvCM,YAAA,CAAsFrC,kBAAA;;MAA5CzB,KAAK,EAAC;;wBAAgB,MAAY,CAAZQ,YAAA,CAAY2D,mBAAA,E;;yBAC5EL,YAAA,CAAwDrC,kBAAA;;MAAxCzB,KAAK,EAAC;;wBAAe,MAAS,CAATQ,YAAA,CAAS4D,gBAAA,E;;WAEhDjE,mBAAA,CAOM,OAPNkE,WAOM,GANJlE,mBAAA,CAAuE;MAAlEH,KAAK,EAAC,WAAW;MAAEsE,KAAK,EAAEd,IAAI,CAACe;wBAAaf,IAAI,CAACe,QAAQ,wBAAAC,WAAA,GAC9DrE,mBAAA,CAGM,OAHNsE,WAGM,GAFJtE,mBAAA,CAAkE,QAAlEuE,WAAkE,EAAAC,gBAAA,CAAvC/D,MAAA,CAAAgE,cAAc,CAACpB,IAAI,CAACqB,QAAQ,mBACvD1E,mBAAA,CAAgE,QAAhE2E,WAAgE,EAAAH,gBAAA,CAArC/D,MAAA,CAAAmE,UAAU,CAACvB,IAAI,CAACwB,UAAU,kB,GAEvD7E,mBAAA,CAA4D,OAA5D8E,WAA4D,EAAjC,MAAI,GAAAN,gBAAA,CAAGnB,IAAI,CAAC0B,YAAY,iB,GAErD/E,mBAAA,CAmBM;MAnBDH,KAAK,EAAC,cAAc;MAAE6C,OAAK,EAAAsC,MAAA,QAAAA,MAAA,MAAAC,cAAA,CAAN,QAAW;QACnC5E,YAAA,CAQYe,oBAAA;MAPVC,IAAI,EAAC,SAAS;MACd6D,IAAI,EAAC,OAAO;MACXxC,OAAK,EAAAf,MAAA,IAAElB,MAAA,CAAA0E,YAAY,CAAC9B,IAAI;MACxB+B,OAAO,EAAE3E,MAAA,CAAA4E,gBAAgB,CAACC,QAAQ,CAACjC,IAAI,CAACE,EAAE;;wBAE3C,MAA+B,CAA/BlD,YAAA,CAA+BiB,kBAAA;0BAAtB,MAAY,CAAZjB,YAAA,CAAYkF,mBAAA,E;;uDAAU,MAEjC,G;;;iEAEQ9E,MAAA,CAAA+E,aAAa,CAACnC,IAAI,K,cAD1BM,YAAA,CAQYvC,oBAAA;;MANVC,IAAI,EAAC,QAAQ;MACb6D,IAAI,EAAC,OAAO;MACXxC,OAAK,EAAAf,MAAA,IAAElB,MAAA,CAAAgF,UAAU,CAACpC,IAAI;;wBAEvB,MAA6B,CAA7BhD,YAAA,CAA6BiB,kBAAA;0BAApB,MAAU,CAAVjB,YAAA,CAAUqF,iBAAA,E;;uDAAU,MAE/B,G;;;;8DA3C0BjF,MAAA,CAAA2E,OAAO,E,GAiDzClF,mBAAA,QAAW,EACXF,mBAAA,CAUM,OAVN2F,WAUM,GATJtF,YAAA,CAQEuF,wBAAA;IAPQ,cAAY,EAAEnF,MAAA,CAAAoF,WAAW;gEAAXpF,MAAA,CAAAoF,WAAW,GAAAlE,MAAA;IACzB,WAAS,EAAElB,MAAA,CAAAqF,QAAQ;6DAARrF,MAAA,CAAAqF,QAAQ,GAAAnE,MAAA;IAC1BoE,KAAK,EAAEtF,MAAA,CAAAsF,KAAK;IACZ,YAAU,EAAE,YAAY;IACzBC,MAAM,EAAC,yCAAyC;IAC/CC,YAAW,EAAExF,MAAA,CAAA6B,SAAS;IACtB4D,eAAc,EAAEzF,MAAA,CAAA6B;0GAKvBpC,mBAAA,aAAgB,EAChBG,YAAA,CA8DY8F,oBAAA;gBA7DD1F,MAAA,CAAA2F,gBAAgB;+DAAhB3F,MAAA,CAAA2F,gBAAgB,GAAAzE,MAAA;IACzBwC,KAAK,EAAC,MAAM;IACZkC,KAAK,EAAC;;sBAM4B,MAgEpC,CApEa5F,MAAA,CAAA6F,YAAY,I,cAAvBxG,mBAAA,CAwDM,OAxDNyG,WAwDM,GAvDJvG,mBAAA,CAoCM,OApCNwG,WAoCM,GAnCJxG,mBAAA,CAWM,OAXNyG,WAWM,GATIhG,MAAA,CAAAiD,WAAW,CAACjD,MAAA,CAAA6F,YAAY,K,cADhCxG,mBAAA,CAKE;;MAHC4G,GAAG,EAAEjG,MAAA,CAAAkG,aAAa,CAAClG,MAAA,CAAA6F,YAAY;MAC/BM,GAAG,EAAEnG,MAAA,CAAA6F,YAAY,CAAClC,QAAQ;MAC3BvE,KAAK,EAAC;2DAERC,mBAAA,CAGM,OAHN+G,WAGM,GAFJxG,YAAA,CAA+BiB,kBAAA;wBAAtB,MAAY,CAAZjB,YAAA,CAAY2D,mBAAA,E;;QACrBhE,mBAAA,CAAwC,cAAAwE,gBAAA,CAA/B/D,MAAA,CAAA6F,YAAY,CAAClC,QAAQ,iB,MAGlCpE,mBAAA,CAsBM,OAtBN8G,WAsBM,GArBJ9G,mBAAA,CAAoC,YAAAwE,gBAAA,CAA7B/D,MAAA,CAAA6F,YAAY,CAAClC,QAAQ,kBAC5BpE,mBAAA,CAGM,OAHN+G,WAGM,G,4BAFJ/G,mBAAA,CAAgC;MAA1BH,KAAK,EAAC;IAAO,GAAC,OAAK,qBACzBG,mBAAA,CAAwD,cAAAwE,gBAAA,CAA/C/D,MAAA,CAAAgE,cAAc,CAAChE,MAAA,CAAA6F,YAAY,CAAC5B,QAAQ,kB,GAE/C1E,mBAAA,CAGM,OAHNgH,WAGM,G,4BAFJhH,mBAAA,CAAgC;MAA1BH,KAAK,EAAC;IAAO,GAAC,OAAK,qBACzBG,mBAAA,CAAwC,cAAAwE,gBAAA,CAA/B/D,MAAA,CAAA6F,YAAY,CAACW,QAAQ,iB,GAEhCjH,mBAAA,CAGM,OAHNkH,WAGM,G,4BAFJlH,mBAAA,CAAgC;MAA1BH,KAAK,EAAC;IAAO,GAAC,OAAK,qBACzBG,mBAAA,CAAsD,cAAAwE,gBAAA,CAA7C/D,MAAA,CAAAmE,UAAU,CAACnE,MAAA,CAAA6F,YAAY,CAACzB,UAAU,kB,GAE7C7E,mBAAA,CAGM,OAHNmH,WAGM,G,4BAFJnH,mBAAA,CAA+B;MAAzBH,KAAK,EAAC;IAAO,GAAC,MAAI,qBACxBG,mBAAA,CAA4C,cAAAwE,gBAAA,CAAnC/D,MAAA,CAAA6F,YAAY,CAACvB,YAAY,iB,GAEzBtE,MAAA,CAAA6F,YAAY,CAACrD,WAAW,I,cAAnCnD,mBAAA,CAGM,OAHNsH,WAGM,G,4BAFJpH,mBAAA,CAAgC;MAA1BH,KAAK,EAAC;IAAO,GAAC,OAAK,qBACzBG,mBAAA,CAA2C,cAAAwE,gBAAA,CAAlC/D,MAAA,CAAA6F,YAAY,CAACrD,WAAW,iB,4CAIvCjD,mBAAA,CAiBM,OAjBNqH,WAiBM,GAhBJhH,YAAA,CAOYe,oBAAA;MANVC,IAAI,EAAC,SAAS;MACbqB,OAAK,EAAAsC,MAAA,QAAAA,MAAA,MAAArD,MAAA,IAAElB,MAAA,CAAA0E,YAAY,CAAC1E,MAAA,CAAA6F,YAAY;MAChClB,OAAO,EAAE3E,MAAA,CAAA4E,gBAAgB,CAACC,QAAQ,CAAC7E,MAAA,CAAA6F,YAAY,CAAC/C,EAAE;;wBAEnD,MAA+B,CAA/BlD,YAAA,CAA+BiB,kBAAA;0BAAtB,MAAY,CAAZjB,YAAA,CAAYkF,mBAAA,E;;uDAAU,QAEjC,G;;;oCAEQ9E,MAAA,CAAA+E,aAAa,CAAC/E,MAAA,CAAA6F,YAAY,K,cADlC3C,YAAA,CAOYvC,oBAAA;;MALVC,IAAI,EAAC,QAAQ;MACZqB,OAAK,EAAAsC,MAAA,QAAAA,MAAA,MAAArD,MAAA,IAAElB,MAAA,CAAAgF,UAAU,CAAChF,MAAA,CAAA6F,YAAY;;wBAE/B,MAA6B,CAA7BjG,YAAA,CAA6BiB,kBAAA;0BAApB,MAAU,CAAVjB,YAAA,CAAUqF,iBAAA,E;;uDAAU,QAE/B,G;;;;;qCAKNxF,mBAAA,aAAgB,EAChBG,YAAA,CAgBY8F,oBAAA;gBAfD1F,MAAA,CAAA6G,gBAAgB;+DAAhB7G,MAAA,CAAA6G,gBAAgB,GAAA3F,MAAA;IACzBwC,KAAK,EAAC,MAAM;IACZkC,KAAK,EAAC,OAAO;IACZ,sBAAoB,EAAE,KAAK;IAC3B,uBAAqB,EAAE;;sBAExB,MAQM,CARNrG,mBAAA,CAQM,OARNuH,WAQM,I,kBAPJzH,mBAAA,CAMMqD,SAAA,QAAAC,WAAA,CAN2B3C,MAAA,CAAA+G,gBAAgB,GAApCC,QAAQ,EAAEC,KAAK;2BAA5B5H,mBAAA,CAMM;QAN8CwD,GAAG,EAAEoE,KAAK;QAAE7H,KAAK,EAAC;UACpEG,mBAAA,CAGM,OAHN2H,WAGM,GAFJ3H,mBAAA,CAAsD,QAAtD4H,WAAsD,EAAApD,gBAAA,CAA3BiD,QAAQ,CAACrD,QAAQ,kBAC5CpE,mBAAA,CAA6D,QAA7D6H,WAA6D,EAAArD,gBAAA,CAA3BiD,QAAQ,CAACK,OAAO,IAAG,GAAC,gB,GAExDzH,YAAA,CAAwE0H,sBAAA;QAA1DC,UAAU,EAAEP,QAAQ,CAACK,OAAO;QAAGG,MAAM,EAAER,QAAQ,CAACQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}