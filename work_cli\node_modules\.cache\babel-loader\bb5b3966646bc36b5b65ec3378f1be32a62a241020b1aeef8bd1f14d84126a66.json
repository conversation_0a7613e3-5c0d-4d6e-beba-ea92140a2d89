{"ast": null, "code": "import { createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, createTextVNode as _createTextVNode, createCommentVNode as _createCommentVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString } from \"vue\";\nconst _hoisted_1 = {\n  class: \"team-applications\"\n};\nconst _hoisted_2 = {\n  class: \"card-header\"\n};\nconst _hoisted_3 = {\n  class: \"header-actions\"\n};\nconst _hoisted_4 = {\n  key: 0,\n  class: \"applications-grid\"\n};\nconst _hoisted_5 = {\n  class: \"applicant-header\"\n};\nconst _hoisted_6 = {\n  class: \"applicant-info\"\n};\nconst _hoisted_7 = {\n  class: \"applicant-details\"\n};\nconst _hoisted_8 = {\n  class: \"username\"\n};\nconst _hoisted_9 = {\n  class: \"application-content\"\n};\nconst _hoisted_10 = {\n  class: \"team-info\"\n};\nconst _hoisted_11 = {\n  class: \"application-reason\"\n};\nconst _hoisted_12 = {\n  class: \"reason-text\"\n};\nconst _hoisted_13 = {\n  class: \"application-meta\"\n};\nconst _hoisted_14 = {\n  class: \"meta-item\"\n};\nconst _hoisted_15 = {\n  key: 0,\n  class: \"meta-item\"\n};\nconst _hoisted_16 = {\n  key: 0,\n  class: \"review-reason\"\n};\nconst _hoisted_17 = {\n  class: \"application-footer\"\n};\nconst _hoisted_18 = {\n  key: 0,\n  class: \"action-buttons\"\n};\nconst _hoisted_19 = {\n  key: 1,\n  class: \"status-info\"\n};\nconst _hoisted_20 = {\n  class: \"status-text\"\n};\nconst _hoisted_21 = {\n  class: \"empty-state\"\n};\nconst _hoisted_22 = {\n  class: \"loading-state\"\n};\nconst _hoisted_23 = {\n  key: 3,\n  class: \"pagination\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_option = _resolveComponent(\"el-option\");\n  const _component_el_select = _resolveComponent(\"el-select\");\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_el_avatar = _resolveComponent(\"el-avatar\");\n  const _component_el_tag = _resolveComponent(\"el-tag\");\n  const _component_Calendar = _resolveComponent(\"Calendar\");\n  const _component_el_icon = _resolveComponent(\"el-icon\");\n  const _component_Check = _resolveComponent(\"Check\");\n  const _component_Close = _resolveComponent(\"Close\");\n  const _component_el_card = _resolveComponent(\"el-card\");\n  const _component_DocumentRemove = _resolveComponent(\"DocumentRemove\");\n  const _component_el_skeleton = _resolveComponent(\"el-skeleton\");\n  const _component_el_pagination = _resolveComponent(\"el-pagination\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_el_card, null, {\n    header: _withCtx(() => [_createElementVNode(\"div\", _hoisted_2, [_cache[4] || (_cache[4] = _createElementVNode(\"h3\", null, \"团队申请管理\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_3, [_createVNode(_component_el_select, {\n      modelValue: $setup.statusFilter,\n      \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $setup.statusFilter = $event),\n      placeholder: \"申请状态\",\n      clearable: \"\",\n      onChange: $setup.fetchApplications,\n      style: {\n        \"width\": \"120px\",\n        \"margin-right\": \"12px\"\n      }\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_option, {\n        label: \"全部状态\",\n        value: \"\"\n      }), _createVNode(_component_el_option, {\n        label: \"待审核\",\n        value: \"PENDING\"\n      }), _createVNode(_component_el_option, {\n        label: \"已通过\",\n        value: \"APPROVED\"\n      }), _createVNode(_component_el_option, {\n        label: \"已拒绝\",\n        value: \"REJECTED\"\n      })]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\", \"onChange\"]), _createVNode(_component_el_button, {\n      onClick: $setup.fetchApplications,\n      loading: $setup.loading,\n      icon: _ctx.Refresh\n    }, {\n      default: _withCtx(() => _cache[3] || (_cache[3] = [_createTextVNode(\" 刷新 \")])),\n      _: 1 /* STABLE */,\n      __: [3]\n    }, 8 /* PROPS */, [\"onClick\", \"loading\", \"icon\"])])])]),\n    default: _withCtx(() => [!$setup.loading && $setup.applications.length > 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_4, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.applications, application => {\n      return _openBlock(), _createElementBlock(\"div\", {\n        key: application.id,\n        class: \"application-card\"\n      }, [_createVNode(_component_el_card, {\n        shadow: \"hover\"\n      }, {\n        default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"div\", _hoisted_6, [_createVNode(_component_el_avatar, {\n          size: 40,\n          src: $setup.getAvatarUrl(application.applicantAvatar)\n        }, {\n          default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.getInitial(application.applicantRealName || application.applicantUsername)), 1 /* TEXT */)]),\n          _: 2 /* DYNAMIC */\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"src\"]), _createElementVNode(\"div\", _hoisted_7, [_createElementVNode(\"h4\", null, _toDisplayString(application.applicantRealName || application.applicantUsername), 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_8, \"@\" + _toDisplayString(application.applicantUsername), 1 /* TEXT */)])]), _createVNode(_component_el_tag, {\n          type: $setup.getStatusType(application.status)\n        }, {\n          default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.getStatusText(application.status)), 1 /* TEXT */)]),\n          _: 2 /* DYNAMIC */\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"type\"])]), _createElementVNode(\"div\", _hoisted_9, [_createElementVNode(\"div\", _hoisted_10, [_cache[5] || (_cache[5] = _createElementVNode(\"h5\", null, \"申请团队\", -1 /* CACHED */)), _createElementVNode(\"p\", null, _toDisplayString(application.teamName), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_11, [_cache[6] || (_cache[6] = _createElementVNode(\"h5\", null, \"申请理由\", -1 /* CACHED */)), _createElementVNode(\"p\", _hoisted_12, _toDisplayString(application.applicationMessage || '无申请理由'), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_13, [_createElementVNode(\"div\", _hoisted_14, [_createVNode(_component_el_icon, null, {\n          default: _withCtx(() => [_createVNode(_component_Calendar)]),\n          _: 1 /* STABLE */\n        }), _createElementVNode(\"span\", null, _toDisplayString($setup.formatDate(application.applyTime)), 1 /* TEXT */)]), application.responseTime ? (_openBlock(), _createElementBlock(\"div\", _hoisted_15, [_createVNode(_component_el_icon, null, {\n          default: _withCtx(() => [_createVNode(_component_Check)]),\n          _: 1 /* STABLE */\n        }), _createElementVNode(\"span\", null, _toDisplayString($setup.formatDate(application.responseTime)), 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true)]), application.responseMessage ? (_openBlock(), _createElementBlock(\"div\", _hoisted_16, [_createElementVNode(\"p\", null, [_createElementVNode(\"strong\", null, _toDisplayString(application.status === 'APPROVED' ? '通过理由' : '拒绝理由') + \"：\", 1 /* TEXT */)]), _createElementVNode(\"p\", null, _toDisplayString(application.responseMessage), 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"div\", _hoisted_17, [application.status === 'PENDING' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_18, [_createVNode(_component_el_button, {\n          type: \"success\",\n          size: \"small\",\n          onClick: $event => $setup.reviewApplication(application.id, true),\n          loading: $setup.reviewingApplication === application.id\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n            default: _withCtx(() => [_createVNode(_component_Check)]),\n            _: 1 /* STABLE */\n          }), _cache[7] || (_cache[7] = _createTextVNode(\" 通过 \"))]),\n          _: 2 /* DYNAMIC */,\n          __: [7]\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\", \"loading\"]), _createVNode(_component_el_button, {\n          type: \"danger\",\n          size: \"small\",\n          onClick: $event => $setup.reviewApplication(application.id, false),\n          loading: $setup.reviewingApplication === application.id\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n            default: _withCtx(() => [_createVNode(_component_Close)]),\n            _: 1 /* STABLE */\n          }), _cache[8] || (_cache[8] = _createTextVNode(\" 拒绝 \"))]),\n          _: 2 /* DYNAMIC */,\n          __: [8]\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\", \"loading\"])])) : (_openBlock(), _createElementBlock(\"div\", _hoisted_19, [_createElementVNode(\"span\", _hoisted_20, _toDisplayString($setup.getStatusText(application.status)), 1 /* TEXT */)]))])]),\n        _: 2 /* DYNAMIC */\n      }, 1024 /* DYNAMIC_SLOTS */)]);\n    }), 128 /* KEYED_FRAGMENT */))])) : !$setup.loading && $setup.applications.length === 0 ? (_openBlock(), _createElementBlock(_Fragment, {\n      key: 1\n    }, [_createCommentVNode(\" 空状态 \"), _createElementVNode(\"div\", _hoisted_21, [_createVNode(_component_el_icon, {\n      class: \"empty-icon\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_DocumentRemove)]),\n      _: 1 /* STABLE */\n    }), _cache[9] || (_cache[9] = _createElementVNode(\"h3\", null, \"暂无申请记录\", -1 /* CACHED */)), _cache[10] || (_cache[10] = _createElementVNode(\"p\", null, \"还没有学生申请加入您的团队\", -1 /* CACHED */))])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)) : (_openBlock(), _createElementBlock(_Fragment, {\n      key: 2\n    }, [_createCommentVNode(\" 加载状态 \"), _createElementVNode(\"div\", _hoisted_22, [_createVNode(_component_el_skeleton, {\n      rows: 6,\n      animated: \"\"\n    })])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)), $setup.total > 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_23, [_createVNode(_component_el_pagination, {\n      \"current-page\": $setup.currentPage,\n      \"onUpdate:currentPage\": _cache[1] || (_cache[1] = $event => $setup.currentPage = $event),\n      \"page-size\": $setup.pageSize,\n      \"onUpdate:pageSize\": _cache[2] || (_cache[2] = $event => $setup.pageSize = $event),\n      total: $setup.total,\n      \"page-sizes\": [10, 20, 50],\n      layout: \"total, sizes, prev, pager, next, jumper\",\n      onSizeChange: $setup.handleSizeChange,\n      onCurrentChange: $setup.handleCurrentChange\n    }, null, 8 /* PROPS */, [\"current-page\", \"page-size\", \"total\", \"onSizeChange\", \"onCurrentChange\"])])) : _createCommentVNode(\"v-if\", true)]),\n    _: 1 /* STABLE */\n  })]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_card", "header", "_withCtx", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_component_el_select", "$setup", "statusFilter", "$event", "placeholder", "clearable", "onChange", "fetchApplications", "style", "_component_el_option", "label", "value", "_component_el_button", "onClick", "loading", "icon", "_ctx", "Refresh", "_cache", "applications", "length", "_hoisted_4", "_Fragment", "_renderList", "application", "key", "id", "shadow", "_hoisted_5", "_hoisted_6", "_component_el_avatar", "size", "src", "getAvatarUrl", "applicantAvatar", "getInitial", "applicantRealName", "applicantUsername", "_hoisted_7", "_toDisplayString", "_hoisted_8", "_component_el_tag", "type", "getStatusType", "status", "getStatusText", "_hoisted_9", "_hoisted_10", "teamName", "_hoisted_11", "_hoisted_12", "applicationMessage", "_hoisted_13", "_hoisted_14", "_component_el_icon", "_component_Calendar", "formatDate", "applyTime", "responseTime", "_hoisted_15", "_component_Check", "responseMessage", "_hoisted_16", "_hoisted_17", "_hoisted_18", "reviewApplication", "reviewingApplication", "_component_Close", "_hoisted_19", "_hoisted_20", "_createCommentVNode", "_hoisted_21", "_component_DocumentRemove", "_hoisted_22", "_component_el_skeleton", "rows", "animated", "total", "_hoisted_23", "_component_el_pagination", "currentPage", "pageSize", "layout", "onSizeChange", "handleSizeChange", "onCurrentChange", "handleCurrentChange"], "sources": ["D:\\workspace\\idea\\worker\\work_cli\\src\\views\\team\\TeamApplicationsView.vue"], "sourcesContent": ["<template>\n  <div class=\"team-applications\">\n    <el-card>\n      <template #header>\n        <div class=\"card-header\">\n          <h3>团队申请管理</h3>\n          <div class=\"header-actions\">\n            <el-select v-model=\"statusFilter\" placeholder=\"申请状态\" clearable @change=\"fetchApplications\" style=\"width: 120px; margin-right: 12px;\">\n              <el-option label=\"全部状态\" value=\"\" />\n              <el-option label=\"待审核\" value=\"PENDING\" />\n              <el-option label=\"已通过\" value=\"APPROVED\" />\n              <el-option label=\"已拒绝\" value=\"REJECTED\" />\n            </el-select>\n            <el-button @click=\"fetchApplications\" :loading=\"loading\" :icon=\"Refresh\">\n              刷新\n            </el-button>\n          </div>\n        </div>\n      </template>\n\n      <!-- 申请卡片网格 -->\n      <div v-if=\"!loading && applications.length > 0\" class=\"applications-grid\">\n        <div v-for=\"application in applications\" :key=\"application.id\" class=\"application-card\">\n          <el-card shadow=\"hover\">\n            <div class=\"applicant-header\">\n              <div class=\"applicant-info\">\n                <el-avatar :size=\"40\" :src=\"getAvatarUrl(application.applicantAvatar)\">\n                  {{ getInitial(application.applicantRealName || application.applicantUsername) }}\n                </el-avatar>\n                <div class=\"applicant-details\">\n                  <h4>{{ application.applicantRealName || application.applicantUsername }}</h4>\n                  <span class=\"username\">@{{ application.applicantUsername }}</span>\n                </div>\n              </div>\n              <el-tag :type=\"getStatusType(application.status)\">\n                {{ getStatusText(application.status) }}\n              </el-tag>\n            </div>\n\n            <div class=\"application-content\">\n              <div class=\"team-info\">\n                <h5>申请团队</h5>\n                <p>{{ application.teamName }}</p>\n              </div>\n\n              <div class=\"application-reason\">\n                <h5>申请理由</h5>\n                <p class=\"reason-text\">{{ application.applicationMessage || '无申请理由' }}</p>\n              </div>\n\n              <div class=\"application-meta\">\n                <div class=\"meta-item\">\n                  <el-icon><Calendar /></el-icon>\n                  <span>{{ formatDate(application.applyTime) }}</span>\n                </div>\n                <div v-if=\"application.responseTime\" class=\"meta-item\">\n                  <el-icon><Check /></el-icon>\n                  <span>{{ formatDate(application.responseTime) }}</span>\n                </div>\n              </div>\n\n              <div v-if=\"application.responseMessage\" class=\"review-reason\">\n                <p><strong>{{ application.status === 'APPROVED' ? '通过理由' : '拒绝理由' }}：</strong></p>\n                <p>{{ application.responseMessage }}</p>\n              </div>\n            </div>\n\n            <div class=\"application-footer\">\n              <div v-if=\"application.status === 'PENDING'\" class=\"action-buttons\">\n                <el-button\n                  type=\"success\"\n                  size=\"small\"\n                  @click=\"reviewApplication(application.id, true)\"\n                  :loading=\"reviewingApplication === application.id\"\n                >\n                  <el-icon><Check /></el-icon>\n                  通过\n                </el-button>\n\n                <el-button\n                  type=\"danger\"\n                  size=\"small\"\n                  @click=\"reviewApplication(application.id, false)\"\n                  :loading=\"reviewingApplication === application.id\"\n                >\n                  <el-icon><Close /></el-icon>\n                  拒绝\n                </el-button>\n              </div>\n              <div v-else class=\"status-info\">\n                <span class=\"status-text\">{{ getStatusText(application.status) }}</span>\n              </div>\n            </div>\n          </el-card>\n        </div>\n      </div>\n\n      <!-- 空状态 -->\n      <div v-else-if=\"!loading && applications.length === 0\" class=\"empty-state\">\n        <el-icon class=\"empty-icon\"><DocumentRemove /></el-icon>\n        <h3>暂无申请记录</h3>\n        <p>还没有学生申请加入您的团队</p>\n      </div>\n\n      <!-- 加载状态 -->\n      <div v-else class=\"loading-state\">\n        <el-skeleton :rows=\"6\" animated />\n      </div>\n\n      <!-- 分页 -->\n      <div v-if=\"total > 0\" class=\"pagination\">\n        <el-pagination\n          v-model:current-page=\"currentPage\"\n          v-model:page-size=\"pageSize\"\n          :total=\"total\"\n          :page-sizes=\"[10, 20, 50]\"\n          layout=\"total, sizes, prev, pager, next, jumper\"\n          @size-change=\"handleSizeChange\"\n          @current-change=\"handleCurrentChange\"\n        />\n      </div>\n    </el-card>\n  </div>\n</template>\n\n<script>\nimport { ref, onMounted, computed } from 'vue'\nimport { ElMessage, ElMessageBox } from 'element-plus'\nimport { teamAPI } from '@/api'\nimport { getAvatarUrl, getInitial } from '@/utils/avatar'\nimport {\n  Clock, Check, Close, Refresh, Calendar, User, Document,\n  DocumentRemove\n} from '@element-plus/icons-vue'\n\nexport default {\n  name: 'TeamApplicationsView',\n  components: {\n    Clock, Check, Close, Refresh, Calendar, User, Document,\n    DocumentRemove\n  },\n  setup() {\n    const applications = ref([])\n    const loading = ref(true)\n    const reviewingApplication = ref(null)\n    const currentTeam = ref(null)\n\n    // 分页\n    const currentPage = ref(1)\n    const pageSize = ref(10)\n    const total = ref(0)\n\n    // 筛选\n    const statusFilter = ref('')\n\n\n\n    // 获取当前用户的团队信息\n    const fetchCurrentTeam = async () => {\n      try {\n        const response = await teamAPI.getMyTeam()\n        currentTeam.value = response.data || response\n        return currentTeam.value\n      } catch (error) {\n        console.error('获取团队信息失败:', error)\n        ElMessage.error('获取团队信息失败')\n        return null\n      }\n    }\n\n    const fetchApplications = async () => {\n      try {\n        loading.value = true\n\n        // 先获取当前团队信息\n        let team = currentTeam.value\n        if (!team) {\n          team = await fetchCurrentTeam()\n        }\n\n        if (!team || !team.id) {\n          ElMessage.warning('您还没有团队，无法查看申请')\n          applications.value = []\n          total.value = 0\n          return\n        }\n\n        // 检查是否是队长\n        const userInfo = JSON.parse(localStorage.getItem('user') || '{}')\n        console.log('=== 前端权限检查调试信息 ===')\n        console.log('当前用户信息:', userInfo)\n        console.log('团队信息:', team)\n        console.log('team.leaderId:', team.leaderId, '类型:', typeof team.leaderId)\n        console.log('team.leader?.id:', team.leader?.id, '类型:', typeof team.leader?.id)\n        console.log('userInfo.id:', userInfo.id, '类型:', typeof userInfo.id)\n        console.log('leaderId === userInfo.id:', team.leaderId === userInfo.id)\n        console.log('leader?.id === userInfo.id:', team.leader?.id === userInfo.id)\n\n        if (team.leaderId !== userInfo.id && team.leader?.id !== userInfo.id) {\n          console.log('=== 前端权限检查失败，用户不是队长 ===')\n          ElMessage.warning('只有队长才能查看团队申请')\n          applications.value = []\n          total.value = 0\n          return\n        }\n        console.log('=== 前端权限检查通过，用户是队长 ===')\n\n        const params = {\n          page: currentPage.value,\n          size: pageSize.value,\n          status: statusFilter.value\n        }\n\n        // 使用团队ID获取申请列表\n        console.log('正在获取团队申请列表，团队ID:', team.id, '参数:', params)\n        const response = await teamAPI.getTeamApplications(team.id, params)\n        console.log('API响应:', response)\n        console.log('响应数据结构:', {\n          hasRecords: response.records !== undefined,\n          hasDataRecords: response.data && response.data.records !== undefined,\n          isArray: Array.isArray(response),\n          responseKeys: Object.keys(response)\n        })\n\n        // 处理不同的响应数据结构\n        if (response.records !== undefined) {\n          // 直接返回分页数据的情况\n          applications.value = response.records || []\n          total.value = response.total || 0\n        } else if (response.data && response.data.records !== undefined) {\n          // 嵌套在data中的情况\n          applications.value = response.data.records || []\n          total.value = response.data.total || 0\n        } else {\n          // 直接返回数组的情况\n          applications.value = Array.isArray(response) ? response : []\n          total.value = applications.value.length\n        }\n      } catch (error) {\n        console.error('获取申请列表失败:', error)\n        ElMessage.error('获取申请列表失败')\n        applications.value = []\n        total.value = 0\n      } finally {\n        loading.value = false\n      }\n    }\n\n    const reviewApplication = async (applicationId, approved) => {\n      try {\n        let reviewReason = ''\n        \n        // 如果是拒绝，要求输入拒绝理由\n        if (!approved) {\n          const { value } = await ElMessageBox.prompt(\n            '请输入拒绝理由：',\n            '拒绝申请',\n            {\n              confirmButtonText: '确认拒绝',\n              cancelButtonText: '取消',\n              inputPlaceholder: '请说明拒绝的原因...',\n              inputValidator: (value) => {\n                if (!value || value.trim().length < 5) {\n                  return '拒绝理由至少需要5个字符'\n                }\n                return true\n              }\n            }\n          )\n          reviewReason = value\n        } else {\n          // 通过申请可以选择性输入理由\n          try {\n            const { value } = await ElMessageBox.prompt(\n              '通过理由（可选）：',\n              '通过申请',\n              {\n                confirmButtonText: '确认通过',\n                cancelButtonText: '直接通过',\n                inputPlaceholder: '欢迎加入团队...',\n                distinguishCancelAndClose: true\n              }\n            )\n            reviewReason = value || '申请通过'\n          } catch (action) {\n            if (action === 'cancel') {\n              reviewReason = '申请通过'\n            } else {\n              return // 用户关闭了对话框\n            }\n          }\n        }\n        \n        reviewingApplication.value = applicationId\n        await teamAPI.reviewApplication(applicationId, {\n          approved,\n          reviewReason\n        })\n        \n        ElMessage.success(approved ? '申请已通过' : '申请已拒绝')\n        await fetchApplications() // 刷新列表\n      } catch (error) {\n        if (error === 'cancel') {\n          return // 用户取消了操作\n        }\n        console.error('审核申请失败:', error)\n        ElMessage.error(error.response?.data?.message || '审核申请失败')\n      } finally {\n        reviewingApplication.value = null\n      }\n    }\n\n    const handleSizeChange = (newSize) => {\n      pageSize.value = newSize\n      currentPage.value = 1\n      fetchApplications()\n    }\n\n    const handleCurrentChange = (newPage) => {\n      currentPage.value = newPage\n      fetchApplications()\n    }\n\n    const getStatusType = (status) => {\n      const statusMap = {\n        'PENDING': 'warning',\n        'APPROVED': 'success',\n        'REJECTED': 'danger',\n        'CANCELLED': 'info'\n      }\n      return statusMap[status] || 'info'\n    }\n\n    const getStatusText = (status) => {\n      const statusTextMap = {\n        'PENDING': '待审核',\n        'APPROVED': '已通过',\n        'REJECTED': '已拒绝',\n        'CANCELLED': '已取消'\n      }\n      return statusTextMap[status] || status\n    }\n\n    const formatDateTime = (dateTime) => {\n      if (!dateTime) return ''\n      return new Date(dateTime).toLocaleString('zh-CN')\n    }\n\n    const formatDate = (dateTime) => {\n      if (!dateTime) return ''\n      return new Date(dateTime).toLocaleDateString('zh-CN')\n    }\n\n    onMounted(() => {\n      fetchApplications()\n    })\n\n    return {\n      applications,\n      loading,\n      reviewingApplication,\n      currentTeam,\n      currentPage,\n      pageSize,\n      total,\n      statusFilter,\n      fetchApplications,\n      fetchCurrentTeam,\n      reviewApplication,\n      handleSizeChange,\n      handleCurrentChange,\n      getStatusType,\n      getStatusText,\n      formatDateTime,\n      formatDate,\n      getAvatarUrl,\n      getInitial\n    }\n  }\n}\n</script>\n\n<style scoped>\n/* 参考项目浏览界面的设计规范 */\n.team-applications {\n  padding: 24px;\n  background: #f5f7fa;\n  min-height: 100vh;\n}\n\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.card-header h3 {\n  margin: 0;\n  color: #303133;\n  font-size: 20px;\n  font-weight: 600;\n}\n\n.header-actions {\n  display: flex;\n  align-items: center;\n}\n\n/* 申请卡片网格布局 */\n.applications-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));\n  gap: 20px;\n  margin-top: 20px;\n}\n\n.application-card {\n  transition: all 0.3s ease;\n}\n\n.application-card:hover {\n  transform: translateY(-2px);\n}\n\n.application-card .el-card {\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n}\n\n.application-card .el-card__body {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n}\n\n.applicant-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  margin-bottom: 15px;\n}\n\n.applicant-info {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n}\n\n.applicant-details h4 {\n  margin: 0 0 4px 0;\n  color: #303133;\n  font-size: 16px;\n  font-weight: 600;\n}\n\n.username {\n  color: #909399;\n  font-size: 12px;\n}\n\n.application-content {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  gap: 15px;\n}\n\n.team-info h5,\n.application-reason h5 {\n  margin: 0 0 8px 0;\n  color: #606266;\n  font-size: 14px;\n  font-weight: 600;\n}\n\n.team-info p {\n  margin: 0;\n  color: #303133;\n  font-size: 14px;\n}\n\n.reason-text {\n  margin: 0;\n  color: #606266;\n  font-size: 14px;\n  line-height: 1.5;\n  display: -webkit-box;\n  -webkit-line-clamp: 2;\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n}\n\n.application-meta {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.meta-item {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  color: #606266;\n  font-size: 12px;\n}\n\n.meta-item .el-icon {\n  color: #409EFF;\n  font-size: 14px;\n}\n\n.review-reason {\n  margin-top: 10px;\n  padding: 12px;\n  background: #f5f7fa;\n  border-radius: 6px;\n  border-left: 3px solid #409EFF;\n}\n\n.review-reason p {\n  margin: 0 0 8px 0;\n  color: #606266;\n  font-size: 14px;\n  line-height: 1.5;\n}\n\n.review-reason p:last-child {\n  margin-bottom: 0;\n}\n\n.application-footer {\n  margin-top: auto;\n  padding-top: 15px;\n  border-top: 1px solid #EBEEF5;\n}\n\n.action-buttons {\n  display: flex;\n  gap: 8px;\n  justify-content: flex-end;\n}\n\n.status-info {\n  text-align: center;\n}\n\n.status-text {\n  color: #909399;\n  font-size: 14px;\n}\n\n/* 空状态和加载状态 */\n.empty-state {\n  text-align: center;\n  padding: 60px 20px;\n  color: #909399;\n}\n\n.empty-icon {\n  font-size: 64px;\n  color: #C0C4CC;\n  margin-bottom: 16px;\n}\n\n.empty-state h3 {\n  margin: 16px 0 8px 0;\n  color: #606266;\n  font-weight: 500;\n}\n\n.empty-state p {\n  margin: 0;\n  color: #909399;\n}\n\n.loading-state {\n  padding: 20px;\n}\n\n.pagination {\n  display: flex;\n  justify-content: center;\n  margin-top: 20px;\n  padding: 20px 0;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .team-applications {\n    padding: 16px;\n  }\n\n  .applications-grid {\n    grid-template-columns: 1fr;\n    gap: 16px;\n  }\n\n  .card-header {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 12px;\n  }\n\n  .header-actions {\n    width: 100%;\n    justify-content: space-between;\n  }\n\n  .applicant-header {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 12px;\n  }\n\n  .action-buttons {\n    flex-direction: column;\n    gap: 8px;\n  }\n\n  .action-buttons .el-button {\n    width: 100%;\n  }\n}\n\n/* 按钮样式优化 */\n:deep(.el-button--success) {\n  background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);\n  border-color: #67c23a;\n  border-radius: 6px;\n  font-weight: 500;\n  transition: all 0.3s ease;\n}\n\n:deep(.el-button--success:hover) {\n  background: linear-gradient(135deg, #5daf34 0%, #7bc143 100%);\n  border-color: #5daf34;\n  transform: translateY(-1px);\n  box-shadow: 0 4px 12px rgba(103, 194, 58, 0.3);\n}\n\n:deep(.el-button--danger) {\n  background: linear-gradient(135deg, #f56c6c 0%, #ff8a8a 100%);\n  border-color: #f56c6c;\n  border-radius: 6px;\n  font-weight: 500;\n  transition: all 0.3s ease;\n}\n\n:deep(.el-button--danger:hover) {\n  background: linear-gradient(135deg, #e85656 0%, #f07777 100%);\n  border-color: #e85656;\n  transform: translateY(-1px);\n  box-shadow: 0 4px 12px rgba(245, 108, 108, 0.3);\n}\n\n/* 状态标签样式优化 */\n:deep(.el-tag) {\n  border-radius: 12px;\n  font-weight: 500;\n  font-size: 12px;\n  padding: 4px 12px;\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAmB;;EAGnBA,KAAK,EAAC;AAAa;;EAEjBA,KAAK,EAAC;AAAgB;;;EAeiBA,KAAK,EAAC;;;EAG3CA,KAAK,EAAC;AAAkB;;EACtBA,KAAK,EAAC;AAAgB;;EAIpBA,KAAK,EAAC;AAAmB;;EAEtBA,KAAK,EAAC;AAAU;;EAQvBA,KAAK,EAAC;AAAqB;;EACzBA,KAAK,EAAC;AAAW;;EAKjBA,KAAK,EAAC;AAAoB;;EAE1BA,KAAK,EAAC;AAAa;;EAGnBA,KAAK,EAAC;AAAkB;;EACtBA,KAAK,EAAC;AAAW;;;EAIeA,KAAK,EAAC;;;;EAMLA,KAAK,EAAC;;;EAM3CA,KAAK,EAAC;AAAoB;;;EACgBA,KAAK,EAAC;;;;EAqBvCA,KAAK,EAAC;;;EACVA,KAAK,EAAC;AAAa;;EAQoBA,KAAK,EAAC;AAAa;;EAO9DA,KAAK,EAAC;AAAe;;;EAKXA,KAAK,EAAC;;;;;;;;;;;;;;;;uBA7GhCC,mBAAA,CAyHM,OAzHNC,UAyHM,GAxHJC,YAAA,CAuHUC,kBAAA;IAtHGC,MAAM,EAAAC,QAAA,CACf,MAaM,CAbNC,mBAAA,CAaM,OAbNC,UAaM,G,0BAZJD,mBAAA,CAAe,YAAX,QAAM,qBACVA,mBAAA,CAUM,OAVNE,UAUM,GATJN,YAAA,CAKYO,oBAAA;kBALQC,MAAA,CAAAC,YAAY;iEAAZD,MAAA,CAAAC,YAAY,GAAAC,MAAA;MAAEC,WAAW,EAAC,MAAM;MAACC,SAAS,EAAT,EAAS;MAAEC,QAAM,EAAEL,MAAA,CAAAM,iBAAiB;MAAEC,KAAyC,EAAzC;QAAA;QAAA;MAAA;;wBACzF,MAAmC,CAAnCf,YAAA,CAAmCgB,oBAAA;QAAxBC,KAAK,EAAC,MAAM;QAACC,KAAK,EAAC;UAC9BlB,YAAA,CAAyCgB,oBAAA;QAA9BC,KAAK,EAAC,KAAK;QAACC,KAAK,EAAC;UAC7BlB,YAAA,CAA0CgB,oBAAA;QAA/BC,KAAK,EAAC,KAAK;QAACC,KAAK,EAAC;UAC7BlB,YAAA,CAA0CgB,oBAAA;QAA/BC,KAAK,EAAC,KAAK;QAACC,KAAK,EAAC;;;mDAE/BlB,YAAA,CAEYmB,oBAAA;MAFAC,OAAK,EAAEZ,MAAA,CAAAM,iBAAiB;MAAGO,OAAO,EAAEb,MAAA,CAAAa,OAAO;MAAGC,IAAI,EAAEC,IAAA,CAAAC;;wBAAS,MAEzEC,MAAA,QAAAA,MAAA,O,iBAFyE,MAEzE,E;;;;sBAcyB,MAqGA,C,CA7GnBjB,MAAA,CAAAa,OAAO,IAAIb,MAAA,CAAAkB,YAAY,CAACC,MAAM,Q,cAA1C7B,mBAAA,CA0EM,OA1EN8B,UA0EM,I,kBAzEJ9B,mBAAA,CAwEM+B,SAAA,QAAAC,WAAA,CAxEqBtB,MAAA,CAAAkB,YAAY,EAA3BK,WAAW;2BAAvBjC,mBAAA,CAwEM;QAxEoCkC,GAAG,EAAED,WAAW,CAACE,EAAE;QAAEpC,KAAK,EAAC;UACnEG,YAAA,CAsEUC,kBAAA;QAtEDiC,MAAM,EAAC;MAAO;0BACrB,MAaM,CAbN9B,mBAAA,CAaM,OAbN+B,UAaM,GAZJ/B,mBAAA,CAQM,OARNgC,UAQM,GAPJpC,YAAA,CAEYqC,oBAAA;UAFAC,IAAI,EAAE,EAAE;UAAGC,GAAG,EAAE/B,MAAA,CAAAgC,YAAY,CAACT,WAAW,CAACU,eAAe;;4BAClE,MAAgF,C,kCAA7EjC,MAAA,CAAAkC,UAAU,CAACX,WAAW,CAACY,iBAAiB,IAAIZ,WAAW,CAACa,iBAAiB,kB;;sDAE9ExC,mBAAA,CAGM,OAHNyC,UAGM,GAFJzC,mBAAA,CAA6E,YAAA0C,gBAAA,CAAtEf,WAAW,CAACY,iBAAiB,IAAIZ,WAAW,CAACa,iBAAiB,kBACrExC,mBAAA,CAAkE,QAAlE2C,UAAkE,EAA3C,GAAC,GAAAD,gBAAA,CAAGf,WAAW,CAACa,iBAAiB,iB,KAG5D5C,YAAA,CAESgD,iBAAA;UAFAC,IAAI,EAAEzC,MAAA,CAAA0C,aAAa,CAACnB,WAAW,CAACoB,MAAM;;4BAC7C,MAAuC,C,kCAApC3C,MAAA,CAAA4C,aAAa,CAACrB,WAAW,CAACoB,MAAM,kB;;yDAIvC/C,mBAAA,CA0BM,OA1BNiD,UA0BM,GAzBJjD,mBAAA,CAGM,OAHNkD,WAGM,G,0BAFJlD,mBAAA,CAAa,YAAT,MAAI,qBACRA,mBAAA,CAAiC,WAAA0C,gBAAA,CAA3Bf,WAAW,CAACwB,QAAQ,iB,GAG5BnD,mBAAA,CAGM,OAHNoD,WAGM,G,0BAFJpD,mBAAA,CAAa,YAAT,MAAI,qBACRA,mBAAA,CAA0E,KAA1EqD,WAA0E,EAAAX,gBAAA,CAAhDf,WAAW,CAAC2B,kBAAkB,4B,GAG1DtD,mBAAA,CASM,OATNuD,WASM,GARJvD,mBAAA,CAGM,OAHNwD,WAGM,GAFJ5D,YAAA,CAA+B6D,kBAAA;4BAAtB,MAAY,CAAZ7D,YAAA,CAAY8D,mBAAA,E;;YACrB1D,mBAAA,CAAoD,cAAA0C,gBAAA,CAA3CtC,MAAA,CAAAuD,UAAU,CAAChC,WAAW,CAACiC,SAAS,kB,GAEhCjC,WAAW,CAACkC,YAAY,I,cAAnCnE,mBAAA,CAGM,OAHNoE,WAGM,GAFJlE,YAAA,CAA4B6D,kBAAA;4BAAnB,MAAS,CAAT7D,YAAA,CAASmE,gBAAA,E;;YAClB/D,mBAAA,CAAuD,cAAA0C,gBAAA,CAA9CtC,MAAA,CAAAuD,UAAU,CAAChC,WAAW,CAACkC,YAAY,kB,0CAIrClC,WAAW,CAACqC,eAAe,I,cAAtCtE,mBAAA,CAGM,OAHNuE,WAGM,GAFJjE,mBAAA,CAAkF,YAA/EA,mBAAA,CAA2E,gBAAA0C,gBAAA,CAAhEf,WAAW,CAACoB,MAAM,qCAAoC,GAAC,gB,GACrE/C,mBAAA,CAAwC,WAAA0C,gBAAA,CAAlCf,WAAW,CAACqC,eAAe,iB,0CAIrChE,mBAAA,CAyBM,OAzBNkE,WAyBM,GAxBOvC,WAAW,CAACoB,MAAM,kB,cAA7BrD,mBAAA,CAoBM,OApBNyE,WAoBM,GAnBJvE,YAAA,CAQYmB,oBAAA;UAPV8B,IAAI,EAAC,SAAS;UACdX,IAAI,EAAC,OAAO;UACXlB,OAAK,EAAAV,MAAA,IAAEF,MAAA,CAAAgE,iBAAiB,CAACzC,WAAW,CAACE,EAAE;UACvCZ,OAAO,EAAEb,MAAA,CAAAiE,oBAAoB,KAAK1C,WAAW,CAACE;;4BAE/C,MAA4B,CAA5BjC,YAAA,CAA4B6D,kBAAA;8BAAnB,MAAS,CAAT7D,YAAA,CAASmE,gBAAA,E;;yDAAU,MAE9B,G;;;qEAEAnE,YAAA,CAQYmB,oBAAA;UAPV8B,IAAI,EAAC,QAAQ;UACbX,IAAI,EAAC,OAAO;UACXlB,OAAK,EAAAV,MAAA,IAAEF,MAAA,CAAAgE,iBAAiB,CAACzC,WAAW,CAACE,EAAE;UACvCZ,OAAO,EAAEb,MAAA,CAAAiE,oBAAoB,KAAK1C,WAAW,CAACE;;4BAE/C,MAA4B,CAA5BjC,YAAA,CAA4B6D,kBAAA;8BAAnB,MAAS,CAAT7D,YAAA,CAAS0E,gBAAA,E;;yDAAU,MAE9B,G;;;wFAEF5E,mBAAA,CAEM,OAFN6E,WAEM,GADJvE,mBAAA,CAAwE,QAAxEwE,WAAwE,EAAA9B,gBAAA,CAA3CtC,MAAA,CAAA4C,aAAa,CAACrB,WAAW,CAACoB,MAAM,kB;;;yCAQtD3C,MAAA,CAAAa,OAAO,IAAIb,MAAA,CAAAkB,YAAY,CAACC,MAAM,U,cAA/C7B,mBAAA,CAIM+B,SAAA;MAAAG,GAAA;IAAA,IALN6C,mBAAA,SAAY,EACZzE,mBAAA,CAIM,OAJN0E,WAIM,GAHJ9E,YAAA,CAAwD6D,kBAAA;MAA/ChE,KAAK,EAAC;IAAY;wBAAC,MAAkB,CAAlBG,YAAA,CAAkB+E,yBAAA,E;;kCAC9C3E,mBAAA,CAAe,YAAX,QAAM,qB,4BACVA,mBAAA,CAAoB,WAAjB,eAAa,oB,qEAIlBN,mBAAA,CAEM+B,SAAA;MAAAG,GAAA;IAAA,IAHN6C,mBAAA,UAAa,EACbzE,mBAAA,CAEM,OAFN4E,WAEM,GADJhF,YAAA,CAAkCiF,sBAAA;MAApBC,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAR;4DAId3E,MAAA,CAAA4E,KAAK,Q,cAAhBtF,mBAAA,CAUM,OAVNuF,WAUM,GATJrF,YAAA,CAQEsF,wBAAA;MAPQ,cAAY,EAAE9E,MAAA,CAAA+E,WAAW;kEAAX/E,MAAA,CAAA+E,WAAW,GAAA7E,MAAA;MACzB,WAAS,EAAEF,MAAA,CAAAgF,QAAQ;+DAARhF,MAAA,CAAAgF,QAAQ,GAAA9E,MAAA;MAC1B0E,KAAK,EAAE5E,MAAA,CAAA4E,KAAK;MACZ,YAAU,EAAE,YAAY;MACzBK,MAAM,EAAC,yCAAyC;MAC/CC,YAAW,EAAElF,MAAA,CAAAmF,gBAAgB;MAC7BC,eAAc,EAAEpF,MAAA,CAAAqF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}