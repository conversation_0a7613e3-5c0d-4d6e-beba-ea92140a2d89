/* Soft Modern UI Design System */

/* Import design system styles */
@import './variables.css';
@import './base.css';
@import './components.css';

/* Element Plus Theme Overrides - 使用新的设计系统 */
:root {
  /* 主色调 */
  --el-color-primary: var(--primary-500);
  --el-color-primary-light-3: var(--primary-300);
  --el-color-primary-light-5: var(--primary-200);
  --el-color-primary-light-7: var(--primary-100);
  --el-color-primary-light-8: var(--primary-50);
  --el-color-primary-light-9: rgba(99, 102, 241, 0.05);
  --el-color-primary-dark-2: var(--primary-700);

  /* 语义化颜色 */
  --el-color-success: var(--success-500);
  --el-color-warning: var(--warning-500);
  --el-color-danger: var(--error-500);
  --el-color-error: var(--error-500);
  --el-color-info: var(--info-500);

  /* 文本颜色 */
  --el-text-color-primary: var(--text-primary);
  --el-text-color-regular: var(--text-secondary);
  --el-text-color-secondary: var(--text-tertiary);
  --el-text-color-placeholder: var(--text-placeholder);

  /* 边框和背景 */
  --el-border-color: var(--gray-200);
  --el-border-color-light: var(--gray-100);
  --el-border-color-lighter: var(--gray-50);
  --el-fill-color-blank: var(--surface-color);
  --el-bg-color: var(--background-color);
  --el-bg-color-page: var(--background-color);

  /* 圆角 */
  --el-border-radius-base: var(--radius-lg);
  --el-border-radius-small: var(--radius-md);
  --el-border-radius-round: var(--radius-full);
  --el-border-radius-circle: 50%;

  /* 阴影 */
  --el-box-shadow: var(--shadow-md);
  --el-box-shadow-light: var(--shadow-sm);
  --el-box-shadow-dark: var(--shadow-lg);

  /* 字体 */
  --el-font-family: var(--font-sans);
  --el-font-size-base: var(--text-sm);
  --el-font-size-small: var(--text-xs);
  --el-font-size-large: var(--text-base);
}

/* Element Plus Component Overrides */

/* Element Plus Component Overrides */

/* Button Overrides */
.el-button {
  border-radius: var(--radius-lg) !important;
  font-weight: var(--font-weight-medium);
  transition: all var(--transition-base);
  font-family: var(--font-sans);
  box-shadow: var(--shadow-xs);
  border: 1px solid transparent;
}

.el-button--primary {
  background: var(--primary-gradient) !important;
  border-color: var(--primary-600) !important;
  box-shadow: var(--shadow-sm) !important;
}

.el-button--primary:hover,
.el-button--primary:focus {
  background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-800) 100%) !important;
  transform: translateY(-1px);
  box-shadow: var(--shadow-md) !important;
  border-color: var(--primary-700) !important;
}

.el-button--default {
  background: var(--surface-color) !important;
  color: var(--text-secondary) !important;
  border-color: var(--gray-300) !important;
}

.el-button--default:hover,
.el-button--default:focus {
  background: var(--gray-50) !important;
  color: var(--text-primary) !important;
  border-color: var(--gray-400) !important;
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm) !important;
}

.el-button--default:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-card) !important;
  color: var(--text-primary) !important;
}

/* Card Overrides */
.el-card {
  border-radius: var(--radius-xl) !important;
  box-shadow: var(--shadow-sm) !important;
  border: 1px solid var(--gray-200) !important;
  transition: all var(--transition-base);
  background: var(--surface-color) !important;
  overflow: hidden;
}

.el-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md) !important;
  border-color: var(--gray-300) !important;
}

.el-card__header {
  background: var(--gray-50) !important;
  border-bottom: 1px solid var(--gray-200) !important;
  padding: var(--space-4) var(--space-6) !important;
  font-weight: var(--font-weight-semibold) !important;
  color: var(--text-primary) !important;
}

.el-card__body {
  padding: var(--space-6) !important;
}

.el-card__header {
  border-bottom: 1px solid rgba(0, 0, 0, 0.05) !important;
  padding: var(--space-6) var(--space-6) var(--space-4) var(--space-6) !important;
}

.el-card__body {
  padding: var(--space-6) !important;
}

/* Input Overrides */
.el-input__wrapper {
  border-radius: var(--radius-lg) !important;
  border: 1px solid var(--gray-300) !important;
  box-shadow: var(--shadow-xs) !important;
  background: var(--surface-color) !important;
  transition: all var(--transition-base);
  padding: var(--space-3) var(--space-4) !important;
}

.el-input__wrapper:hover {
  border-color: var(--gray-400) !important;
  box-shadow: var(--shadow-sm) !important;
}

.el-input__wrapper.is-focus {
  border-color: var(--primary-500) !important;
  box-shadow: var(--shadow-sm), 0 0 0 3px var(--primary-100) !important;
  background: var(--surface-color) !important;
}

.el-input__inner {
  color: var(--text-primary) !important;
  font-family: var(--font-family) !important;
}

.el-input__inner::placeholder {
  color: var(--text-placeholder) !important;
}

/* Select Overrides */
.el-select .el-input__wrapper {
  border-radius: var(--radius-lg) !important;
}

/* 确保选择框有足够的最小宽度 */
.el-select {
  min-width: 120px !important;
}

/* 搜索框统一宽度 */
.search-bar .el-input {
  width: 200px !important;
}

/* 下拉菜单样式 - 防止重叠 */
.el-select-dropdown {
  min-width: 140px !important;
  width: auto !important;
  border-radius: 12px !important;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1) !important;
  border: 1px solid #e5e7eb !important;
  background: white !important;
  margin-top: 4px !important;
  z-index: 9999 !important;
  position: absolute !important;
}

/* 统一的下拉选项样式 - 防止样式冲突 */
.el-select-dropdown__item {
  padding: 8px 16px !important;
  white-space: nowrap !important;
  overflow: visible !important;
  text-overflow: clip !important;
  border-radius: 8px !important;
  margin: 2px 4px !important;
  transition: all 0.2s ease !important;
  background: transparent !important;
  color: #374151 !important;
}

.el-select-dropdown__item:hover {
  background: #f0f4ff !important;
  color: #4f46e5 !important;
}

.el-select-dropdown__item.selected {
  background: #6366f1 !important;
  color: white !important;
}

/* 强制覆盖可能的重复样式 */
body .el-select-dropdown__item {
  padding: 8px 16px !important;
  margin: 2px 4px !important;
  border-radius: 8px !important;
}

/* Form Item Overrides */
.el-form-item__label {
  font-weight: var(--font-weight-medium) !important;
  color: var(--text-primary) !important;
  font-size: var(--text-sm) !important;
  font-family: var(--font-sans) !important;
  margin-bottom: var(--space-2) !important;
}

.el-form-item__error {
  color: var(--error-500) !important;
  font-size: var(--text-xs) !important;
  font-family: var(--font-sans) !important;
  margin-top: var(--space-1) !important;
}

/* Textarea Overrides */
.el-textarea__inner {
  border-radius: var(--radius-lg) !important;
  border: 1px solid var(--gray-300) !important;
  box-shadow: var(--shadow-xs) !important;
  font-family: var(--font-sans) !important;
  transition: all var(--transition-base) !important;
  padding: var(--space-3) var(--space-4) !important;
  font-size: var(--text-sm) !important;
  line-height: var(--leading-relaxed) !important;
}

.el-textarea__inner:hover {
  border-color: var(--gray-400) !important;
  box-shadow: var(--shadow-sm) !important;
}

.el-textarea__inner:focus {
  border-color: var(--primary-500) !important;
  box-shadow: var(--shadow-sm), 0 0 0 3px var(--primary-100) !important;
}

/* Menu Overrides */
.el-menu {
  border: none !important;
  background: transparent !important;
}

.el-menu-item {
  border-radius: var(--radius-md) !important;
  margin: var(--space-1) var(--space-3) !important;
  transition: all var(--transition-base);
}

.el-menu-item:hover {
  background: var(--primary-50) !important;
  color: var(--primary-600) !important;
}

.el-menu-item.is-active {
  background: var(--primary-gradient) !important;
  color: var(--text-on-primary) !important;
}

/* Tag Overrides */
.el-tag {
  border-radius: var(--radius-full) !important;
  border: 1px solid transparent !important;
  font-size: var(--text-xs) !important;
  font-weight: var(--font-weight-medium) !important;
  font-family: var(--font-sans) !important;
  padding: var(--space-1) var(--space-3) !important;
  transition: all var(--transition-base) !important;
}

.el-tag--primary {
  background: var(--primary-100) !important;
  color: var(--primary-700) !important;
  border-color: var(--primary-200) !important;
}

.el-tag--success {
  background: var(--success-50) !important;
  color: var(--success-600) !important;
  border-color: var(--success-200) !important;
}

.el-tag--warning {
  background: var(--warning-50) !important;
  color: var(--warning-600) !important;
  border-color: var(--warning-200) !important;
}

.el-tag--danger {
  background: var(--error-50) !important;
  color: var(--error-600) !important;
  border-color: var(--error-200) !important;
}

.el-tag--info {
  background: var(--info-50) !important;
  color: var(--info-600) !important;
  border-color: var(--info-200) !important;
}

.el-tag.is-closable:hover {
  transform: scale(1.05) !important;
  box-shadow: var(--shadow-sm) !important;
}

/* Badge Overrides */
.el-badge__content {
  background: var(--error-500) !important;
  border: 2px solid var(--surface-color) !important;
  font-size: var(--text-xs) !important;
  font-weight: var(--font-weight-semibold) !important;
  font-family: var(--font-sans) !important;
  box-shadow: var(--shadow-sm) !important;
}

/* Notification Overrides */
.el-notification {
  border-radius: var(--radius-xl) !important;
  box-shadow: var(--shadow-xl) !important;
  border: 1px solid var(--gray-200) !important;
  backdrop-filter: blur(10px) !important;
  padding: var(--space-4) !important;
}

.el-notification__title {
  font-weight: var(--font-weight-semibold) !important;
  font-family: var(--font-sans) !important;
  color: var(--text-primary) !important;
  font-size: var(--text-base) !important;
}

.el-notification__content {
  font-family: var(--font-sans) !important;
  color: var(--text-secondary) !important;
  font-size: var(--text-sm) !important;
  line-height: var(--leading-relaxed) !important;
}

.el-notification--success {
  background: var(--success-50) !important;
  border-color: var(--success-200) !important;
}

.el-notification--warning {
  background: var(--warning-50) !important;
  border-color: var(--warning-200) !important;
}

.el-notification--error {
  background: var(--error-50) !important;
  border-color: var(--error-200) !important;
}

.el-notification--info {
  background: var(--info-50) !important;
  border-color: var(--info-200) !important;
}

/* Tooltip Overrides */
.el-tooltip__popper {
  border-radius: var(--radius-lg) !important;
  box-shadow: var(--shadow-lg) !important;
  border: 1px solid var(--gray-200) !important;
  backdrop-filter: blur(10px) !important;
  font-family: var(--font-sans) !important;
  font-size: var(--text-xs) !important;
  padding: var(--space-2) var(--space-3) !important;
}

/* Popover Overrides */
.el-popover {
  border-radius: var(--radius-xl) !important;
  box-shadow: var(--shadow-lg) !important;
  border: 1px solid var(--gray-200) !important;
  backdrop-filter: blur(10px) !important;
  padding: var(--space-4) !important;
}

.el-popover__title {
  font-weight: var(--font-weight-semibold) !important;
  font-family: var(--font-sans) !important;
  color: var(--text-primary) !important;
  font-size: var(--text-sm) !important;
  margin-bottom: var(--space-2) !important;
}

/* Progress Overrides */
.el-progress-bar__outer {
  border-radius: var(--radius-full) !important;
  background: var(--gray-200) !important;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1) !important;
}

.el-progress-bar__inner {
  border-radius: var(--radius-full) !important;
  background: var(--primary-gradient) !important;
  transition: all var(--transition-base) !important;
  position: relative;
  overflow: hidden;
}

.el-progress-bar__inner::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: progress-shine 2s infinite;
}

.el-progress__text {
  font-family: var(--font-sans) !important;
  font-weight: var(--font-weight-medium) !important;
  font-size: var(--text-sm) !important;
  color: var(--text-secondary) !important;
}

/* Steps Overrides */
.el-steps {
  font-family: var(--font-sans) !important;
}

.el-step__head {
  border-radius: var(--radius-full) !important;
}

.el-step__head.is-process {
  background: var(--primary-gradient) !important;
  border-color: var(--primary-600) !important;
  box-shadow: var(--shadow-sm) !important;
}

.el-step__head.is-finish {
  background: var(--success-500) !important;
  border-color: var(--success-600) !important;
}

.el-step__head.is-wait {
  background: var(--gray-200) !important;
  border-color: var(--gray-300) !important;
}

.el-step__title {
  font-weight: var(--font-weight-medium) !important;
  font-size: var(--text-sm) !important;
}

.el-step__title.is-process {
  color: var(--primary-600) !important;
}

.el-step__title.is-finish {
  color: var(--success-600) !important;
}

.el-step__description {
  font-size: var(--text-xs) !important;
  color: var(--text-tertiary) !important;
}

@keyframes progress-shine {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* Switch Overrides */
.el-switch {
  font-family: var(--font-sans) !important;
}

.el-switch__core {
  border-radius: var(--radius-full) !important;
  background: var(--gray-300) !important;
  border: 2px solid transparent !important;
  transition: all var(--transition-base) !important;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1) !important;
}

.el-switch.is-checked .el-switch__core {
  background: var(--primary-gradient) !important;
  box-shadow: var(--shadow-sm) !important;
}

.el-switch__action {
  background: var(--surface-color) !important;
  border-radius: var(--radius-full) !important;
  box-shadow: var(--shadow-sm) !important;
  transition: all var(--transition-base) !important;
}

.el-switch.is-checked .el-switch__action {
  box-shadow: var(--shadow-md) !important;
}

/* Slider Overrides */
.el-slider__runway {
  background: var(--gray-200) !important;
  border-radius: var(--radius-full) !important;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1) !important;
}

.el-slider__bar {
  background: var(--primary-gradient) !important;
  border-radius: var(--radius-full) !important;
}

.el-slider__button {
  background: var(--surface-color) !important;
  border: 3px solid var(--primary-500) !important;
  box-shadow: var(--shadow-md) !important;
  transition: all var(--transition-base) !important;
}

.el-slider__button:hover {
  transform: scale(1.1) !important;
  box-shadow: var(--shadow-lg) !important;
}

/* Radio Overrides */
.el-radio__input.is-checked .el-radio__inner {
  background: var(--primary-gradient) !important;
  border-color: var(--primary-600) !important;
  box-shadow: var(--shadow-sm) !important;
}

.el-radio__inner {
  border-color: var(--gray-300) !important;
  transition: all var(--transition-base) !important;
}

.el-radio__inner:hover {
  border-color: var(--primary-400) !important;
}

.el-radio__label {
  font-family: var(--font-sans) !important;
  font-size: var(--text-sm) !important;
  color: var(--text-primary) !important;
}

/* Checkbox Overrides */
.el-checkbox__input.is-checked .el-checkbox__inner {
  background: var(--primary-gradient) !important;
  border-color: var(--primary-600) !important;
  box-shadow: var(--shadow-sm) !important;
}

.el-checkbox__inner {
  border-color: var(--gray-300) !important;
  border-radius: var(--radius-sm) !important;
  transition: all var(--transition-base) !important;
}

.el-checkbox__inner:hover {
  border-color: var(--primary-400) !important;
}

.el-checkbox__label {
  font-family: var(--font-sans) !important;
  font-size: var(--text-sm) !important;
  color: var(--text-primary) !important;
}

/* Avatar Overrides */
.el-avatar {
  border: 2px solid var(--primary-gradient-start) !important;
  box-shadow: var(--shadow-soft) !important;
}

/* Dropdown Overrides */
.el-dropdown-menu {
  border-radius: var(--radius-lg) !important;
  box-shadow: var(--shadow-card) !important;
  border: none !important;
  padding: var(--space-2) !important;
}

.el-dropdown-menu__item {
  border-radius: var(--radius-md) !important;
  margin: 2px 0 !important;
  transition: all var(--transition-fast);
}

.el-dropdown-menu__item:hover {
  background: var(--background-color) !important;
  color: var(--text-primary) !important;
}

/* Table Overrides */
.el-table {
  border-radius: var(--radius-xl) !important;
  overflow: hidden;
  box-shadow: var(--shadow-sm) !important;
  border: 1px solid var(--gray-200) !important;
  background: var(--surface-color) !important;
}

.el-table th.el-table__cell {
  background: var(--gray-50) !important;
  color: var(--text-secondary) !important;
  font-weight: var(--font-weight-semibold) !important;
  border-bottom: 1px solid var(--gray-200) !important;
  font-size: var(--text-sm) !important;
  padding: var(--space-4) var(--space-3) !important;
}

.el-table td.el-table__cell {
  border-bottom: 1px solid var(--gray-100) !important;
  padding: var(--space-4) var(--space-3) !important;
  font-size: var(--text-sm) !important;
}

.el-table tr:hover > td {
  background: var(--primary-50) !important;
}

.el-table tbody tr:nth-child(even) > td {
  background: rgba(249, 250, 251, 0.5) !important;
}

.el-table tbody tr:nth-child(even):hover > td {
  background: var(--primary-50) !important;
}

/* Pagination Overrides */
.el-pagination {
  justify-content: center !important;
}

.el-pagination .el-pager li {
  border-radius: var(--radius-md) !important;
  margin: 0 2px !important;
  transition: all var(--transition-fast);
}

.el-pagination .el-pager li:hover {
  background: var(--background-color) !important;
}

.el-pagination .el-pager li.active {
  background: var(--primary-gradient) !important;
  color: var(--text-on-primary) !important;
}

/* 全局分页容器样式 */
.pagination,
.pagination-container {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: var(--space-6);
  padding: var(--space-4) 0;
}

/* 响应式分页 */
@media (max-width: 768px) {
  .pagination,
  .pagination-container {
    margin-top: var(--space-4);
    padding: var(--space-3) 0;
  }

  .el-pagination {
    flex-wrap: wrap;
    gap: var(--space-2);
  }

  .el-pagination .el-pagination__sizes,
  .el-pagination .el-pagination__total {
    order: -1;
    flex-basis: 100%;
    justify-content: center;
    margin-bottom: var(--space-2);
  }
}

/* Dialog Overrides */
.el-dialog {
  border-radius: var(--radius-2xl) !important;
  box-shadow: var(--shadow-xl) !important;
  border: 1px solid var(--gray-200) !important;
  backdrop-filter: blur(10px) !important;
  overflow: hidden !important;
}

.el-dialog__header {
  background: var(--gray-50) !important;
  border-bottom: 1px solid var(--gray-200) !important;
  padding: var(--space-6) !important;
  font-weight: var(--font-weight-semibold) !important;
  color: var(--text-primary) !important;
}

.el-dialog__body {
  padding: var(--space-6) !important;
  background: var(--surface-color) !important;
}

.el-dialog__headerbtn {
  top: var(--space-4) !important;
  right: var(--space-4) !important;
  width: 32px !important;
  height: 32px !important;
  border-radius: var(--radius-lg) !important;
  background: var(--gray-100) !important;
  transition: all var(--transition-base) !important;
}

.el-dialog__headerbtn:hover {
  background: var(--gray-200) !important;
  transform: scale(1.05) !important;
}

/* Message Overrides */
.el-message {
  border-radius: var(--radius-xl) !important;
  box-shadow: var(--shadow-lg) !important;
  border: 1px solid var(--gray-200) !important;
  font-weight: var(--font-weight-medium) !important;
  font-family: var(--font-sans) !important;
  backdrop-filter: blur(10px) !important;
  padding: var(--space-4) var(--space-6) !important;
  z-index: 10000 !important;
}

.el-message--success {
  background: var(--success-50) !important;
  border-color: var(--success-200) !important;
  color: var(--success-600) !important;
}

.el-message--warning {
  background: var(--warning-50) !important;
  border-color: var(--warning-200) !important;
  color: var(--warning-600) !important;
}

.el-message--error {
  background: var(--error-50) !important;
  border-color: var(--error-200) !important;
  color: var(--error-600) !important;
}

.el-message--info {
  background: var(--info-50) !important;
  border-color: var(--info-200) !important;
  color: var(--info-600) !important;
}

.el-message--success {
  background: linear-gradient(135deg, #4ade80 0%, #22c55e 100%) !important;
  color: #ffffff !important;
}

.el-message--success .el-message__content {
  color: #ffffff !important;
}

.el-message--error {
  background: linear-gradient(135deg, #f87171 0%, #ef4444 100%) !important;
  color: #ffffff !important;
}

.el-message--error .el-message__content {
  color: #ffffff !important;
}

.el-message--warning {
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%) !important;
  color: #ffffff !important;
}

.el-message--warning .el-message__content {
  color: #ffffff !important;
}

.el-message--info {
  background: var(--primary-gradient) !important;
  color: #ffffff !important;
}

.el-message--info .el-message__content {
  color: #ffffff !important;
}

/* Notification Overrides */
.el-notification {
  border-radius: var(--radius-lg) !important;
  box-shadow: var(--shadow-card) !important;
  border: none !important;
  font-family: var(--font-family) !important;
  z-index: 10000 !important;
}

.el-notification--success {
  background: linear-gradient(135deg, #4ade80 0%, #22c55e 100%) !important;
  color: #ffffff !important;
}

.el-notification--success .el-notification__title,
.el-notification--success .el-notification__content {
  color: #ffffff !important;
}

.el-notification--error {
  background: linear-gradient(135deg, #f87171 0%, #ef4444 100%) !important;
  color: #ffffff !important;
}

.el-notification--error .el-notification__title,
.el-notification--error .el-notification__content {
  color: #ffffff !important;
}

.el-notification--warning {
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%) !important;
  color: #ffffff !important;
}

.el-notification--warning .el-notification__title,
.el-notification--warning .el-notification__content {
  color: #ffffff !important;
}

.el-notification--info {
  background: var(--primary-gradient) !important;
  color: #ffffff !important;
}

.el-notification--info .el-notification__title,
.el-notification--info .el-notification__content {
  color: #ffffff !important;
}

/* MessageBox (确认框) Overrides */
.el-message-box {
  border-radius: var(--radius-xl) !important;
  box-shadow: var(--shadow-card) !important;
  border: none !important;
  font-family: var(--font-family) !important;
}

.el-message-box__title {
  color: var(--text-primary) !important;
  font-weight: var(--font-weight-semibold) !important;
}

.el-message-box__content {
  color: var(--text-secondary) !important;
}

.el-message-box__message {
  color: var(--text-primary) !important;
}

/* Popconfirm Overrides */
.el-popconfirm__main {
  color: var(--text-primary) !important;
}

/* Tooltip Overrides */
.el-tooltip__popper {
  background: var(--text-primary) !important;
  color: #ffffff !important;
  border-radius: var(--radius-md) !important;
  box-shadow: var(--shadow-card) !important;
}

.el-tooltip__popper .el-popper__arrow::before {
  background: var(--text-primary) !important;
  border: none !important;
}

/* 确保所有弹出框文字颜色正确 */
.el-message .el-message__icon,
.el-notification .el-notification__icon {
  color: #ffffff !important;
}

/* Loading 组件样式 */
.el-loading-mask {
  background-color: rgba(255, 255, 255, 0.9) !important;
}

.el-loading-spinner .el-loading-text {
  color: var(--text-primary) !important;
}

/* Drawer 抽屉组件 */
.el-drawer {
  border-radius: var(--radius-xl) 0 0 var(--radius-xl) !important;
  box-shadow: var(--shadow-card) !important;
}

.el-drawer__header {
  border-bottom: 1px solid rgba(0, 0, 0, 0.05) !important;
  padding: var(--space-6) !important;
}

.el-drawer__title {
  color: var(--text-primary) !important;
  font-weight: var(--font-weight-semibold) !important;
}

.el-drawer__body {
  padding: var(--space-6) !important;
}

/* Popover 弹出框 */
.el-popover {
  border-radius: var(--radius-lg) !important;
  box-shadow: var(--shadow-card) !important;
  border: none !important;
  background: var(--surface-color) !important;
}

.el-popover .el-popover__title {
  color: var(--text-primary) !important;
}

.el-popover .el-popover__reference {
  color: var(--text-secondary) !important;
}

/* 确保深色模式下的文字可见性 */
@media (prefers-color-scheme: dark) {
  .el-message--success,
  .el-message--error,
  .el-message--warning,
  .el-message--info,
  .el-notification--success,
  .el-notification--error,
  .el-notification--warning,
  .el-notification--info {
    color: #ffffff !important;
  }

  .el-message--success .el-message__content,
  .el-message--error .el-message__content,
  .el-message--warning .el-message__content,
  .el-message--info .el-message__content,
  .el-notification--success .el-notification__title,
  .el-notification--success .el-notification__content,
  .el-notification--error .el-notification__title,
  .el-notification--error .el-notification__content,
  .el-notification--warning .el-notification__title,
  .el-notification--warning .el-notification__content,
  .el-notification--info .el-notification__title,
  .el-notification--info .el-notification__content {
    color: #ffffff !important;
  }
}
