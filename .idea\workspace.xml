<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="e1506fd0-acd6-4654-8896-3a1dc1f1f16a" name="更改" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 0
}</component>
  <component name="ProjectId" id="2zOdOC4anBZYAXOIeZjN4cxeLXz" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Maven.demo [clean].executor": "Run",
    "Maven.demo [compile].executor": "Run",
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "Spring Boot.DemoApplication.executor": "Run",
    "ignore.virus.scanning.warn.message": "true",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "D:/workspace/idea/worker",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "npm.serve.executor": "Run",
    "project.structure.last.edited": "项目",
    "project.structure.proportion": "0.0",
    "project.structure.side.proportion": "0.0",
    "settings.editor.selected.configurable": "preferences.pluginManager",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RunManager" selected="npm.serve">
    <configuration name="DemoApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="demo" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.example.demo.DemoApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="serve" type="js.build_tools.npm" temporary="true" nameIsGenerated="true">
      <package-json value="$PROJECT_DIR$/work_cli/package.json" />
      <command value="run" />
      <scripts>
        <script value="serve" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="npm.serve" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-28b599e66164-intellij.indexing.shared.core-IU-242.23726.103" />
        <option value="bundled-js-predefined-d6986cc7102b-5c90d61e3bab-JavaScript-IU-242.23726.103" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="e1506fd0-acd6-4654-8896-3a1dc1f1f16a" name="更改" comment="" />
      <created>1751606018613</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1751606018613</updated>
      <workItem from="1751606019643" duration="666000" />
      <workItem from="1751627272929" duration="12203000" />
      <workItem from="1751714554781" duration="87000" />
      <workItem from="1751714652123" duration="1898000" />
      <workItem from="1751720441822" duration="3717000" />
      <workItem from="1751766626589" duration="2506000" />
      <workItem from="1751782886330" duration="19301000" />
      <workItem from="1752112246735" duration="3737000" />
      <workItem from="1752150556508" duration="28172000" />
      <workItem from="1752368380827" duration="8321000" />
      <workItem from="1752394229672" duration="7579000" />
      <workItem from="1752456450885" duration="2429000" />
      <workItem from="1752482088743" duration="7921000" />
      <workItem from="1752548920660" duration="30309000" />
      <workItem from="1752757816686" duration="4256000" />
      <workItem from="1752818340502" duration="14969000" />
      <workItem from="1752913139404" duration="14746000" />
      <workItem from="1752997599801" duration="3179000" />
      <workItem from="1753005612683" duration="2052000" />
      <workItem from="1753083427144" duration="6945000" />
      <workItem from="1753265041101" duration="35000" />
      <workItem from="1753267785003" duration="592000" />
      <workItem from="1753275291774" duration="3691000" />
      <workItem from="1753582688915" duration="826000" />
      <workItem from="1753678247039" duration="3340000" />
      <workItem from="1753787144344" duration="9121000" />
      <workItem from="1753848151814" duration="1241000" />
      <workItem from="1753869768640" duration="2352000" />
      <workItem from="1754039481570" duration="1044000" />
      <workItem from="1754052887134" duration="43000" />
      <workItem from="1754104583845" duration="1737000" />
      <workItem from="1754106630023" duration="3081000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>