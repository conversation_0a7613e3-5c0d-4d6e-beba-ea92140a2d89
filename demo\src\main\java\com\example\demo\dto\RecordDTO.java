package com.example.demo.dto;

import com.example.demo.entity.Record;
import com.example.demo.entity.User;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 记录展示DTO
 */
@Data
public class RecordDTO {
    
    private Long id;
    private Record.RecordType type;
    private String subType;
    private String title;
    private String content;
    private Record.RecordStatus status;
    private Integer priority;
    private LocalDateTime dueDate;
    private String attachments;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
    
    // 关联信息
    private Long projectId;
    private String projectName;
    private Long teamId;
    private String teamName;
    private Long parentId;
    
    // 创建者信息
    private UserInfo creator;
    
    // 回复列表（仅讨论类型）
    private List<RecordDTO> replies;
    
    // 回复数量
    private Integer replyCount;
    
    // 是否已完成（仅任务类型）
    private Boolean completed;
    
    // 是否逾期（仅任务类型）
    private Boolean overdue;
    
    /**
     * 用户信息内部类
     */
    @Data
    public static class UserInfo {
        private Long id;
        private String username;
        private String realName;
        private String avatar;
        private User.UserRole role;
        
        public static UserInfo fromUser(User user) {
            UserInfo info = new UserInfo();
            info.setId(user.getId());
            info.setUsername(user.getUsername());
            info.setRealName(user.getRealName());
            info.setAvatar(user.getAvatar());
            info.setRole(user.getRole());
            return info;
        }
    }
    
    /**
     * 从Record实体创建DTO
     */
    public static RecordDTO fromRecord(Record record) {
        RecordDTO dto = new RecordDTO();
        dto.setId(record.getId());
        dto.setType(record.getType());
        dto.setSubType(record.getSubType());
        dto.setTitle(record.getTitle());
        dto.setContent(record.getContent());
        dto.setStatus(record.getStatus());
        dto.setPriority(convertPriorityToInteger(record.getPriority()));
        dto.setDueDate(record.getDueDate());
        dto.setAttachments(record.getAttachments());
        dto.setCreateTime(record.getCreateTime());
        dto.setUpdateTime(record.getUpdateTime());
        dto.setParentId(record.getParentId());
        
        if (record.getProject() != null) {
            dto.setProjectId(record.getProject().getId());
            dto.setProjectName(record.getProject().getName());
        }
        
        if (record.getTeam() != null) {
            dto.setTeamId(record.getTeam().getId());
            dto.setTeamName(record.getTeam().getName());
        }
        
        if (record.getUser() != null) {
            dto.setCreator(UserInfo.fromUser(record.getUser()));
        }
        
        // 计算任务状态
        if (Record.RecordType.TASK.equals(record.getType())) {
            dto.setCompleted(false); // 这里需要根据实际业务逻辑设置
            dto.setOverdue(record.getDueDate() != null && 
                          record.getDueDate().isBefore(LocalDateTime.now()));
        }
        
        return dto;
    }
    
    /**
     * 从Record实体创建DTO（包含回复）
     */
    public static RecordDTO fromRecord(Record record, List<RecordDTO> replies) {
        RecordDTO dto = fromRecord(record);
        dto.setReplies(replies);
        dto.setReplyCount(replies != null ? replies.size() : 0);
        return dto;
    }

    /**
     * 将Priority枚举转换为Integer类型
     */
    private static Integer convertPriorityToInteger(Record.Priority priority) {
        if (priority == null) {
            return 2; // 默认中等优先级
        }

        switch (priority) {
            case LOW:
                return 1;
            case MEDIUM:
                return 2;
            case HIGH:
                return 3;
            case URGENT:
                return 4;
            default:
                return 2;
        }
    }
}
