package com.example.demo.service;

import com.example.demo.common.PageResult;
import com.example.demo.dto.RecordCreateRequest;
import com.example.demo.dto.RecordDTO;
import com.example.demo.dto.RecordUpdateRequest;
import com.example.demo.entity.Record;
import org.springframework.data.domain.Pageable;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 记录服务接口
 */
public interface RecordService {
    
    /**
     * 创建记录
     */
    Record createRecord(Long userId, RecordCreateRequest request);
    
    /**
     * 更新记录
     */
    Record updateRecord(Long recordId, Long userId, RecordUpdateRequest request);
    
    /**
     * 根据ID查找记录
     */
    Record findById(Long id);
    
    /**
     * 获取记录DTO
     */
    RecordDTO getRecordDTO(Long recordId);
    
    /**
     * 删除记录
     */
    void deleteRecord(Long recordId, Long userId);
    
    /**
     * 创建任务
     */
    Record createTask(Long userId, Long projectId, Long teamId, String title, String content, 
                     Integer priority, LocalDateTime dueDate);
    
    /**
     * 创建讨论
     */
    Record createDiscussion(Long userId, Long projectId, Long teamId, String title, String content);

    /**
     * 创建讨论（带子类型）
     */
    Record createDiscussion(Long userId, Long projectId, Long teamId, String title, String content, String subType);
    
    /**
     * 回复讨论
     */
    Record replyDiscussion(Long userId, Long parentId, String content);

    /**
     * 查找用户对特定任务的提交记录
     */
    Record findSubmissionByTaskAndUser(Long taskId, Long userId);
    
    /**
     * 创建提交
     */
    Record createSubmission(Long userId, Long projectId, Long teamId, String title, String content, 
                           String attachments);
    
    /**
     * 创建评价
     */
    Record createEvaluation(Long userId, Long projectId, Long teamId, String title, String content, 
                           Integer priority);
    
    /**
     * 创建公告
     */
    Record createAnnouncement(Long userId, Long projectId, String title, String content, 
                             Integer priority);
    
    /**
     * 分页查询所有记录
     */
    PageResult<RecordDTO> findAllRecords(Pageable pageable);
    
    /**
     * 分页查询项目记录
     */
    PageResult<RecordDTO> findProjectRecords(Long projectId, Record.RecordType type, Pageable pageable);
    
    /**
     * 分页查询团队记录
     */
    PageResult<RecordDTO> findTeamRecords(Long teamId, Record.RecordType type, Pageable pageable);
    
    /**
     * 分页查询用户记录
     */
    PageResult<RecordDTO> findUserRecords(Long userId, Record.RecordType type, Pageable pageable);
    
    /**
     * 分页查询项目任务
     */
    PageResult<RecordDTO> findProjectTasks(Long projectId, Pageable pageable);
    
    /**
     * 分页查询团队任务
     */
    PageResult<RecordDTO> findTeamTasks(Long teamId, Pageable pageable);
    
    /**
     * 分页查询用户任务
     */
    PageResult<RecordDTO> findUserTasks(Long userId, Pageable pageable);
    
    /**
     * 分页查询项目讨论
     */
    PageResult<RecordDTO> findProjectDiscussions(Long projectId, Pageable pageable);
    
    /**
     * 分页查询团队讨论
     */
    PageResult<RecordDTO> findTeamDiscussions(Long teamId, Pageable pageable);
    
    /**
     * 分页查询项目提交
     */
    PageResult<RecordDTO> findProjectSubmissions(Long projectId, Pageable pageable);
    
    /**
     * 分页查询团队提交
     */
    PageResult<RecordDTO> findTeamSubmissions(Long teamId, Pageable pageable);
    
    /**
     * 分页查询项目评价
     */
    PageResult<RecordDTO> findProjectEvaluations(Long projectId, Pageable pageable);
    
    /**
     * 获取讨论回复
     */
    List<RecordDTO> getDiscussionReplies(Long discussionId);
    
    /**
     * 搜索记录
     */
    PageResult<RecordDTO> searchRecords(String keyword, Record.RecordType type, Pageable pageable);
    
    /**
     * 获取即将到期的任务
     */
    List<RecordDTO> getTasksDueSoon(Long userId, int days);
    
    /**
     * 获取逾期任务
     */
    List<RecordDTO> getOverdueTasks(Long userId);
    
    /**
     * 标记任务完成
     */
    void markTaskCompleted(Long taskId, Long userId);
    
    /**
     * 标记任务未完成
     */
    void markTaskIncomplete(Long taskId, Long userId);
    
    /**
     * 检查用户是否可以编辑记录
     */
    boolean canEditRecord(Long recordId, Long userId);
    
    /**
     * 检查用户是否可以删除记录
     */
    boolean canDeleteRecord(Long recordId, Long userId);
    
    /**
     * 统计记录数量
     */
    long countRecords();
    
    /**
     * 统计各类型记录数量
     */
    List<Object[]> countRecordsByType();

    /**
     * 统计指定类型的记录数量
     */
    long countRecordsByType(String type);

    /**
     * 统计指定类型和状态的记录数量
     */
    long countRecordsByTypeAndStatus(String type, String status);

    /**
     * 统计指定项目列表的任务数量
     */
    long countTasksByProjects(List<Long> projectIds);

    /**
     * 统计指定项目列表的已完成任务数量
     */
    long countCompletedTasksByProjects(List<Long> projectIds);

    /**
     * 统计用户创建的任务数量
     */
    long countTasksByCreator(Long userId);

    /**
     * 统计教师待审核的任务提交数量
     */
    long countPendingTaskSubmissionsByTeacher(Long teacherId);

    /**
     * 统计指定项目的任务数量
     */
    long countTasksByProject(Long projectId);

    /**
     * 统计指定项目中已完成的任务数量
     */
    long countCompletedTasksByProject(Long projectId);

    /**
     * 统计指定团队的任务数量
     */
    long countTasksByTeam(Long teamId);

    /**
     * 统计指定团队中已完成的任务数量
     */
    long countCompletedTasksByTeam(Long teamId);

    /**
     * 统计用户在指定项目中完成的任务数量
     */
    long countCompletedTasksByUserAndProject(Long userId, Long projectId);

    /**
     * 统计用户的提交数量
     */
    long countSubmissionsByUser(Long userId);

    /**
     * 统计用户在指定项目中的提交数量
     */
    long countSubmissionsByUserAndProject(Long userId, Long projectId);

    /**
     * 统计用户发起的讨论数量
     */
    long countDiscussionsByUser(Long userId);

    /**
     * 统计用户记录数量
     */
    Object[] getUserRecordStats(Long userId);
    
    /**
     * 统计项目记录数量
     */
    Object[] getProjectRecordStats(Long projectId);
    
    /**
     * 统计团队记录数量
     */
    Object[] getTeamRecordStats(Long teamId);
}
