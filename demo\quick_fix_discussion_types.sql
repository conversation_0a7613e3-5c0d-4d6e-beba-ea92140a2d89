-- 快速修复讨论类型显示问题
-- 可以直接在数据库中执行

-- 1. 添加sub_type字段（如果不存在）
ALTER TABLE records ADD COLUMN IF NOT EXISTS sub_type VARCHAR(50) NULL COMMENT '记录子类型（用于讨论分类）';

-- 2. 为所有现有的讨论记录设置默认子类型
UPDATE records 
SET sub_type = 'DISCUSSION' 
WHERE type = 'DISCUSSION' 
AND (sub_type IS NULL OR sub_type = '' OR sub_type = 'null');

-- 3. 为公告类型记录设置子类型
UPDATE records 
SET sub_type = 'ANNOUNCEMENT' 
WHERE type = 'ANNOUNCEMENT' 
AND (sub_type IS NULL OR sub_type = '' OR sub_type = 'null');

-- 4. 验证修复结果
SELECT 
    id,
    type,
    sub_type,
    title,
    create_time
FROM records 
WHERE type IN ('DISCUSSION', 'ANNOUNCEMENT')
ORDER BY create_time DESC 
LIMIT 5;

-- 5. 统计修复后的数据
SELECT 
    type,
    sub_type,
    COUNT(*) as count
FROM records 
WHERE type IN ('DISCUSSION', 'ANNOUNCEMENT')
GROUP BY type, sub_type
ORDER BY type, sub_type;
