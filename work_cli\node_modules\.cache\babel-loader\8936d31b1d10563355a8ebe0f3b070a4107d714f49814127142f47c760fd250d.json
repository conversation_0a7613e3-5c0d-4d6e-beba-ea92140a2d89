{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { ref, reactive, onMounted, computed } from 'vue';\nimport { useStore } from 'vuex';\nimport { recordAPI, teamAPI, projectAPI } from '@/api';\nimport { ElMessage, ElMessageBox } from 'element-plus';\nimport { getAvatarUrl, getInitial } from '@/utils/avatar';\nimport { Document, ChatDotRound, Edit, Delete, Plus, Loading, Refresh, Clock } from '@element-plus/icons-vue';\nexport default {\n  name: 'DiscussionView',\n  components: {\n    Document,\n    ChatDotRound,\n    Edit,\n    Delete,\n    Plus,\n    Loading,\n    Refresh,\n    Clock\n  },\n  setup() {\n    const store = useStore();\n    const formRef = ref();\n    const replyFormRef = ref();\n    const loading = ref(false);\n    const submitting = ref(false);\n    const showCreateDialog = ref(false);\n    const showDetailDialog = ref(false);\n    const showReplyDialog = ref(false);\n    const editingDiscussion = ref(null);\n    const currentDiscussion = ref(null);\n    const replyingToDiscussion = ref(null);\n    const discussionReplies = ref([]);\n    const loadingReplies = ref(false);\n    const discussions = ref([]);\n    const myTeams = ref([]);\n    const currentTeam = ref(null);\n    const currentTeamId = ref(null);\n    const currentPage = ref(1);\n    const pageSize = ref(10);\n    const total = ref(0);\n    const discussionForm = reactive({\n      type: 'DISCUSSION',\n      title: '',\n      content: ''\n    });\n    const replyForm = reactive({\n      content: ''\n    });\n    const formRules = {\n      type: [{\n        required: true,\n        message: '请选择讨论类型',\n        trigger: 'change'\n      }],\n      title: [{\n        required: true,\n        message: '请输入讨论标题',\n        trigger: 'blur'\n      }, {\n        min: 2,\n        max: 100,\n        message: '标题长度在 2 到 100 个字符',\n        trigger: 'blur'\n      }],\n      content: [{\n        required: true,\n        message: '请输入讨论内容',\n        trigger: 'blur'\n      }, {\n        min: 10,\n        max: 2000,\n        message: '内容长度在 10 到 2000 个字符',\n        trigger: 'blur'\n      }]\n    };\n    const replyRules = {\n      content: [{\n        required: true,\n        message: '请输入回复内容',\n        trigger: 'blur'\n      }, {\n        min: 5,\n        max: 1000,\n        message: '回复内容长度在 5 到 1000 个字符',\n        trigger: 'blur'\n      }]\n    };\n\n    // 使用与DashboardView相同的方式获取用户信息\n    const currentUser = computed(() => store.getters.currentUser);\n    const isTeacher = computed(() => store.getters.isTeacher);\n    const isStudent = computed(() => store.getters.isStudent);\n\n    // 加载我的团队\n    const loadMyTeams = async () => {\n      try {\n        if (isTeacher.value) {\n          // 首先获取教师发布的项目\n          const projectsResponse = await projectAPI.getMyProjects();\n          const myProjects = projectsResponse?.records || [];\n          if (myProjects.length > 0) {\n            // 获取所有项目相关的团队\n            const allTeams = [];\n            for (const project of myProjects) {\n              try {\n                const teamsResponse = await teamAPI.getProjectTeams(project.id);\n                if (teamsResponse?.records) {\n                  // 为每个团队添加项目信息\n                  const teamsWithProject = teamsResponse.records.map(team => ({\n                    ...team,\n                    projectId: project.id,\n                    projectName: project.name\n                  }));\n                  allTeams.push(...teamsWithProject);\n                }\n              } catch (err) {\n                console.warn(`获取项目 ${project.id} 的团队失败:`, err);\n              }\n            }\n            if (allTeams.length > 0) {\n              myTeams.value = allTeams;\n              currentTeamId.value = allTeams[0].id;\n              await loadDiscussions();\n            } else {\n              ElMessage.info('暂无团队申请您发布的项目');\n              myTeams.value = [];\n            }\n          } else {\n            ElMessage.info('您还没有发布任何项目，无法查看团队讨论');\n            myTeams.value = [];\n          }\n        } else {\n          // 使用真实API获取学生的团队\n          const response = await teamAPI.getMyTeam();\n          if (response) {\n            currentTeam.value = response;\n            currentTeamId.value = response.id;\n            myTeams.value = [response]; // 学生只有一个团队\n            await loadDiscussions();\n          } else {\n            ElMessage.warning('您还没有加入任何团队');\n            myTeams.value = [];\n          }\n        }\n      } catch (error) {\n        console.error('加载团队信息失败:', error);\n        ElMessage.error(`加载团队信息失败: ${error.message || '未知错误'}`);\n        myTeams.value = [];\n      }\n    };\n\n    // 加载讨论列表\n    const loadDiscussions = async () => {\n      if (!currentTeamId.value) {\n        return;\n      }\n      try {\n        loading.value = true;\n\n        // 使用专门的团队讨论相关记录API，包括讨论、公告、通知\n        const params = {\n          page: currentPage.value,\n          size: pageSize.value,\n          sortBy: 'createTime',\n          sortDir: 'desc'\n        };\n        const response = await recordAPI.getTeamDiscussionRelated(currentTeamId.value, params);\n        console.log('团队讨论相关记录原始响应:', response);\n        if (response && response.records) {\n          console.log('记录数据示例:', response.records[0]);\n\n          // 不需要前端过滤，后端已经返回了正确的记录类型\n          const filteredRecords = response.records;\n\n          // 处理用户信息显示\n          const recordsWithUserInfo = filteredRecords.map(record => {\n            // 从creator对象中获取用户信息\n            let userName = '未知用户';\n            let userRole = 'STUDENT';\n            let userId = null;\n            if (record.creator) {\n              userName = record.creator.realName || record.creator.username || '未知用户';\n              userRole = record.creator.role || 'STUDENT';\n              userId = record.creator.id;\n            }\n            console.log('处理记录用户信息:', {\n              recordId: record.id,\n              creator: record.creator,\n              最终结果: {\n                userName: userName,\n                userRole: userRole,\n                userId: userId\n              }\n            });\n            return {\n              ...record,\n              userName: userName,\n              userRole: userRole,\n              userId: userId,\n              userAvatar: record.creator?.avatar || null\n            };\n          });\n          discussions.value = recordsWithUserInfo.map(record => ({\n            id: record.id,\n            title: record.title || '无标题',\n            content: record.content || '',\n            type: record.type || 'DISCUSSION',\n            userName: record.userName,\n            userRole: record.userRole,\n            userId: record.userId,\n            userAvatar: record.userAvatar,\n            // 确保头像数据被传递\n            createTime: record.createTime,\n            updateTime: record.updateTime,\n            attachments: record.attachments || []\n          }));\n\n          // 使用后端返回的准确总数（后端已经过滤了正确的记录类型）\n          total.value = response.total || 0;\n        } else {\n          discussions.value = [];\n          total.value = 0;\n        }\n        loading.value = false;\n      } catch (error) {\n        console.error('加载讨论列表失败:', error);\n        ElMessage.error('加载讨论列表失败');\n        loading.value = false;\n        discussions.value = [];\n        total.value = 0;\n      }\n    };\n\n    // 提交讨论\n    const submitDiscussion = async () => {\n      if (!formRef.value) return;\n\n      // 检查必要的数据\n      if (!currentTeamId.value) {\n        ElMessage.error('请先选择团队');\n        return;\n      }\n      try {\n        await formRef.value.validate();\n        submitting.value = true;\n\n        // 准备提交的数据\n        const recordData = {\n          title: discussionForm.title,\n          content: discussionForm.content,\n          type: discussionForm.type,\n          // 直接使用用户选择的类型\n          teamId: currentTeamId.value,\n          projectId: currentTeam.value?.projectId || null\n        };\n        console.log('提交讨论数据:', recordData);\n        console.log('当前用户信息:', currentUser.value);\n        console.log('localStorage token:', localStorage.getItem('token'));\n        if (editingDiscussion.value) {\n          // 更新现有讨论\n          console.log('更新讨论，ID:', editingDiscussion.value.id);\n          const response = await recordAPI.updateRecord(editingDiscussion.value.id, recordData);\n          console.log('更新讨论响应:', response);\n          ElMessage.success('讨论更新成功');\n          // 重新加载讨论列表\n          await loadDiscussions();\n        } else {\n          // 创建新讨论\n          console.log('创建新讨论');\n          const response = await recordAPI.createRecord(recordData);\n          console.log('创建讨论响应:', response);\n          ElMessage.success('讨论发布成功');\n          // 重新加载讨论列表\n          await loadDiscussions();\n        }\n        showCreateDialog.value = false;\n        resetForm();\n        submitting.value = false;\n      } catch (error) {\n        console.error('提交讨论失败:', error);\n        ElMessage.error(`提交讨论失败: ${error.response?.data?.message || error.message || '未知错误'}`);\n        submitting.value = false;\n      }\n    };\n\n    // 重置表单\n    const resetForm = () => {\n      Object.assign(discussionForm, {\n        type: '',\n        title: '',\n        content: ''\n      });\n      editingDiscussion.value = null;\n    };\n\n    // 编辑讨论\n    const editDiscussion = discussion => {\n      editingDiscussion.value = discussion;\n      Object.assign(discussionForm, {\n        type: discussion.type,\n        title: discussion.title,\n        content: discussion.content\n      });\n      showCreateDialog.value = true;\n    };\n\n    // 删除讨论\n    const deleteDiscussion = async id => {\n      try {\n        await ElMessageBox.confirm('确定要删除这条讨论吗？', '确认删除', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        });\n\n        // 使用真实API删除讨论\n        const response = await recordAPI.deleteRecord(id);\n        if (response) {\n          ElMessage.success('讨论删除成功');\n          // 重新加载讨论列表\n          await loadDiscussions();\n        }\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('删除讨论失败:', error);\n          ElMessage.error('删除讨论失败');\n        }\n      }\n    };\n\n    // 回复讨论\n    const replyToDiscussion = discussion => {\n      replyingToDiscussion.value = discussion;\n      replyForm.content = '';\n      showReplyDialog.value = true;\n    };\n\n    // 提交回复\n    const submitReply = async () => {\n      if (!replyFormRef.value) return;\n\n      // 检查必要的数据\n      if (!currentTeamId.value) {\n        ElMessage.error('请先选择团队');\n        return;\n      }\n      if (!replyingToDiscussion.value) {\n        ElMessage.error('回复目标不存在');\n        return;\n      }\n      try {\n        await replyFormRef.value.validate();\n      } catch (error) {\n        return;\n      }\n      try {\n        submitting.value = true;\n\n        // 准备回复数据\n        const replyData = {\n          title: `回复：${replyingToDiscussion.value.title}`,\n          content: replyForm.content,\n          type: 'DISCUSSION',\n          // 使用DISCUSSION类型而不是REPLY\n          parentId: replyingToDiscussion.value.id,\n          // 父讨论ID\n          // 不绑定teamId，这样回复只会在项目详情界面显示，不会在项目讨论界面显示\n          projectId: currentTeam.value?.projectId || null\n        };\n        console.log('提交回复数据:', replyData);\n        console.log('当前用户信息:', currentUser.value);\n        console.log('localStorage token:', localStorage.getItem('token'));\n        const response = await recordAPI.createRecord(replyData);\n        console.log('回复响应:', response);\n        ElMessage.success('回复发布成功');\n        showReplyDialog.value = false;\n        replyForm.content = '';\n\n        // 如果详情对话框是打开的，重新加载回复\n        if (showDetailDialog.value && currentDiscussion.value) {\n          await loadDiscussionReplies(currentDiscussion.value.id);\n        }\n        replyingToDiscussion.value = null;\n        // 重新加载讨论列表\n        await loadDiscussions();\n        submitting.value = false;\n      } catch (error) {\n        console.error('提交回复失败:', error);\n        ElMessage.error(`提交回复失败: ${error.response?.data?.message || error.message || '未知错误'}`);\n        submitting.value = false;\n      }\n    };\n\n    // 加载讨论回复\n    const loadDiscussionReplies = async discussionId => {\n      try {\n        loadingReplies.value = true;\n        console.log('开始加载讨论回复，讨论ID:', discussionId);\n        console.log('请求URL将是:', `/api/records/${discussionId}/replies`);\n        const response = await recordAPI.getDiscussionReplies(discussionId);\n        console.log('回复API响应:', response);\n\n        // 检查响应结构\n        console.log('响应结构检查:', {\n          response: response,\n          responseType: typeof response,\n          isArray: Array.isArray(response),\n          responseLength: Array.isArray(response) ? response.length : 'not array'\n        });\n        if (response && Array.isArray(response)) {\n          // 处理回复数据，确保用户信息正确显示\n          discussionReplies.value = response.map(reply => ({\n            ...reply,\n            userName: reply.creator?.realName || reply.creator?.username || '未知用户',\n            userRole: reply.creator?.role || 'STUDENT',\n            userId: reply.creator?.id,\n            userAvatar: reply.creator?.avatar || null\n          }));\n          console.log('处理后的回复数据:', discussionReplies.value);\n        } else if (response === null || response === undefined) {\n          console.log('API返回null/undefined数据，可能没有回复');\n          discussionReplies.value = [];\n        } else {\n          console.log('API返回空数据或格式不正确，响应:', response);\n          discussionReplies.value = [];\n        }\n        loadingReplies.value = false;\n      } catch (error) {\n        console.error('加载回复失败:', error);\n        console.error('错误详情:', {\n          message: error.message,\n          response: error.response?.data,\n          status: error.response?.status,\n          url: error.config?.url\n        });\n        ElMessage.error(`加载回复失败: ${error.response?.data?.message || error.message || '未知错误'}`);\n        loadingReplies.value = false;\n        discussionReplies.value = [];\n      }\n    };\n\n    // 查看讨论详情\n    const viewDiscussionDetail = async discussion => {\n      currentDiscussion.value = discussion;\n      showDetailDialog.value = true;\n\n      // 先清空之前的回复数据\n      discussionReplies.value = [];\n\n      // 加载该讨论的回复\n      console.log('准备加载讨论回复，讨论对象:', {\n        id: discussion.id,\n        title: discussion.title,\n        type: discussion.type,\n        parentId: discussion.parentId\n      });\n      await loadDiscussionReplies(discussion.id);\n    };\n\n    // 权限检查\n    const canEdit = discussion => {\n      return discussion.userId === currentUser.value?.id;\n    };\n    const canDelete = discussion => {\n      return discussion.userId === currentUser.value?.id || currentUser.value?.role === 'TEACHER';\n    };\n\n    // 工具方法\n    const formatDate = date => {\n      if (!date) return '';\n      return new Date(date).toLocaleString('zh-CN');\n    };\n    const getTypeColor = type => {\n      const colorMap = {\n        'DISCUSSION': 'primary',\n        'ANNOUNCEMENT': 'danger',\n        'NOTIFICATION': 'info',\n        'TASK': 'warning',\n        'SUBMISSION': 'success',\n        'EVALUATION': 'info'\n      };\n      return colorMap[type] || '';\n    };\n    const getTypeText = type => {\n      const textMap = {\n        'DISCUSSION': '一般讨论',\n        'ANNOUNCEMENT': '公告通知',\n        'NOTIFICATION': '系统通知',\n        'TASK': '任务',\n        'SUBMISSION': '提交',\n        'EVALUATION': '评价'\n      };\n      return textMap[type] || type;\n    };\n    onMounted(() => {\n      console.log('组件挂载时的用户信息:', {\n        currentUser: currentUser.value,\n        token: localStorage.getItem('token'),\n        storeUser: store.state.user,\n        isAuthenticated: store.state.isAuthenticated\n      });\n      loadMyTeams();\n    });\n    return {\n      loading,\n      submitting,\n      showCreateDialog,\n      showDetailDialog,\n      showReplyDialog,\n      editingDiscussion,\n      currentDiscussion,\n      replyingToDiscussion,\n      discussionReplies,\n      loadingReplies,\n      discussions,\n      myTeams,\n      currentTeam,\n      currentTeamId,\n      currentPage,\n      pageSize,\n      total,\n      discussionForm,\n      replyForm,\n      formRules,\n      replyRules,\n      formRef,\n      replyFormRef,\n      currentUser,\n      isTeacher,\n      isStudent,\n      store,\n      loadDiscussions,\n      loadDiscussionReplies,\n      submitDiscussion,\n      submitReply,\n      editDiscussion,\n      deleteDiscussion,\n      replyToDiscussion,\n      viewDiscussionDetail,\n      canEdit,\n      canDelete,\n      formatDate,\n      getTypeColor,\n      getTypeText,\n      // 头像工具函数\n      getAvatarUrl,\n      getInitial\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "reactive", "onMounted", "computed", "useStore", "recordAPI", "teamAPI", "projectAPI", "ElMessage", "ElMessageBox", "getAvatarUrl", "getInitial", "Document", "ChatDotRound", "Edit", "Delete", "Plus", "Loading", "Refresh", "Clock", "name", "components", "setup", "store", "formRef", "replyFormRef", "loading", "submitting", "showCreateDialog", "showDetailDialog", "showReplyDialog", "editingDiscussion", "currentDiscussion", "replyingToDiscussion", "discussionReplies", "loadingReplies", "discussions", "myTeams", "currentTeam", "currentTeamId", "currentPage", "pageSize", "total", "discussionForm", "type", "title", "content", "replyForm", "formRules", "required", "message", "trigger", "min", "max", "replyRules", "currentUser", "getters", "<PERSON><PERSON><PERSON>er", "isStudent", "loadMyTeams", "value", "projectsResponse", "getMyProjects", "myProjects", "records", "length", "allTeams", "project", "teamsResponse", "getProjectTeams", "id", "teamsWithProject", "map", "team", "projectId", "projectName", "push", "err", "console", "warn", "loadDiscussions", "info", "response", "getMyTeam", "warning", "error", "params", "page", "size", "sortBy", "sortDir", "getTeamDiscussionRelated", "log", "filteredRecords", "recordsWithUserInfo", "record", "userName", "userRole", "userId", "creator", "realName", "username", "role", "recordId", "最终结果", "userAvatar", "avatar", "createTime", "updateTime", "attachments", "submitDiscussion", "validate", "recordData", "teamId", "localStorage", "getItem", "updateRecord", "success", "createRecord", "resetForm", "data", "Object", "assign", "editDiscussion", "discussion", "deleteDiscussion", "confirm", "confirmButtonText", "cancelButtonText", "deleteRecord", "replyToDiscussion", "submitReply", "replyData", "parentId", "loadDiscussionReplies", "discussionId", "getDiscussionReplies", "responseType", "isArray", "Array", "responseLength", "reply", "undefined", "status", "url", "config", "viewDiscussionDetail", "canEdit", "canDelete", "formatDate", "date", "Date", "toLocaleString", "getTypeColor", "colorMap", "getTypeText", "textMap", "token", "storeUser", "state", "user", "isAuthenticated"], "sources": ["D:\\workspace\\idea\\worker\\work_cli\\src\\views\\collaboration\\DiscussionView.vue"], "sourcesContent": ["<template>\n  <div class=\"discussion\">\n    <el-card>\n      <template #header>\n        <div class=\"card-header\">\n          <h3>项目讨论</h3>\n          <div class=\"header-actions\">\n            <el-select\n              v-if=\"isTeacher\"\n              v-model=\"currentTeamId\"\n              placeholder=\"选择团队\"\n              @change=\"loadDiscussions\"\n            >\n              <el-option\n                v-for=\"team in myTeams\"\n                :key=\"team.id\"\n                :label=\"`${team.name} (${team.projectName || '未知项目'})`\"\n                :value=\"team.id\"\n              />\n            </el-select>\n            <div v-else-if=\"currentTeam\" class=\"current-team-info\">\n              <span class=\"team-name\">{{ currentTeam.name }}</span>\n              <el-tag size=\"small\" type=\"info\">我的团队</el-tag>\n            </div>\n            <div v-else class=\"no-team-info\">\n              <el-tag size=\"small\" type=\"warning\">未加入团队</el-tag>\n            </div>\n\n            <el-button\n              v-if=\"currentTeamId\"\n              type=\"primary\"\n              @click=\"showCreateDialog = true\"\n              :icon=\"Plus\"\n            >\n              发起讨论\n            </el-button>\n            <el-button @click=\"loadDiscussions\" :icon=\"Refresh\">\n              刷新\n            </el-button>\n          </div>\n        </div>\n      </template>\n      \n      <!-- 讨论列表 -->\n      <div class=\"discussion-list\" v-loading=\"loading\">\n        <div v-if=\"discussions.length === 0 && !loading\" class=\"empty-state\">\n          <el-empty description=\"暂无讨论记录\">\n            <el-button type=\"primary\" @click=\"showCreateDialog = true\">\n              发起讨论\n            </el-button>\n          </el-empty>\n        </div>\n        \n        <div v-else class=\"discussion-grid\">\n          <div v-for=\"discussion in discussions\" :key=\"discussion.id\" class=\"discussion-card\">\n            <el-card shadow=\"hover\" @click=\"viewDiscussionDetail(discussion)\">\n              <h4>{{ discussion.title || '无标题讨论' }}</h4>\n              <p class=\"discussion-description\">{{ discussion.content }}</p>\n\n              <div class=\"discussion-meta\">\n                <el-tag v-if=\"discussion.type\" :type=\"getTypeColor(discussion.type)\" size=\"small\">\n                  {{ getTypeText(discussion.type) }}\n                </el-tag>\n                <span class=\"discussion-author\">{{ discussion.userName }}</span>\n              </div>\n\n              <div class=\"discussion-info\">\n                <div class=\"info-item\">\n                  <el-icon><Clock /></el-icon>\n                  <span>{{ formatDate(discussion.createTime) }}</span>\n                </div>\n                <div class=\"info-item\" v-if=\"discussion.attachments && discussion.attachments.length > 0\">\n                  <el-icon><Document /></el-icon>\n                  <span>{{ discussion.attachments.length }}个附件</span>\n                </div>\n              </div>\n\n              <div class=\"discussion-footer\">\n                <el-button size=\"small\" @click.stop=\"viewDiscussionDetail(discussion)\">\n                  查看详情\n                </el-button>\n                <el-button size=\"small\" @click.stop=\"replyToDiscussion(discussion)\" :icon=\"ChatDotRound\">\n                  回复\n                </el-button>\n                <el-button v-if=\"canEdit(discussion)\" size=\"small\" @click.stop=\"editDiscussion(discussion)\" :icon=\"Edit\">\n                  编辑\n                </el-button>\n                <el-button v-if=\"canDelete(discussion)\" size=\"small\" type=\"danger\" @click.stop=\"deleteDiscussion(discussion.id)\" :icon=\"Delete\">\n                  删除\n                </el-button>\n              </div>\n            </el-card>\n          </div>\n        </div>\n      </div>\n      \n      <!-- 分页 -->\n      <div v-if=\"total > 0\" class=\"pagination\">\n        <el-pagination\n          v-model:current-page=\"currentPage\"\n          v-model:page-size=\"pageSize\"\n          :total=\"total\"\n          :page-sizes=\"[10, 20, 50]\"\n          layout=\"total, sizes, prev, pager, next, jumper\"\n          @size-change=\"loadDiscussions\"\n          @current-change=\"loadDiscussions\"\n        />\n      </div>\n    </el-card>\n    \n    <!-- 创建/编辑讨论对话框 -->\n    <el-dialog\n      v-model=\"showCreateDialog\"\n      :title=\"editingDiscussion ? '编辑讨论' : '发起讨论'\"\n      width=\"600px\"\n    >\n      <el-form\n        ref=\"formRef\"\n        :model=\"discussionForm\"\n        :rules=\"formRules\"\n        label-width=\"80px\"\n      >\n        <el-form-item label=\"讨论类型\" prop=\"type\">\n          <el-select v-model=\"discussionForm.type\" placeholder=\"请选择讨论类型\">\n            <el-option label=\"一般讨论\" value=\"DISCUSSION\" />\n            <el-option label=\"公告通知\" value=\"ANNOUNCEMENT\" />\n            <el-option label=\"系统通知\" value=\"NOTIFICATION\" />\n          </el-select>\n        </el-form-item>\n        \n        <el-form-item label=\"讨论标题\" prop=\"title\">\n          <el-input v-model=\"discussionForm.title\" placeholder=\"请输入讨论标题\" />\n        </el-form-item>\n        \n        <el-form-item label=\"讨论内容\" prop=\"content\">\n          <el-input\n            v-model=\"discussionForm.content\"\n            type=\"textarea\"\n            :rows=\"6\"\n            placeholder=\"请输入讨论内容\"\n          />\n        </el-form-item>\n      </el-form>\n      \n      <template #footer>\n        <el-button @click=\"showCreateDialog = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"submitDiscussion\" :loading=\"submitting\">\n          {{ editingDiscussion ? '更新' : '发布' }}\n        </el-button>\n      </template>\n    </el-dialog>\n\n    <!-- 回复对话框 -->\n    <el-dialog\n      v-model=\"showReplyDialog\"\n      title=\"回复讨论\"\n      width=\"600px\"\n      :close-on-click-modal=\"false\"\n    >\n      <div v-if=\"replyingToDiscussion\" class=\"reply-context\">\n        <h4>回复：{{ replyingToDiscussion.title }}</h4>\n        <div class=\"original-content\">\n          <p><strong>{{ replyingToDiscussion.userName }}</strong> 说：</p>\n          <p>{{ replyingToDiscussion.content }}</p>\n        </div>\n      </div>\n\n      <el-form\n        ref=\"replyFormRef\"\n        :model=\"replyForm\"\n        :rules=\"replyRules\"\n        label-width=\"80px\"\n        style=\"margin-top: 20px;\"\n      >\n        <el-form-item label=\"回复内容\" prop=\"content\">\n          <el-input\n            v-model=\"replyForm.content\"\n            type=\"textarea\"\n            :rows=\"6\"\n            placeholder=\"请输入您的回复...\"\n            maxlength=\"1000\"\n            show-word-limit\n          />\n        </el-form-item>\n      </el-form>\n\n      <template #footer>\n        <el-button @click=\"showReplyDialog = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"submitReply\" :loading=\"submitting\">\n          发布回复\n        </el-button>\n      </template>\n    </el-dialog>\n\n    <!-- 讨论详情对话框 -->\n    <el-dialog\n      v-model=\"showDetailDialog\"\n      title=\"讨论详情\"\n      width=\"800px\"\n      :close-on-click-modal=\"false\"\n    >\n      <div v-if=\"currentDiscussion\" class=\"discussion-detail\">\n        <!-- 主讨论内容 -->\n        <div class=\"main-discussion\">\n          <div class=\"discussion-header\">\n            <div class=\"header-left\">\n              <h2 class=\"discussion-title\">{{ currentDiscussion.title }}</h2>\n              <div class=\"discussion-tags\">\n                <el-tag :type=\"getTypeColor(currentDiscussion.type)\" size=\"small\">\n                  {{ getTypeText(currentDiscussion.type) }}\n                </el-tag>\n              </div>\n            </div>\n            <div class=\"header-right\">\n              <div class=\"author-info\">\n                <el-avatar :size=\"40\" class=\"author-avatar\" :src=\"getAvatarUrl(currentDiscussion.userAvatar)\">\n                  {{ getInitial(currentDiscussion.userName) }}\n                </el-avatar>\n                <div class=\"author-details\">\n                  <div class=\"author-name\">{{ currentDiscussion.userName }}</div>\n                  <div class=\"publish-time\">{{ formatDate(currentDiscussion.createTime) }}</div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div class=\"discussion-content\">\n            <div class=\"content-text\">{{ currentDiscussion.content }}</div>\n\n            <div v-if=\"currentDiscussion.attachments && currentDiscussion.attachments.length > 0\" class=\"attachments-section\">\n              <div class=\"attachments-title\">\n                <el-icon><Document /></el-icon>\n                <span>附件 ({{ currentDiscussion.attachments.length }})</span>\n              </div>\n              <div class=\"attachments-list\">\n                <div v-for=\"file in currentDiscussion.attachments\" :key=\"file.id\" class=\"attachment-item\">\n                  <el-icon><Document /></el-icon>\n                  <span class=\"file-name\">{{ file.name }}</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- 回复列表 -->\n        <div class=\"replies-section\">\n          <div class=\"replies-header\">\n            <div class=\"replies-title\">\n              <el-icon><ChatDotRound /></el-icon>\n              <span>回复讨论 ({{ discussionReplies.length }})</span>\n            </div>\n            <el-divider />\n          </div>\n\n          <div v-if=\"loadingReplies\" class=\"loading-state\">\n            <el-icon class=\"is-loading\"><Loading /></el-icon>\n            <span>加载回复中...</span>\n          </div>\n\n          <div v-else-if=\"discussionReplies.length === 0\" class=\"empty-state\">\n            <el-empty description=\"暂无回复\" :image-size=\"80\">\n              <el-button type=\"primary\" @click=\"replyToDiscussion(currentDiscussion)\">\n                发表回复\n              </el-button>\n            </el-empty>\n          </div>\n\n          <div v-else class=\"replies-list\">\n            <div v-for=\"(reply, index) in discussionReplies\" :key=\"reply.id\" class=\"reply-item\">\n              <div class=\"reply-number\">#{{ index + 1 }}</div>\n              <div class=\"reply-content-wrapper\">\n                <div class=\"reply-header\">\n                  <div class=\"reply-author\">\n                    <el-avatar :size=\"32\" class=\"reply-avatar\" :src=\"getAvatarUrl(reply.userAvatar)\">\n                      {{ getInitial(reply.userName) }}\n                    </el-avatar>\n                    <div class=\"author-info\">\n                      <span class=\"author-name\">{{ reply.userName }}</span>\n                      <el-tag :type=\"reply.userRole === 'TEACHER' ? 'warning' : 'info'\" size=\"small\" class=\"role-tag\">\n                        {{ reply.userRole === 'TEACHER' ? '教师' : '学生' }}\n                      </el-tag>\n                    </div>\n                  </div>\n                  <div class=\"reply-time\">\n                    {{ formatDate(reply.createTime) }}\n                  </div>\n                </div>\n                <div class=\"reply-content\">\n                  {{ reply.content }}\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <template #footer>\n        <el-button @click=\"showDetailDialog = false\">关闭</el-button>\n        <el-button type=\"primary\" @click=\"replyToDiscussion(currentDiscussion)\">\n          回复\n        </el-button>\n      </template>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { ref, reactive, onMounted, computed } from 'vue'\nimport { useStore } from 'vuex'\nimport { recordAPI, teamAPI, projectAPI } from '@/api'\nimport { ElMessage, ElMessageBox } from 'element-plus'\nimport { getAvatarUrl, getInitial } from '@/utils/avatar'\nimport {\n  Document,\n  ChatDotRound,\n  Edit,\n  Delete,\n  Plus,\n  Loading,\n  Refresh,\n  Clock\n} from '@element-plus/icons-vue'\n\nexport default {\n  name: 'DiscussionView',\n  components: {\n    Document,\n    ChatDotRound,\n    Edit,\n    Delete,\n    Plus,\n    Loading,\n    Refresh,\n    Clock\n  },\n  setup() {\n    const store = useStore()\n    const formRef = ref()\n    const replyFormRef = ref()\n    \n    const loading = ref(false)\n    const submitting = ref(false)\n    const showCreateDialog = ref(false)\n    const showDetailDialog = ref(false)\n    const showReplyDialog = ref(false)\n    const editingDiscussion = ref(null)\n    const currentDiscussion = ref(null)\n    const replyingToDiscussion = ref(null)\n    const discussionReplies = ref([])\n    const loadingReplies = ref(false)\n    \n    const discussions = ref([])\n    const myTeams = ref([])\n    const currentTeam = ref(null)\n    const currentTeamId = ref(null)\n    const currentPage = ref(1)\n    const pageSize = ref(10)\n    const total = ref(0)\n    \n    const discussionForm = reactive({\n      type: 'DISCUSSION',\n      title: '',\n      content: ''\n    })\n\n    const replyForm = reactive({\n      content: ''\n    })\n    \n    const formRules = {\n      type: [\n        { required: true, message: '请选择讨论类型', trigger: 'change' }\n      ],\n      title: [\n        { required: true, message: '请输入讨论标题', trigger: 'blur' },\n        { min: 2, max: 100, message: '标题长度在 2 到 100 个字符', trigger: 'blur' }\n      ],\n      content: [\n        { required: true, message: '请输入讨论内容', trigger: 'blur' },\n        { min: 10, max: 2000, message: '内容长度在 10 到 2000 个字符', trigger: 'blur' }\n      ]\n    }\n\n    const replyRules = {\n      content: [\n        { required: true, message: '请输入回复内容', trigger: 'blur' },\n        { min: 5, max: 1000, message: '回复内容长度在 5 到 1000 个字符', trigger: 'blur' }\n      ]\n    }\n    \n    // 使用与DashboardView相同的方式获取用户信息\n    const currentUser = computed(() => store.getters.currentUser)\n    const isTeacher = computed(() => store.getters.isTeacher)\n    const isStudent = computed(() => store.getters.isStudent)\n\n    // 加载我的团队\n    const loadMyTeams = async () => {\n      try {\n        if (isTeacher.value) {\n          // 首先获取教师发布的项目\n          const projectsResponse = await projectAPI.getMyProjects()\n          const myProjects = projectsResponse?.records || []\n\n          if (myProjects.length > 0) {\n            // 获取所有项目相关的团队\n            const allTeams = []\n\n            for (const project of myProjects) {\n              try {\n                const teamsResponse = await teamAPI.getProjectTeams(project.id)\n\n                if (teamsResponse?.records) {\n                  // 为每个团队添加项目信息\n                  const teamsWithProject = teamsResponse.records.map(team => ({\n                    ...team,\n                    projectId: project.id,\n                    projectName: project.name\n                  }))\n                  allTeams.push(...teamsWithProject)\n                }\n              } catch (err) {\n                console.warn(`获取项目 ${project.id} 的团队失败:`, err)\n              }\n            }\n\n            if (allTeams.length > 0) {\n              myTeams.value = allTeams\n              currentTeamId.value = allTeams[0].id\n              await loadDiscussions()\n            } else {\n              ElMessage.info('暂无团队申请您发布的项目')\n              myTeams.value = []\n            }\n          } else {\n            ElMessage.info('您还没有发布任何项目，无法查看团队讨论')\n            myTeams.value = []\n          }\n        } else {\n          // 使用真实API获取学生的团队\n          const response = await teamAPI.getMyTeam()\n\n          if (response) {\n            currentTeam.value = response\n            currentTeamId.value = response.id\n            myTeams.value = [response]  // 学生只有一个团队\n            await loadDiscussions()\n          } else {\n            ElMessage.warning('您还没有加入任何团队')\n            myTeams.value = []\n          }\n        }\n      } catch (error) {\n        console.error('加载团队信息失败:', error)\n        ElMessage.error(`加载团队信息失败: ${error.message || '未知错误'}`)\n        myTeams.value = []\n      }\n    }\n\n    // 加载讨论列表\n    const loadDiscussions = async () => {\n      if (!currentTeamId.value) {\n        return\n      }\n\n      try {\n        loading.value = true\n\n        // 使用专门的团队讨论相关记录API，包括讨论、公告、通知\n        const params = {\n          page: currentPage.value,\n          size: pageSize.value,\n          sortBy: 'createTime',\n          sortDir: 'desc'\n        }\n\n        const response = await recordAPI.getTeamDiscussionRelated(currentTeamId.value, params)\n        console.log('团队讨论相关记录原始响应:', response)\n\n        if (response && response.records) {\n          console.log('记录数据示例:', response.records[0])\n\n          // 不需要前端过滤，后端已经返回了正确的记录类型\n          const filteredRecords = response.records\n\n          // 处理用户信息显示\n          const recordsWithUserInfo = filteredRecords.map(record => {\n            // 从creator对象中获取用户信息\n            let userName = '未知用户'\n            let userRole = 'STUDENT'\n            let userId = null\n\n            if (record.creator) {\n              userName = record.creator.realName || record.creator.username || '未知用户'\n              userRole = record.creator.role || 'STUDENT'\n              userId = record.creator.id\n            }\n\n            console.log('处理记录用户信息:', {\n              recordId: record.id,\n              creator: record.creator,\n              最终结果: {\n                userName: userName,\n                userRole: userRole,\n                userId: userId\n              }\n            })\n\n            return {\n              ...record,\n              userName: userName,\n              userRole: userRole,\n              userId: userId,\n              userAvatar: record.creator?.avatar || null\n            }\n          })\n\n          discussions.value = recordsWithUserInfo.map(record => ({\n            id: record.id,\n            title: record.title || '无标题',\n            content: record.content || '',\n            type: record.type || 'DISCUSSION',\n            userName: record.userName,\n            userRole: record.userRole,\n            userId: record.userId,\n            userAvatar: record.userAvatar, // 确保头像数据被传递\n            createTime: record.createTime,\n            updateTime: record.updateTime,\n            attachments: record.attachments || []\n          }))\n\n          // 使用后端返回的准确总数（后端已经过滤了正确的记录类型）\n          total.value = response.total || 0\n        } else {\n          discussions.value = []\n          total.value = 0\n        }\n\n        loading.value = false\n\n      } catch (error) {\n        console.error('加载讨论列表失败:', error)\n        ElMessage.error('加载讨论列表失败')\n        loading.value = false\n        discussions.value = []\n        total.value = 0\n      }\n    }\n    \n    // 提交讨论\n    const submitDiscussion = async () => {\n      if (!formRef.value) return\n\n      // 检查必要的数据\n      if (!currentTeamId.value) {\n        ElMessage.error('请先选择团队')\n        return\n      }\n\n      try {\n        await formRef.value.validate()\n        submitting.value = true\n\n        // 准备提交的数据\n        const recordData = {\n          title: discussionForm.title,\n          content: discussionForm.content,\n          type: discussionForm.type, // 直接使用用户选择的类型\n          teamId: currentTeamId.value,\n          projectId: currentTeam.value?.projectId || null\n        }\n\n        console.log('提交讨论数据:', recordData)\n        console.log('当前用户信息:', currentUser.value)\n        console.log('localStorage token:', localStorage.getItem('token'))\n\n        if (editingDiscussion.value) {\n          // 更新现有讨论\n          console.log('更新讨论，ID:', editingDiscussion.value.id)\n          const response = await recordAPI.updateRecord(editingDiscussion.value.id, recordData)\n          console.log('更新讨论响应:', response)\n          ElMessage.success('讨论更新成功')\n          // 重新加载讨论列表\n          await loadDiscussions()\n        } else {\n          // 创建新讨论\n          console.log('创建新讨论')\n          const response = await recordAPI.createRecord(recordData)\n          console.log('创建讨论响应:', response)\n          ElMessage.success('讨论发布成功')\n          // 重新加载讨论列表\n          await loadDiscussions()\n        }\n\n        showCreateDialog.value = false\n        resetForm()\n        submitting.value = false\n\n      } catch (error) {\n        console.error('提交讨论失败:', error)\n        ElMessage.error(`提交讨论失败: ${error.response?.data?.message || error.message || '未知错误'}`)\n        submitting.value = false\n      }\n    }\n    \n    // 重置表单\n    const resetForm = () => {\n      Object.assign(discussionForm, {\n        type: '',\n        title: '',\n        content: ''\n      })\n      editingDiscussion.value = null\n    }\n    \n    // 编辑讨论\n    const editDiscussion = (discussion) => {\n      editingDiscussion.value = discussion\n      Object.assign(discussionForm, {\n        type: discussion.type,\n        title: discussion.title,\n        content: discussion.content\n      })\n      showCreateDialog.value = true\n    }\n    \n    // 删除讨论\n    const deleteDiscussion = async (id) => {\n      try {\n        await ElMessageBox.confirm('确定要删除这条讨论吗？', '确认删除', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        })\n\n        // 使用真实API删除讨论\n        const response = await recordAPI.deleteRecord(id)\n        if (response) {\n          ElMessage.success('讨论删除成功')\n          // 重新加载讨论列表\n          await loadDiscussions()\n        }\n\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('删除讨论失败:', error)\n          ElMessage.error('删除讨论失败')\n        }\n      }\n    }\n\n    // 回复讨论\n    const replyToDiscussion = (discussion) => {\n      replyingToDiscussion.value = discussion\n      replyForm.content = ''\n      showReplyDialog.value = true\n    }\n\n    // 提交回复\n    const submitReply = async () => {\n      if (!replyFormRef.value) return\n\n      // 检查必要的数据\n      if (!currentTeamId.value) {\n        ElMessage.error('请先选择团队')\n        return\n      }\n\n      if (!replyingToDiscussion.value) {\n        ElMessage.error('回复目标不存在')\n        return\n      }\n\n      try {\n        await replyFormRef.value.validate()\n      } catch (error) {\n        return\n      }\n\n      try {\n        submitting.value = true\n\n        // 准备回复数据\n        const replyData = {\n          title: `回复：${replyingToDiscussion.value.title}`,\n          content: replyForm.content,\n          type: 'DISCUSSION', // 使用DISCUSSION类型而不是REPLY\n          parentId: replyingToDiscussion.value.id, // 父讨论ID\n          // 不绑定teamId，这样回复只会在项目详情界面显示，不会在项目讨论界面显示\n          projectId: currentTeam.value?.projectId || null\n        }\n\n        console.log('提交回复数据:', replyData)\n        console.log('当前用户信息:', currentUser.value)\n        console.log('localStorage token:', localStorage.getItem('token'))\n\n        const response = await recordAPI.createRecord(replyData)\n        console.log('回复响应:', response)\n\n        ElMessage.success('回复发布成功')\n        showReplyDialog.value = false\n        replyForm.content = ''\n\n        // 如果详情对话框是打开的，重新加载回复\n        if (showDetailDialog.value && currentDiscussion.value) {\n          await loadDiscussionReplies(currentDiscussion.value.id)\n        }\n\n        replyingToDiscussion.value = null\n        // 重新加载讨论列表\n        await loadDiscussions()\n\n        submitting.value = false\n\n      } catch (error) {\n        console.error('提交回复失败:', error)\n        ElMessage.error(`提交回复失败: ${error.response?.data?.message || error.message || '未知错误'}`)\n        submitting.value = false\n      }\n    }\n\n    // 加载讨论回复\n    const loadDiscussionReplies = async (discussionId) => {\n      try {\n        loadingReplies.value = true\n        console.log('开始加载讨论回复，讨论ID:', discussionId)\n        console.log('请求URL将是:', `/api/records/${discussionId}/replies`)\n\n        const response = await recordAPI.getDiscussionReplies(discussionId)\n        console.log('回复API响应:', response)\n\n        // 检查响应结构\n        console.log('响应结构检查:', {\n          response: response,\n          responseType: typeof response,\n          isArray: Array.isArray(response),\n          responseLength: Array.isArray(response) ? response.length : 'not array'\n        })\n\n        if (response && Array.isArray(response)) {\n          // 处理回复数据，确保用户信息正确显示\n          discussionReplies.value = response.map(reply => ({\n            ...reply,\n            userName: reply.creator?.realName || reply.creator?.username || '未知用户',\n            userRole: reply.creator?.role || 'STUDENT',\n            userId: reply.creator?.id,\n            userAvatar: reply.creator?.avatar || null\n          }))\n          console.log('处理后的回复数据:', discussionReplies.value)\n        } else if (response === null || response === undefined) {\n          console.log('API返回null/undefined数据，可能没有回复')\n          discussionReplies.value = []\n        } else {\n          console.log('API返回空数据或格式不正确，响应:', response)\n          discussionReplies.value = []\n        }\n\n        loadingReplies.value = false\n      } catch (error) {\n        console.error('加载回复失败:', error)\n        console.error('错误详情:', {\n          message: error.message,\n          response: error.response?.data,\n          status: error.response?.status,\n          url: error.config?.url\n        })\n        ElMessage.error(`加载回复失败: ${error.response?.data?.message || error.message || '未知错误'}`)\n        loadingReplies.value = false\n        discussionReplies.value = []\n      }\n    }\n\n    // 查看讨论详情\n    const viewDiscussionDetail = async (discussion) => {\n      currentDiscussion.value = discussion\n      showDetailDialog.value = true\n\n      // 先清空之前的回复数据\n      discussionReplies.value = []\n\n      // 加载该讨论的回复\n      console.log('准备加载讨论回复，讨论对象:', {\n        id: discussion.id,\n        title: discussion.title,\n        type: discussion.type,\n        parentId: discussion.parentId\n      })\n      await loadDiscussionReplies(discussion.id)\n    }\n\n    // 权限检查\n    const canEdit = (discussion) => {\n      return discussion.userId === currentUser.value?.id\n    }\n    \n    const canDelete = (discussion) => {\n      return discussion.userId === currentUser.value?.id || currentUser.value?.role === 'TEACHER'\n    }\n    \n    // 工具方法\n    const formatDate = (date) => {\n      if (!date) return ''\n      return new Date(date).toLocaleString('zh-CN')\n    }\n    \n    const getTypeColor = (type) => {\n      const colorMap = {\n        'DISCUSSION': 'primary',\n        'ANNOUNCEMENT': 'danger',\n        'NOTIFICATION': 'info',\n        'TASK': 'warning',\n        'SUBMISSION': 'success',\n        'EVALUATION': 'info'\n      }\n      return colorMap[type] || ''\n    }\n\n    const getTypeText = (type) => {\n      const textMap = {\n        'DISCUSSION': '一般讨论',\n        'ANNOUNCEMENT': '公告通知',\n        'NOTIFICATION': '系统通知',\n        'TASK': '任务',\n        'SUBMISSION': '提交',\n        'EVALUATION': '评价'\n      }\n      return textMap[type] || type\n    }\n    \n    onMounted(() => {\n      console.log('组件挂载时的用户信息:', {\n        currentUser: currentUser.value,\n        token: localStorage.getItem('token'),\n        storeUser: store.state.user,\n        isAuthenticated: store.state.isAuthenticated\n      })\n      loadMyTeams()\n    })\n    \n    return {\n      loading,\n      submitting,\n      showCreateDialog,\n      showDetailDialog,\n      showReplyDialog,\n      editingDiscussion,\n      currentDiscussion,\n      replyingToDiscussion,\n      discussionReplies,\n      loadingReplies,\n      discussions,\n      myTeams,\n      currentTeam,\n      currentTeamId,\n      currentPage,\n      pageSize,\n      total,\n      discussionForm,\n      replyForm,\n      formRules,\n      replyRules,\n      formRef,\n      replyFormRef,\n      currentUser,\n      isTeacher,\n      isStudent,\n      store,\n      loadDiscussions,\n      loadDiscussionReplies,\n      submitDiscussion,\n      submitReply,\n      editDiscussion,\n      deleteDiscussion,\n      replyToDiscussion,\n      viewDiscussionDetail,\n      canEdit,\n      canDelete,\n      formatDate,\n      getTypeColor,\n      getTypeText,\n      // 头像工具函数\n      getAvatarUrl,\n      getInitial\n    }\n  }\n}\n</script>\n\n<style scoped>\n.discussion {\n  padding: 0;\n}\n\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.card-header h3 {\n  margin: 0;\n}\n\n.header-actions {\n  display: flex;\n  gap: 12px;\n  align-items: center;\n}\n\n.current-team-info {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.team-name {\n  font-weight: 500;\n  color: #303133;\n}\n\n.discussion-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\n  gap: 20px;\n  margin-top: 16px;\n}\n\n.discussion-card {\n  cursor: pointer;\n  transition: all 0.3s ease;\n  height: 100%;\n}\n\n.discussion-card:hover {\n  transform: translateY(-4px);\n}\n\n.discussion-card .el-card {\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n}\n\n.discussion-card .el-card__body {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  min-height: 200px; /* 设置最小高度，确保卡片一致性 */\n}\n\n.discussion-card h4 {\n  margin: 0 0 10px 0;\n  color: #303133;\n  cursor: pointer;\n}\n\n.discussion-card h4:hover {\n  color: #409eff;\n}\n\n.discussion-description {\n  color: #606266;\n  margin: 10px 0;\n  display: -webkit-box;\n  -webkit-line-clamp: 2; /* 只显示2行 */\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  line-height: 1.5;\n  height: 3em; /* 固定高度为2行 (1.5 * 2 = 3em) */\n  flex: none; /* 不允许弹性伸缩，保持固定高度 */\n}\n\n.discussion-meta {\n  display: flex;\n  gap: 8px;\n  align-items: center;\n  margin: 12px 0;\n  padding-top: 8px;\n  border-top: 1px solid #f0f0f0;\n}\n\n.discussion-author {\n  color: #909399;\n  font-size: 13px;\n}\n\n.discussion-info {\n  margin: 12px 0;\n}\n\n.info-item {\n  display: flex;\n  align-items: center;\n  gap: 6px;\n  margin-bottom: 6px;\n  color: #909399;\n  font-size: 13px;\n}\n\n.discussion-footer {\n  margin-top: auto;\n  text-align: right;\n}\n\n.pagination {\n  margin-top: var(--space-6);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  padding: var(--space-4) 0;\n}\n\n/* 讨论详情对话框样式 */\n.discussion-detail {\n  max-height: 70vh;\n  overflow-y: auto;\n}\n\n.main-discussion {\n  background: #fff;\n  border-radius: 8px;\n  margin-bottom: 24px;\n}\n\n.discussion-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  padding-bottom: 16px;\n  border-bottom: 2px solid #f0f0f0;\n  margin-bottom: 20px;\n}\n\n.header-left {\n  flex: 1;\n}\n\n.discussion-title {\n  margin: 0 0 12px 0;\n  color: #303133;\n  font-size: 20px;\n  font-weight: 600;\n  line-height: 1.4;\n}\n\n.discussion-tags {\n  display: flex;\n  gap: 8px;\n}\n\n.header-right {\n  margin-left: 20px;\n}\n\n.author-info {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n}\n\n.author-avatar {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  font-weight: 600;\n}\n\n.author-details {\n  text-align: right;\n}\n\n.author-name {\n  display: block;\n  font-weight: 600;\n  color: #303133;\n  margin-bottom: 4px;\n}\n\n.publish-time {\n  font-size: 13px;\n  color: #909399;\n}\n\n.discussion-content {\n  padding: 0;\n}\n\n.content-text {\n  color: #606266;\n  line-height: 1.8;\n  font-size: 15px;\n  margin-bottom: 20px;\n  white-space: pre-wrap;\n}\n\n.attachments-section {\n  background: #f8f9fa;\n  border-radius: 6px;\n  padding: 16px;\n  border-left: 4px solid #409eff;\n}\n\n.attachments-title {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-weight: 600;\n  color: #303133;\n  margin-bottom: 12px;\n}\n\n.attachments-list {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.attachment-item {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  padding: 8px 12px;\n  background: white;\n  border-radius: 4px;\n  border: 1px solid #e4e7ed;\n  transition: all 0.3s ease;\n}\n\n.attachment-item:hover {\n  border-color: #409eff;\n  background: #f0f9ff;\n}\n\n.file-name {\n  color: #606266;\n  font-size: 14px;\n}\n\n/* 回复列表样式 */\n.replies-section {\n  margin-top: 32px;\n  padding-top: 24px;\n  border-top: 2px solid #f0f0f0;\n}\n\n.replies-header {\n  margin-bottom: 20px;\n}\n\n.replies-title {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-size: 16px;\n  font-weight: 600;\n  color: #303133;\n  margin-bottom: 16px;\n}\n\n.loading-state {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 12px;\n  padding: 40px;\n  color: #909399;\n}\n\n.empty-state {\n  text-align: center;\n  padding: 40px 20px;\n}\n\n.replies-list {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n}\n\n.reply-item {\n  display: flex;\n  gap: 12px;\n  padding: 16px;\n  background: #fafbfc;\n  border-radius: 8px;\n  border: 1px solid #e4e7ed;\n  transition: all 0.3s ease;\n}\n\n.reply-item:hover {\n  background: #f5f7fa;\n  border-color: #c0c4cc;\n}\n\n.reply-number {\n  color: #909399;\n  font-size: 12px;\n  font-weight: 600;\n  min-width: 24px;\n  text-align: center;\n  padding-top: 4px;\n}\n\n.reply-content-wrapper {\n  flex: 1;\n}\n\n.reply-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 12px;\n}\n\n.reply-author {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n}\n\n.reply-avatar {\n  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);\n  color: #303133;\n  font-weight: 600;\n}\n\n.author-info {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.author-name {\n  font-weight: 600;\n  color: #303133;\n  font-size: 14px;\n}\n\n.role-tag {\n  font-size: 11px;\n}\n\n.reply-time {\n  font-size: 12px;\n  color: #909399;\n}\n\n.reply-content {\n  color: #606266;\n  line-height: 1.6;\n  font-size: 14px;\n  white-space: pre-wrap;\n}\n</style>\n\n\n"], "mappings": ";;;AAmTA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,QAAO,QAAS,KAAI;AACvD,SAASC,QAAO,QAAS,MAAK;AAC9B,SAASC,SAAS,EAAEC,OAAO,EAAEC,UAAS,QAAS,OAAM;AACrD,SAASC,SAAS,EAAEC,YAAW,QAAS,cAAa;AACrD,SAASC,YAAY,EAAEC,UAAS,QAAS,gBAAe;AACxD,SACEC,QAAQ,EACRC,YAAY,EACZC,IAAI,EACJC,MAAM,EACNC,IAAI,EACJC,OAAO,EACPC,OAAO,EACPC,KAAI,QACC,yBAAwB;AAE/B,eAAe;EACbC,IAAI,EAAE,gBAAgB;EACtBC,UAAU,EAAE;IACVT,QAAQ;IACRC,YAAY;IACZC,IAAI;IACJC,MAAM;IACNC,IAAI;IACJC,OAAO;IACPC,OAAO;IACPC;EACF,CAAC;EACDG,KAAKA,CAAA,EAAG;IACN,MAAMC,KAAI,GAAInB,QAAQ,CAAC;IACvB,MAAMoB,OAAM,GAAIxB,GAAG,CAAC;IACpB,MAAMyB,YAAW,GAAIzB,GAAG,CAAC;IAEzB,MAAM0B,OAAM,GAAI1B,GAAG,CAAC,KAAK;IACzB,MAAM2B,UAAS,GAAI3B,GAAG,CAAC,KAAK;IAC5B,MAAM4B,gBAAe,GAAI5B,GAAG,CAAC,KAAK;IAClC,MAAM6B,gBAAe,GAAI7B,GAAG,CAAC,KAAK;IAClC,MAAM8B,eAAc,GAAI9B,GAAG,CAAC,KAAK;IACjC,MAAM+B,iBAAgB,GAAI/B,GAAG,CAAC,IAAI;IAClC,MAAMgC,iBAAgB,GAAIhC,GAAG,CAAC,IAAI;IAClC,MAAMiC,oBAAmB,GAAIjC,GAAG,CAAC,IAAI;IACrC,MAAMkC,iBAAgB,GAAIlC,GAAG,CAAC,EAAE;IAChC,MAAMmC,cAAa,GAAInC,GAAG,CAAC,KAAK;IAEhC,MAAMoC,WAAU,GAAIpC,GAAG,CAAC,EAAE;IAC1B,MAAMqC,OAAM,GAAIrC,GAAG,CAAC,EAAE;IACtB,MAAMsC,WAAU,GAAItC,GAAG,CAAC,IAAI;IAC5B,MAAMuC,aAAY,GAAIvC,GAAG,CAAC,IAAI;IAC9B,MAAMwC,WAAU,GAAIxC,GAAG,CAAC,CAAC;IACzB,MAAMyC,QAAO,GAAIzC,GAAG,CAAC,EAAE;IACvB,MAAM0C,KAAI,GAAI1C,GAAG,CAAC,CAAC;IAEnB,MAAM2C,cAAa,GAAI1C,QAAQ,CAAC;MAC9B2C,IAAI,EAAE,YAAY;MAClBC,KAAK,EAAE,EAAE;MACTC,OAAO,EAAE;IACX,CAAC;IAED,MAAMC,SAAQ,GAAI9C,QAAQ,CAAC;MACzB6C,OAAO,EAAE;IACX,CAAC;IAED,MAAME,SAAQ,GAAI;MAChBJ,IAAI,EAAE,CACJ;QAAEK,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,SAAS;QAAEC,OAAO,EAAE;MAAS,EACzD;MACDN,KAAK,EAAE,CACL;QAAEI,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,SAAS;QAAEC,OAAO,EAAE;MAAO,CAAC,EACvD;QAAEC,GAAG,EAAE,CAAC;QAAEC,GAAG,EAAE,GAAG;QAAEH,OAAO,EAAE,mBAAmB;QAAEC,OAAO,EAAE;MAAO,EACnE;MACDL,OAAO,EAAE,CACP;QAAEG,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,SAAS;QAAEC,OAAO,EAAE;MAAO,CAAC,EACvD;QAAEC,GAAG,EAAE,EAAE;QAAEC,GAAG,EAAE,IAAI;QAAEH,OAAO,EAAE,qBAAqB;QAAEC,OAAO,EAAE;MAAO;IAE1E;IAEA,MAAMG,UAAS,GAAI;MACjBR,OAAO,EAAE,CACP;QAAEG,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,SAAS;QAAEC,OAAO,EAAE;MAAO,CAAC,EACvD;QAAEC,GAAG,EAAE,CAAC;QAAEC,GAAG,EAAE,IAAI;QAAEH,OAAO,EAAE,sBAAsB;QAAEC,OAAO,EAAE;MAAO;IAE1E;;IAEA;IACA,MAAMI,WAAU,GAAIpD,QAAQ,CAAC,MAAMoB,KAAK,CAACiC,OAAO,CAACD,WAAW;IAC5D,MAAME,SAAQ,GAAItD,QAAQ,CAAC,MAAMoB,KAAK,CAACiC,OAAO,CAACC,SAAS;IACxD,MAAMC,SAAQ,GAAIvD,QAAQ,CAAC,MAAMoB,KAAK,CAACiC,OAAO,CAACE,SAAS;;IAExD;IACA,MAAMC,WAAU,GAAI,MAAAA,CAAA,KAAY;MAC9B,IAAI;QACF,IAAIF,SAAS,CAACG,KAAK,EAAE;UACnB;UACA,MAAMC,gBAAe,GAAI,MAAMtD,UAAU,CAACuD,aAAa,CAAC;UACxD,MAAMC,UAAS,GAAIF,gBAAgB,EAAEG,OAAM,IAAK,EAAC;UAEjD,IAAID,UAAU,CAACE,MAAK,GAAI,CAAC,EAAE;YACzB;YACA,MAAMC,QAAO,GAAI,EAAC;YAElB,KAAK,MAAMC,OAAM,IAAKJ,UAAU,EAAE;cAChC,IAAI;gBACF,MAAMK,aAAY,GAAI,MAAM9D,OAAO,CAAC+D,eAAe,CAACF,OAAO,CAACG,EAAE;gBAE9D,IAAIF,aAAa,EAAEJ,OAAO,EAAE;kBAC1B;kBACA,MAAMO,gBAAe,GAAIH,aAAa,CAACJ,OAAO,CAACQ,GAAG,CAACC,IAAG,KAAM;oBAC1D,GAAGA,IAAI;oBACPC,SAAS,EAAEP,OAAO,CAACG,EAAE;oBACrBK,WAAW,EAAER,OAAO,CAAC/C;kBACvB,CAAC,CAAC;kBACF8C,QAAQ,CAACU,IAAI,CAAC,GAAGL,gBAAgB;gBACnC;cACF,EAAE,OAAOM,GAAG,EAAE;gBACZC,OAAO,CAACC,IAAI,CAAC,QAAQZ,OAAO,CAACG,EAAE,SAAS,EAAEO,GAAG;cAC/C;YACF;YAEA,IAAIX,QAAQ,CAACD,MAAK,GAAI,CAAC,EAAE;cACvB5B,OAAO,CAACuB,KAAI,GAAIM,QAAO;cACvB3B,aAAa,CAACqB,KAAI,GAAIM,QAAQ,CAAC,CAAC,CAAC,CAACI,EAAC;cACnC,MAAMU,eAAe,CAAC;YACxB,OAAO;cACLxE,SAAS,CAACyE,IAAI,CAAC,cAAc;cAC7B5C,OAAO,CAACuB,KAAI,GAAI,EAAC;YACnB;UACF,OAAO;YACLpD,SAAS,CAACyE,IAAI,CAAC,qBAAqB;YACpC5C,OAAO,CAACuB,KAAI,GAAI,EAAC;UACnB;QACF,OAAO;UACL;UACA,MAAMsB,QAAO,GAAI,MAAM5E,OAAO,CAAC6E,SAAS,CAAC;UAEzC,IAAID,QAAQ,EAAE;YACZ5C,WAAW,CAACsB,KAAI,GAAIsB,QAAO;YAC3B3C,aAAa,CAACqB,KAAI,GAAIsB,QAAQ,CAACZ,EAAC;YAChCjC,OAAO,CAACuB,KAAI,GAAI,CAACsB,QAAQ,GAAG;YAC5B,MAAMF,eAAe,CAAC;UACxB,OAAO;YACLxE,SAAS,CAAC4E,OAAO,CAAC,YAAY;YAC9B/C,OAAO,CAACuB,KAAI,GAAI,EAAC;UACnB;QACF;MACF,EAAE,OAAOyB,KAAK,EAAE;QACdP,OAAO,CAACO,KAAK,CAAC,WAAW,EAAEA,KAAK;QAChC7E,SAAS,CAAC6E,KAAK,CAAC,aAAaA,KAAK,CAACnC,OAAM,IAAK,MAAM,EAAE;QACtDb,OAAO,CAACuB,KAAI,GAAI,EAAC;MACnB;IACF;;IAEA;IACA,MAAMoB,eAAc,GAAI,MAAAA,CAAA,KAAY;MAClC,IAAI,CAACzC,aAAa,CAACqB,KAAK,EAAE;QACxB;MACF;MAEA,IAAI;QACFlC,OAAO,CAACkC,KAAI,GAAI,IAAG;;QAEnB;QACA,MAAM0B,MAAK,GAAI;UACbC,IAAI,EAAE/C,WAAW,CAACoB,KAAK;UACvB4B,IAAI,EAAE/C,QAAQ,CAACmB,KAAK;UACpB6B,MAAM,EAAE,YAAY;UACpBC,OAAO,EAAE;QACX;QAEA,MAAMR,QAAO,GAAI,MAAM7E,SAAS,CAACsF,wBAAwB,CAACpD,aAAa,CAACqB,KAAK,EAAE0B,MAAM;QACrFR,OAAO,CAACc,GAAG,CAAC,eAAe,EAAEV,QAAQ;QAErC,IAAIA,QAAO,IAAKA,QAAQ,CAAClB,OAAO,EAAE;UAChCc,OAAO,CAACc,GAAG,CAAC,SAAS,EAAEV,QAAQ,CAAClB,OAAO,CAAC,CAAC,CAAC;;UAE1C;UACA,MAAM6B,eAAc,GAAIX,QAAQ,CAAClB,OAAM;;UAEvC;UACA,MAAM8B,mBAAkB,GAAID,eAAe,CAACrB,GAAG,CAACuB,MAAK,IAAK;YACxD;YACA,IAAIC,QAAO,GAAI,MAAK;YACpB,IAAIC,QAAO,GAAI,SAAQ;YACvB,IAAIC,MAAK,GAAI,IAAG;YAEhB,IAAIH,MAAM,CAACI,OAAO,EAAE;cAClBH,QAAO,GAAID,MAAM,CAACI,OAAO,CAACC,QAAO,IAAKL,MAAM,CAACI,OAAO,CAACE,QAAO,IAAK,MAAK;cACtEJ,QAAO,GAAIF,MAAM,CAACI,OAAO,CAACG,IAAG,IAAK,SAAQ;cAC1CJ,MAAK,GAAIH,MAAM,CAACI,OAAO,CAAC7B,EAAC;YAC3B;YAEAQ,OAAO,CAACc,GAAG,CAAC,WAAW,EAAE;cACvBW,QAAQ,EAAER,MAAM,CAACzB,EAAE;cACnB6B,OAAO,EAAEJ,MAAM,CAACI,OAAO;cACvBK,IAAI,EAAE;gBACJR,QAAQ,EAAEA,QAAQ;gBAClBC,QAAQ,EAAEA,QAAQ;gBAClBC,MAAM,EAAEA;cACV;YACF,CAAC;YAED,OAAO;cACL,GAAGH,MAAM;cACTC,QAAQ,EAAEA,QAAQ;cAClBC,QAAQ,EAAEA,QAAQ;cAClBC,MAAM,EAAEA,MAAM;cACdO,UAAU,EAAEV,MAAM,CAACI,OAAO,EAAEO,MAAK,IAAK;YACxC;UACF,CAAC;UAEDtE,WAAW,CAACwB,KAAI,GAAIkC,mBAAmB,CAACtB,GAAG,CAACuB,MAAK,KAAM;YACrDzB,EAAE,EAAEyB,MAAM,CAACzB,EAAE;YACbzB,KAAK,EAAEkD,MAAM,CAAClD,KAAI,IAAK,KAAK;YAC5BC,OAAO,EAAEiD,MAAM,CAACjD,OAAM,IAAK,EAAE;YAC7BF,IAAI,EAAEmD,MAAM,CAACnD,IAAG,IAAK,YAAY;YACjCoD,QAAQ,EAAED,MAAM,CAACC,QAAQ;YACzBC,QAAQ,EAAEF,MAAM,CAACE,QAAQ;YACzBC,MAAM,EAAEH,MAAM,CAACG,MAAM;YACrBO,UAAU,EAAEV,MAAM,CAACU,UAAU;YAAE;YAC/BE,UAAU,EAAEZ,MAAM,CAACY,UAAU;YAC7BC,UAAU,EAAEb,MAAM,CAACa,UAAU;YAC7BC,WAAW,EAAEd,MAAM,CAACc,WAAU,IAAK;UACrC,CAAC,CAAC;;UAEF;UACAnE,KAAK,CAACkB,KAAI,GAAIsB,QAAQ,CAACxC,KAAI,IAAK;QAClC,OAAO;UACLN,WAAW,CAACwB,KAAI,GAAI,EAAC;UACrBlB,KAAK,CAACkB,KAAI,GAAI;QAChB;QAEAlC,OAAO,CAACkC,KAAI,GAAI,KAAI;MAEtB,EAAE,OAAOyB,KAAK,EAAE;QACdP,OAAO,CAACO,KAAK,CAAC,WAAW,EAAEA,KAAK;QAChC7E,SAAS,CAAC6E,KAAK,CAAC,UAAU;QAC1B3D,OAAO,CAACkC,KAAI,GAAI,KAAI;QACpBxB,WAAW,CAACwB,KAAI,GAAI,EAAC;QACrBlB,KAAK,CAACkB,KAAI,GAAI;MAChB;IACF;;IAEA;IACA,MAAMkD,gBAAe,GAAI,MAAAA,CAAA,KAAY;MACnC,IAAI,CAACtF,OAAO,CAACoC,KAAK,EAAE;;MAEpB;MACA,IAAI,CAACrB,aAAa,CAACqB,KAAK,EAAE;QACxBpD,SAAS,CAAC6E,KAAK,CAAC,QAAQ;QACxB;MACF;MAEA,IAAI;QACF,MAAM7D,OAAO,CAACoC,KAAK,CAACmD,QAAQ,CAAC;QAC7BpF,UAAU,CAACiC,KAAI,GAAI,IAAG;;QAEtB;QACA,MAAMoD,UAAS,GAAI;UACjBnE,KAAK,EAAEF,cAAc,CAACE,KAAK;UAC3BC,OAAO,EAAEH,cAAc,CAACG,OAAO;UAC/BF,IAAI,EAAED,cAAc,CAACC,IAAI;UAAE;UAC3BqE,MAAM,EAAE1E,aAAa,CAACqB,KAAK;UAC3Bc,SAAS,EAAEpC,WAAW,CAACsB,KAAK,EAAEc,SAAQ,IAAK;QAC7C;QAEAI,OAAO,CAACc,GAAG,CAAC,SAAS,EAAEoB,UAAU;QACjClC,OAAO,CAACc,GAAG,CAAC,SAAS,EAAErC,WAAW,CAACK,KAAK;QACxCkB,OAAO,CAACc,GAAG,CAAC,qBAAqB,EAAEsB,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAEhE,IAAIpF,iBAAiB,CAAC6B,KAAK,EAAE;UAC3B;UACAkB,OAAO,CAACc,GAAG,CAAC,UAAU,EAAE7D,iBAAiB,CAAC6B,KAAK,CAACU,EAAE;UAClD,MAAMY,QAAO,GAAI,MAAM7E,SAAS,CAAC+G,YAAY,CAACrF,iBAAiB,CAAC6B,KAAK,CAACU,EAAE,EAAE0C,UAAU;UACpFlC,OAAO,CAACc,GAAG,CAAC,SAAS,EAAEV,QAAQ;UAC/B1E,SAAS,CAAC6G,OAAO,CAAC,QAAQ;UAC1B;UACA,MAAMrC,eAAe,CAAC;QACxB,OAAO;UACL;UACAF,OAAO,CAACc,GAAG,CAAC,OAAO;UACnB,MAAMV,QAAO,GAAI,MAAM7E,SAAS,CAACiH,YAAY,CAACN,UAAU;UACxDlC,OAAO,CAACc,GAAG,CAAC,SAAS,EAAEV,QAAQ;UAC/B1E,SAAS,CAAC6G,OAAO,CAAC,QAAQ;UAC1B;UACA,MAAMrC,eAAe,CAAC;QACxB;QAEApD,gBAAgB,CAACgC,KAAI,GAAI,KAAI;QAC7B2D,SAAS,CAAC;QACV5F,UAAU,CAACiC,KAAI,GAAI,KAAI;MAEzB,EAAE,OAAOyB,KAAK,EAAE;QACdP,OAAO,CAACO,KAAK,CAAC,SAAS,EAAEA,KAAK;QAC9B7E,SAAS,CAAC6E,KAAK,CAAC,WAAWA,KAAK,CAACH,QAAQ,EAAEsC,IAAI,EAAEtE,OAAM,IAAKmC,KAAK,CAACnC,OAAM,IAAK,MAAM,EAAE;QACrFvB,UAAU,CAACiC,KAAI,GAAI,KAAI;MACzB;IACF;;IAEA;IACA,MAAM2D,SAAQ,GAAIA,CAAA,KAAM;MACtBE,MAAM,CAACC,MAAM,CAAC/E,cAAc,EAAE;QAC5BC,IAAI,EAAE,EAAE;QACRC,KAAK,EAAE,EAAE;QACTC,OAAO,EAAE;MACX,CAAC;MACDf,iBAAiB,CAAC6B,KAAI,GAAI,IAAG;IAC/B;;IAEA;IACA,MAAM+D,cAAa,GAAKC,UAAU,IAAK;MACrC7F,iBAAiB,CAAC6B,KAAI,GAAIgE,UAAS;MACnCH,MAAM,CAACC,MAAM,CAAC/E,cAAc,EAAE;QAC5BC,IAAI,EAAEgF,UAAU,CAAChF,IAAI;QACrBC,KAAK,EAAE+E,UAAU,CAAC/E,KAAK;QACvBC,OAAO,EAAE8E,UAAU,CAAC9E;MACtB,CAAC;MACDlB,gBAAgB,CAACgC,KAAI,GAAI,IAAG;IAC9B;;IAEA;IACA,MAAMiE,gBAAe,GAAI,MAAOvD,EAAE,IAAK;MACrC,IAAI;QACF,MAAM7D,YAAY,CAACqH,OAAO,CAAC,aAAa,EAAE,MAAM,EAAE;UAChDC,iBAAiB,EAAE,IAAI;UACvBC,gBAAgB,EAAE,IAAI;UACtBpF,IAAI,EAAE;QACR,CAAC;;QAED;QACA,MAAMsC,QAAO,GAAI,MAAM7E,SAAS,CAAC4H,YAAY,CAAC3D,EAAE;QAChD,IAAIY,QAAQ,EAAE;UACZ1E,SAAS,CAAC6G,OAAO,CAAC,QAAQ;UAC1B;UACA,MAAMrC,eAAe,CAAC;QACxB;MAEF,EAAE,OAAOK,KAAK,EAAE;QACd,IAAIA,KAAI,KAAM,QAAQ,EAAE;UACtBP,OAAO,CAACO,KAAK,CAAC,SAAS,EAAEA,KAAK;UAC9B7E,SAAS,CAAC6E,KAAK,CAAC,QAAQ;QAC1B;MACF;IACF;;IAEA;IACA,MAAM6C,iBAAgB,GAAKN,UAAU,IAAK;MACxC3F,oBAAoB,CAAC2B,KAAI,GAAIgE,UAAS;MACtC7E,SAAS,CAACD,OAAM,GAAI,EAAC;MACrBhB,eAAe,CAAC8B,KAAI,GAAI,IAAG;IAC7B;;IAEA;IACA,MAAMuE,WAAU,GAAI,MAAAA,CAAA,KAAY;MAC9B,IAAI,CAAC1G,YAAY,CAACmC,KAAK,EAAE;;MAEzB;MACA,IAAI,CAACrB,aAAa,CAACqB,KAAK,EAAE;QACxBpD,SAAS,CAAC6E,KAAK,CAAC,QAAQ;QACxB;MACF;MAEA,IAAI,CAACpD,oBAAoB,CAAC2B,KAAK,EAAE;QAC/BpD,SAAS,CAAC6E,KAAK,CAAC,SAAS;QACzB;MACF;MAEA,IAAI;QACF,MAAM5D,YAAY,CAACmC,KAAK,CAACmD,QAAQ,CAAC;MACpC,EAAE,OAAO1B,KAAK,EAAE;QACd;MACF;MAEA,IAAI;QACF1D,UAAU,CAACiC,KAAI,GAAI,IAAG;;QAEtB;QACA,MAAMwE,SAAQ,GAAI;UAChBvF,KAAK,EAAE,MAAMZ,oBAAoB,CAAC2B,KAAK,CAACf,KAAK,EAAE;UAC/CC,OAAO,EAAEC,SAAS,CAACD,OAAO;UAC1BF,IAAI,EAAE,YAAY;UAAE;UACpByF,QAAQ,EAAEpG,oBAAoB,CAAC2B,KAAK,CAACU,EAAE;UAAE;UACzC;UACAI,SAAS,EAAEpC,WAAW,CAACsB,KAAK,EAAEc,SAAQ,IAAK;QAC7C;QAEAI,OAAO,CAACc,GAAG,CAAC,SAAS,EAAEwC,SAAS;QAChCtD,OAAO,CAACc,GAAG,CAAC,SAAS,EAAErC,WAAW,CAACK,KAAK;QACxCkB,OAAO,CAACc,GAAG,CAAC,qBAAqB,EAAEsB,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAEhE,MAAMjC,QAAO,GAAI,MAAM7E,SAAS,CAACiH,YAAY,CAACc,SAAS;QACvDtD,OAAO,CAACc,GAAG,CAAC,OAAO,EAAEV,QAAQ;QAE7B1E,SAAS,CAAC6G,OAAO,CAAC,QAAQ;QAC1BvF,eAAe,CAAC8B,KAAI,GAAI,KAAI;QAC5Bb,SAAS,CAACD,OAAM,GAAI,EAAC;;QAErB;QACA,IAAIjB,gBAAgB,CAAC+B,KAAI,IAAK5B,iBAAiB,CAAC4B,KAAK,EAAE;UACrD,MAAM0E,qBAAqB,CAACtG,iBAAiB,CAAC4B,KAAK,CAACU,EAAE;QACxD;QAEArC,oBAAoB,CAAC2B,KAAI,GAAI,IAAG;QAChC;QACA,MAAMoB,eAAe,CAAC;QAEtBrD,UAAU,CAACiC,KAAI,GAAI,KAAI;MAEzB,EAAE,OAAOyB,KAAK,EAAE;QACdP,OAAO,CAACO,KAAK,CAAC,SAAS,EAAEA,KAAK;QAC9B7E,SAAS,CAAC6E,KAAK,CAAC,WAAWA,KAAK,CAACH,QAAQ,EAAEsC,IAAI,EAAEtE,OAAM,IAAKmC,KAAK,CAACnC,OAAM,IAAK,MAAM,EAAE;QACrFvB,UAAU,CAACiC,KAAI,GAAI,KAAI;MACzB;IACF;;IAEA;IACA,MAAM0E,qBAAoB,GAAI,MAAOC,YAAY,IAAK;MACpD,IAAI;QACFpG,cAAc,CAACyB,KAAI,GAAI,IAAG;QAC1BkB,OAAO,CAACc,GAAG,CAAC,gBAAgB,EAAE2C,YAAY;QAC1CzD,OAAO,CAACc,GAAG,CAAC,UAAU,EAAE,gBAAgB2C,YAAY,UAAU;QAE9D,MAAMrD,QAAO,GAAI,MAAM7E,SAAS,CAACmI,oBAAoB,CAACD,YAAY;QAClEzD,OAAO,CAACc,GAAG,CAAC,UAAU,EAAEV,QAAQ;;QAEhC;QACAJ,OAAO,CAACc,GAAG,CAAC,SAAS,EAAE;UACrBV,QAAQ,EAAEA,QAAQ;UAClBuD,YAAY,EAAE,OAAOvD,QAAQ;UAC7BwD,OAAO,EAAEC,KAAK,CAACD,OAAO,CAACxD,QAAQ,CAAC;UAChC0D,cAAc,EAAED,KAAK,CAACD,OAAO,CAACxD,QAAQ,IAAIA,QAAQ,CAACjB,MAAK,GAAI;QAC9D,CAAC;QAED,IAAIiB,QAAO,IAAKyD,KAAK,CAACD,OAAO,CAACxD,QAAQ,CAAC,EAAE;UACvC;UACAhD,iBAAiB,CAAC0B,KAAI,GAAIsB,QAAQ,CAACV,GAAG,CAACqE,KAAI,KAAM;YAC/C,GAAGA,KAAK;YACR7C,QAAQ,EAAE6C,KAAK,CAAC1C,OAAO,EAAEC,QAAO,IAAKyC,KAAK,CAAC1C,OAAO,EAAEE,QAAO,IAAK,MAAM;YACtEJ,QAAQ,EAAE4C,KAAK,CAAC1C,OAAO,EAAEG,IAAG,IAAK,SAAS;YAC1CJ,MAAM,EAAE2C,KAAK,CAAC1C,OAAO,EAAE7B,EAAE;YACzBmC,UAAU,EAAEoC,KAAK,CAAC1C,OAAO,EAAEO,MAAK,IAAK;UACvC,CAAC,CAAC;UACF5B,OAAO,CAACc,GAAG,CAAC,WAAW,EAAE1D,iBAAiB,CAAC0B,KAAK;QAClD,OAAO,IAAIsB,QAAO,KAAM,IAAG,IAAKA,QAAO,KAAM4D,SAAS,EAAE;UACtDhE,OAAO,CAACc,GAAG,CAAC,8BAA8B;UAC1C1D,iBAAiB,CAAC0B,KAAI,GAAI,EAAC;QAC7B,OAAO;UACLkB,OAAO,CAACc,GAAG,CAAC,oBAAoB,EAAEV,QAAQ;UAC1ChD,iBAAiB,CAAC0B,KAAI,GAAI,EAAC;QAC7B;QAEAzB,cAAc,CAACyB,KAAI,GAAI,KAAI;MAC7B,EAAE,OAAOyB,KAAK,EAAE;QACdP,OAAO,CAACO,KAAK,CAAC,SAAS,EAAEA,KAAK;QAC9BP,OAAO,CAACO,KAAK,CAAC,OAAO,EAAE;UACrBnC,OAAO,EAAEmC,KAAK,CAACnC,OAAO;UACtBgC,QAAQ,EAAEG,KAAK,CAACH,QAAQ,EAAEsC,IAAI;UAC9BuB,MAAM,EAAE1D,KAAK,CAACH,QAAQ,EAAE6D,MAAM;UAC9BC,GAAG,EAAE3D,KAAK,CAAC4D,MAAM,EAAED;QACrB,CAAC;QACDxI,SAAS,CAAC6E,KAAK,CAAC,WAAWA,KAAK,CAACH,QAAQ,EAAEsC,IAAI,EAAEtE,OAAM,IAAKmC,KAAK,CAACnC,OAAM,IAAK,MAAM,EAAE;QACrFf,cAAc,CAACyB,KAAI,GAAI,KAAI;QAC3B1B,iBAAiB,CAAC0B,KAAI,GAAI,EAAC;MAC7B;IACF;;IAEA;IACA,MAAMsF,oBAAmB,GAAI,MAAOtB,UAAU,IAAK;MACjD5F,iBAAiB,CAAC4B,KAAI,GAAIgE,UAAS;MACnC/F,gBAAgB,CAAC+B,KAAI,GAAI,IAAG;;MAE5B;MACA1B,iBAAiB,CAAC0B,KAAI,GAAI,EAAC;;MAE3B;MACAkB,OAAO,CAACc,GAAG,CAAC,gBAAgB,EAAE;QAC5BtB,EAAE,EAAEsD,UAAU,CAACtD,EAAE;QACjBzB,KAAK,EAAE+E,UAAU,CAAC/E,KAAK;QACvBD,IAAI,EAAEgF,UAAU,CAAChF,IAAI;QACrByF,QAAQ,EAAET,UAAU,CAACS;MACvB,CAAC;MACD,MAAMC,qBAAqB,CAACV,UAAU,CAACtD,EAAE;IAC3C;;IAEA;IACA,MAAM6E,OAAM,GAAKvB,UAAU,IAAK;MAC9B,OAAOA,UAAU,CAAC1B,MAAK,KAAM3C,WAAW,CAACK,KAAK,EAAEU,EAAC;IACnD;IAEA,MAAM8E,SAAQ,GAAKxB,UAAU,IAAK;MAChC,OAAOA,UAAU,CAAC1B,MAAK,KAAM3C,WAAW,CAACK,KAAK,EAAEU,EAAC,IAAKf,WAAW,CAACK,KAAK,EAAE0C,IAAG,KAAM,SAAQ;IAC5F;;IAEA;IACA,MAAM+C,UAAS,GAAKC,IAAI,IAAK;MAC3B,IAAI,CAACA,IAAI,EAAE,OAAO,EAAC;MACnB,OAAO,IAAIC,IAAI,CAACD,IAAI,CAAC,CAACE,cAAc,CAAC,OAAO;IAC9C;IAEA,MAAMC,YAAW,GAAK7G,IAAI,IAAK;MAC7B,MAAM8G,QAAO,GAAI;QACf,YAAY,EAAE,SAAS;QACvB,cAAc,EAAE,QAAQ;QACxB,cAAc,EAAE,MAAM;QACtB,MAAM,EAAE,SAAS;QACjB,YAAY,EAAE,SAAS;QACvB,YAAY,EAAE;MAChB;MACA,OAAOA,QAAQ,CAAC9G,IAAI,KAAK,EAAC;IAC5B;IAEA,MAAM+G,WAAU,GAAK/G,IAAI,IAAK;MAC5B,MAAMgH,OAAM,GAAI;QACd,YAAY,EAAE,MAAM;QACpB,cAAc,EAAE,MAAM;QACtB,cAAc,EAAE,MAAM;QACtB,MAAM,EAAE,IAAI;QACZ,YAAY,EAAE,IAAI;QAClB,YAAY,EAAE;MAChB;MACA,OAAOA,OAAO,CAAChH,IAAI,KAAKA,IAAG;IAC7B;IAEA1C,SAAS,CAAC,MAAM;MACd4E,OAAO,CAACc,GAAG,CAAC,aAAa,EAAE;QACzBrC,WAAW,EAAEA,WAAW,CAACK,KAAK;QAC9BiG,KAAK,EAAE3C,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QACpC2C,SAAS,EAAEvI,KAAK,CAACwI,KAAK,CAACC,IAAI;QAC3BC,eAAe,EAAE1I,KAAK,CAACwI,KAAK,CAACE;MAC/B,CAAC;MACDtG,WAAW,CAAC;IACd,CAAC;IAED,OAAO;MACLjC,OAAO;MACPC,UAAU;MACVC,gBAAgB;MAChBC,gBAAgB;MAChBC,eAAe;MACfC,iBAAiB;MACjBC,iBAAiB;MACjBC,oBAAoB;MACpBC,iBAAiB;MACjBC,cAAc;MACdC,WAAW;MACXC,OAAO;MACPC,WAAW;MACXC,aAAa;MACbC,WAAW;MACXC,QAAQ;MACRC,KAAK;MACLC,cAAc;MACdI,SAAS;MACTC,SAAS;MACTM,UAAU;MACV9B,OAAO;MACPC,YAAY;MACZ8B,WAAW;MACXE,SAAS;MACTC,SAAS;MACTnC,KAAK;MACLyD,eAAe;MACfsD,qBAAqB;MACrBxB,gBAAgB;MAChBqB,WAAW;MACXR,cAAc;MACdE,gBAAgB;MAChBK,iBAAiB;MACjBgB,oBAAoB;MACpBC,OAAO;MACPC,SAAS;MACTC,UAAU;MACVI,YAAY;MACZE,WAAW;MACX;MACAjJ,YAAY;MACZC;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}