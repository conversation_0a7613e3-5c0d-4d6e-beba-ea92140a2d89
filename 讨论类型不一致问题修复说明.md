# 讨论类型不一致问题修复说明

## 问题描述

项目讨论页面前端和数据库中的记录类型不一致：

### 前端使用的讨论类型
- `DISCUSSION`（一般讨论）
- `PROGRESS`（进度汇报）
- `ISSUE`（问题讨论）
- `QUESTION`（技术提问）
- `ANNOUNCEMENT`（公告通知）
- `RESOURCE`（资源分享）
- `OTHER`（其他）

### 后端Record实体类中的RecordType枚举
- `TASK`（任务）
- `DISCUSSION`（讨论）
- `SUBMISSION`（提交）
- `EVALUATION`（评价）
- `ANNOUNCEMENT`（公告）
- `NOTIFICATION`（通知）

### 数据库records表中的type字段枚举
- `TASK`, `DISCUSSION`, `SUBMISSION`, `ANNOUNCEMENT`, `FEEDBACK`, `EVALUATION_ITEM`

## 解决方案

采用**子类型方案**，在保持现有数据结构的基础上，添加子类型字段来支持前端的详细讨论分类。

## 修改内容

### 1. 数据库修改
- 添加数据库迁移脚本：`V1_1__add_record_sub_type.sql`
- 在records表中添加`sub_type`字段（VARCHAR(50)）
- 为现有讨论记录设置默认子类型
- 添加复合索引提高查询性能

### 2. 后端修改

#### Record实体类 (`Record.java`)
- 添加`subType`字段
- 新增`DiscussionSubType`枚举，包含前端需要的所有讨论类型
- 添加便利方法：
  - `getDiscussionSubType()` - 获取讨论子类型
  - `setDiscussionSubType()` - 设置讨论子类型
  - `DiscussionSubType.fromString()` - 字符串转换方法

#### DTO修改
- `RecordDTO.java` - 添加`subType`字段，更新`fromRecord()`方法
- `RecordCreateRequest.java` - 添加`subType`字段

#### 服务层修改
- `RecordService.java` - 添加支持子类型的`createDiscussion()`重载方法
- `RecordServiceImpl.java` - 实现新方法，在创建记录时设置子类型

#### 控制器修改
- `RecordController.java` - 更新`createDiscussion()`方法，接收`type`参数作为子类型

### 3. 前端修改

#### DiscussionView.vue
- 修改提交逻辑：主类型固定为`DISCUSSION`，用户选择的类型作为`subType`
- 修改显示逻辑：使用`subType`来显示讨论类型标签
- 修改编辑逻辑：优先使用`subType`，兼容旧数据

## 兼容性

- **向后兼容**：现有数据不受影响，旧的讨论记录会自动设置默认子类型
- **前端兼容**：支持新旧数据格式，优先使用`subType`，回退到`type`
- **API兼容**：保持现有API不变，新增可选参数

## 数据流程

### 创建讨论
1. 前端：用户选择讨论类型（如"进度汇报"）
2. 前端：提交数据时设置`type: "DISCUSSION"`, `subType: "PROGRESS"`
3. 后端：保存到数据库，`type`字段为`DISCUSSION`，`sub_type`字段为`PROGRESS`

### 显示讨论
1. 后端：查询数据库，返回包含`type`和`subType`的DTO
2. 前端：优先使用`subType`显示讨论类型标签

## 测试

创建了单元测试 `RecordTest.java` 验证：
- 讨论子类型的设置和获取
- 字符串转换功能
- 非讨论类型的子类型处理

## 部署步骤

1. 执行数据库迁移脚本
2. 部署后端代码
3. 部署前端代码
4. 验证功能正常

## 验证方法

1. 创建新讨论，选择不同类型，检查数据库中的记录
2. 编辑现有讨论，验证类型显示正确
3. 检查讨论列表中的类型标签显示
4. 验证旧数据的兼容性
