package com.example.demo.dto;

import com.example.demo.entity.Record;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;
import java.time.LocalDateTime;

/**
 * 记录创建请求DTO
 */
@Data
public class RecordCreateRequest {
    
    @NotNull(message = "记录类型不能为空")
    private Record.RecordType type;

    private String subType;

    @NotBlank(message = "标题不能为空")
    @Size(max = 200, message = "标题长度不能超过200个字符")
    private String title;
    
    @NotBlank(message = "内容不能为空")
    @Size(max = 5000, message = "内容长度不能超过5000个字符")
    private String content;
    
    private Long projectId;
    
    private Long teamId;
    
    private Long parentId;
    
    @Min(value = 1, message = "优先级最小为1")
    @Max(value = 5, message = "优先级最大为5")
    private Integer priority;

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime dueDate;
    
    @Size(max = 1000, message = "附件信息长度不能超过1000个字符")
    private String attachments;
    
    /**
     * 验证业务规则
     */
    public boolean isValid() {
        // 任务类型必须有截止时间
        if (Record.RecordType.TASK.equals(type) && dueDate == null) {
            return false;
        }
        
        // 讨论回复必须有父记录ID
        if (Record.RecordType.DISCUSSION.equals(type) && parentId != null) {
            return true; // 这是回复
        }
        
        // 其他类型的记录必须关联项目或团队
        return projectId != null || teamId != null;
    }
    
    /**
     * 验证截止时间是否合理
     */
    public boolean isDueDateValid() {
        return dueDate == null || dueDate.isAfter(LocalDateTime.now());
    }
}
