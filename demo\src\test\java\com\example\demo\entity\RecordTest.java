package com.example.demo.entity;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * Record实体类测试
 */
public class RecordTest {

    @Test
    public void testDiscussionSubType() {
        Record record = new Record();
        record.setType(Record.RecordType.DISCUSSION);
        
        // 测试设置讨论子类型
        record.setDiscussionSubType(Record.DiscussionSubType.PROGRESS);
        assertEquals(Record.DiscussionSubType.PROGRESS, record.getDiscussionSubType());
        assertEquals("PROGRESS", record.getSubType());
        
        // 测试字符串形式设置
        record.setDiscussionSubType("QUESTION");
        assertEquals("QUESTION", record.getSubType());
        assertEquals(Record.DiscussionSubType.QUESTION, record.getDiscussionSubType());
    }
    
    @Test
    public void testDiscussionSubTypeFromString() {
        // 测试有效值
        assertEquals(Record.DiscussionSubType.DISCUSSION, Record.DiscussionSubType.fromString("DISCUSSION"));
        assertEquals(Record.DiscussionSubType.PROGRESS, Record.DiscussionSubType.fromString("PROGRESS"));
        assertEquals(Record.DiscussionSubType.ISSUE, Record.DiscussionSubType.fromString("ISSUE"));
        assertEquals(Record.DiscussionSubType.QUESTION, Record.DiscussionSubType.fromString("QUESTION"));
        assertEquals(Record.DiscussionSubType.ANNOUNCEMENT, Record.DiscussionSubType.fromString("ANNOUNCEMENT"));
        assertEquals(Record.DiscussionSubType.RESOURCE, Record.DiscussionSubType.fromString("RESOURCE"));
        assertEquals(Record.DiscussionSubType.OTHER, Record.DiscussionSubType.fromString("OTHER"));
        
        // 测试无效值
        assertEquals(Record.DiscussionSubType.DISCUSSION, Record.DiscussionSubType.fromString("INVALID"));
        assertEquals(Record.DiscussionSubType.DISCUSSION, Record.DiscussionSubType.fromString(null));
        assertEquals(Record.DiscussionSubType.DISCUSSION, Record.DiscussionSubType.fromString(""));
    }
    
    @Test
    public void testNonDiscussionTypeSubType() {
        Record record = new Record();
        record.setType(Record.RecordType.TASK);
        
        // 非讨论类型不应该设置子类型
        record.setDiscussionSubType(Record.DiscussionSubType.PROGRESS);
        assertNull(record.getDiscussionSubType());
        assertNull(record.getSubType());
    }
}
