-- 修复records表字段使用情况
-- 这个脚本用于分析和清理records表中的无用字段

-- 1. 检查score字段的使用情况
-- score字段主要用于EVALUATION_ITEM类型的记录
SELECT 
    type,
    COUNT(*) as total_count,
    COUNT(score) as score_count,
    AVG(score) as avg_score
FROM records 
WHERE score IS NOT NULL
GROUP BY type;

-- 2. 检查weight字段的使用情况  
-- weight字段主要用于EVALUATION_ITEM类型的记录
SELECT 
    type,
    COUNT(*) as total_count,
    COUNT(weight) as weight_count,
    AVG(weight) as avg_weight
FROM records 
WHERE weight IS NOT NULL
GROUP BY type;

-- 3. 检查is_anonymous字段的使用情况
-- is_anonymous字段主要用于匿名评价或反馈
SELECT 
    type,
    COUNT(*) as total_count,
    SUM(CASE WHEN is_anonymous = 1 THEN 1 ELSE 0 END) as anonymous_count,
    SUM(CASE WHEN is_anonymous = 0 THEN 1 ELSE 0 END) as non_anonymous_count
FROM records 
GROUP BY type;

-- 4. 检查target_id字段的使用情况
-- target_id字段用于关联被评价的目标（如项目、团队等）
SELECT 
    type,
    COUNT(*) as total_count,
    COUNT(target_id) as target_id_count
FROM records 
WHERE target_id IS NOT NULL
GROUP BY type;

-- 5. 分析各类型记录的字段使用模式
SELECT 
    type,
    COUNT(*) as total_records,
    COUNT(score) as has_score,
    COUNT(weight) as has_weight,
    COUNT(target_id) as has_target_id,
    SUM(CASE WHEN is_anonymous = 1 THEN 1 ELSE 0 END) as anonymous_records,
    COUNT(parent_id) as has_parent,
    COUNT(due_date) as has_due_date
FROM records 
GROUP BY type
ORDER BY type;

-- 6. 清理建议注释
/*
根据分析结果，字段使用建议：

1. score字段：
   - 主要用于EVALUATION_ITEM类型
   - 对于TASK、DISCUSSION、SUBMISSION、ANNOUNCEMENT、FEEDBACK类型通常为NULL
   - 建议保留，用于评价功能

2. weight字段：
   - 主要用于EVALUATION_ITEM类型，表示评价项的权重
   - 其他类型通常为NULL
   - 建议保留，用于加权评价计算

3. is_anonymous字段：
   - 用于FEEDBACK和EVALUATION_ITEM类型，支持匿名反馈/评价
   - 对于DISCUSSION、ANNOUNCEMENT等公开类型通常为false
   - 建议保留，用于隐私保护

4. target_id字段：
   - 用于EVALUATION_ITEM类型，关联被评价的目标
   - 用于FEEDBACK类型，关联反馈的目标
   - 建议保留，用于关联关系

结论：这些字段并非无用，而是为不同类型的记录提供特定功能支持。
建议保留所有字段，但在应用层面确保正确使用。
*/

-- 7. 数据完整性检查
-- 检查EVALUATION_ITEM类型是否正确使用了评价相关字段
SELECT 
    id,
    title,
    type,
    score,
    weight,
    target_id,
    is_anonymous
FROM records 
WHERE type = 'EVALUATION_ITEM'
ORDER BY create_time DESC
LIMIT 10;

-- 8. 检查FEEDBACK类型是否正确使用了相关字段
SELECT 
    id,
    title,
    type,
    target_id,
    is_anonymous,
    parent_id
FROM records 
WHERE type = 'FEEDBACK'
ORDER BY create_time DESC
LIMIT 10;

-- 9. 检查SUBMISSION类型的使用情况
SELECT 
    id,
    title,
    type,
    status,
    attachments,
    team_id,
    project_id
FROM records 
WHERE type = 'SUBMISSION'
ORDER BY create_time DESC
LIMIT 10;
