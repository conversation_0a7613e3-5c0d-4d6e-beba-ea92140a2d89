{"ast": null, "code": "import request from '@/utils/request';\nexport const notificationAPI = {\n  // 获取通知列表\n  getNotifications(params = {}) {\n    return request.get('/notifications', {\n      params\n    });\n  },\n  // 获取未读通知数量\n  getUnreadCount() {\n    return request.get('/notifications/unread-count');\n  },\n  // 标记通知为已读\n  markAsRead(notificationId) {\n    return request.put(`/notifications/${notificationId}/read`);\n  },\n  // 批量标记为已读\n  markAllAsRead() {\n    return request.put('/notifications/mark-all-read');\n  },\n  // 删除通知\n  deleteNotification(notificationId) {\n    return request.delete(`/notifications/${notificationId}`);\n  },\n  // 清空所有通知\n  clearAllNotifications() {\n    return request.delete('/notifications/clear-all');\n  },\n  // 获取通知详情\n  getNotificationDetail(notificationId) {\n    return request.get(`/notifications/${notificationId}`);\n  },\n  // 创建系统通知 (管理员功能)\n  createSystemNotification(data) {\n    return request.post('/notifications/system', data);\n  },\n  // 获取通知设置\n  getNotificationSettings() {\n    return request.get('/notifications/settings');\n  },\n  // 更新通知设置\n  updateNotificationSettings(settings) {\n    return request.put('/notifications/settings', settings);\n  },\n  // 基于records表的通知相关API\n\n  // 获取项目相关通知\n  getProjectNotifications(projectId, params = {}) {\n    return request.get(`/records/projects/${projectId}/notifications`, {\n      params\n    });\n  },\n  // 获取团队相关通知\n  getTeamNotifications(teamId, params = {}) {\n    return request.get(`/records/teams/${teamId}/notifications`, {\n      params\n    });\n  },\n  // 创建项目通知记录\n  createProjectNotification(projectId, data) {\n    return request.post(`/records/projects/${projectId}/notifications`, {\n      ...data,\n      recordType: 'ANNOUNCEMENT',\n      targetType: 'PROJECT',\n      targetId: projectId\n    });\n  },\n  // 创建团队通知记录\n  createTeamNotification(teamId, data) {\n    return request.post(`/records/teams/${teamId}/notifications`, {\n      ...data,\n      recordType: 'ANNOUNCEMENT',\n      targetType: 'TEAM',\n      targetId: teamId\n    });\n  },\n  // 创建用户通知记录\n  createUserNotification(userId, data) {\n    return request.post('/records', {\n      ...data,\n      recordType: 'ANNOUNCEMENT',\n      targetType: 'USER',\n      targetId: userId\n    });\n  },\n  // 获取用户的所有通知记录\n  getUserNotifications(params = {}) {\n    return request.get('/records/notifications', {\n      params: {\n        ...params,\n        recordType: 'NOTIFICATION'\n      }\n    });\n  },\n  // 获取系统通知记录\n  getSystemNotifications(params = {}) {\n    return request.get('/records/system-notifications', {\n      params: {\n        ...params,\n        recordType: 'SYSTEM_NOTIFICATION'\n      }\n    });\n  },\n  // 通知类型枚举\n  NOTIFICATION_TYPES: {\n    SYSTEM: 'SYSTEM',\n    // 系统通知\n    PROJECT: 'PROJECT',\n    // 项目通知\n    TEAM: 'TEAM',\n    // 团队通知\n    EVALUATION: 'EVALUATION',\n    // 评价通知\n    MESSAGE: 'MESSAGE',\n    // 消息通知\n    ASSIGNMENT: 'ASSIGNMENT',\n    // 分配通知\n    DEADLINE: 'DEADLINE',\n    // 截止日期通知\n    APPROVAL: 'APPROVAL' // 审批通知\n  },\n  // 通知优先级枚举\n  NOTIFICATION_PRIORITIES: {\n    LOW: 'LOW',\n    NORMAL: 'NORMAL',\n    HIGH: 'HIGH',\n    URGENT: 'URGENT'\n  },\n  // 通知状态枚举\n  NOTIFICATION_STATUS: {\n    UNREAD: 'UNREAD',\n    READ: 'READ',\n    ARCHIVED: 'ARCHIVED'\n  }\n};\n\n// 通知工具函数\nexport const notificationUtils = {\n  // 格式化通知时间\n  formatNotificationTime(time) {\n    const now = new Date();\n    const notificationTime = new Date(time);\n    const diff = now - notificationTime;\n    if (diff < 60000) {\n      // 1分钟内\n      return '刚刚';\n    } else if (diff < 3600000) {\n      // 1小时内\n      return `${Math.floor(diff / 60000)}分钟前`;\n    } else if (diff < 86400000) {\n      // 1天内\n      return `${Math.floor(diff / 3600000)}小时前`;\n    } else if (diff < 604800000) {\n      // 1周内\n      return `${Math.floor(diff / 86400000)}天前`;\n    } else {\n      return notificationTime.toLocaleDateString();\n    }\n  },\n  // 获取通知类型标签\n  getNotificationTypeLabel(type) {\n    const typeMap = {\n      SYSTEM: '系统',\n      PROJECT: '项目',\n      TEAM: '团队',\n      EVALUATION: '评价',\n      MESSAGE: '消息',\n      ASSIGNMENT: '分配',\n      DEADLINE: '截止',\n      APPROVAL: '审批'\n    };\n    return typeMap[type] || '其他';\n  },\n  // 获取通知优先级颜色\n  getNotificationPriorityColor(priority) {\n    const colorMap = {\n      LOW: '#909399',\n      NORMAL: '#409EFF',\n      HIGH: '#E6A23C',\n      URGENT: '#F56C6C'\n    };\n    return colorMap[priority] || '#909399';\n  },\n  // 获取通知图标\n  getNotificationIcon(type) {\n    const iconMap = {\n      SYSTEM: 'Setting',\n      PROJECT: 'Folder',\n      TEAM: 'UserFilled',\n      EVALUATION: 'Star',\n      MESSAGE: 'ChatDotRound',\n      ASSIGNMENT: 'DocumentChecked',\n      DEADLINE: 'Clock',\n      APPROVAL: 'Check'\n    };\n    return iconMap[type] || 'Bell';\n  },\n  // 生成通知内容\n  generateNotificationContent(type, data) {\n    const templates = {\n      PROJECT_CREATED: '新项目\"{title}\"已创建',\n      PROJECT_ASSIGNED: '项目\"{title}\"已分配给您的团队',\n      TEAM_JOINED: '新成员加入了团队\"{teamName}\"',\n      EVALUATION_RECEIVED: '您收到了新的评价',\n      DEADLINE_REMINDER: '项目\"{title}\"即将截止',\n      APPROVAL_NEEDED: '有新的申请需要您审核'\n    };\n    return templates[type] || '您有新的通知';\n  }\n};\nexport default notificationAPI;", "map": {"version": 3, "names": ["request", "notificationAPI", "getNotifications", "params", "get", "getUnreadCount", "mark<PERSON><PERSON><PERSON>", "notificationId", "put", "markAllAsRead", "deleteNotification", "delete", "clearAllNotifications", "getNotificationDetail", "createSystemNotification", "data", "post", "getNotificationSettings", "updateNotificationSettings", "settings", "getProjectNotifications", "projectId", "getTeamNotifications", "teamId", "createProjectNotification", "recordType", "targetType", "targetId", "createTeamNotification", "createUserNotification", "userId", "getUserNotifications", "getSystemNotifications", "NOTIFICATION_TYPES", "SYSTEM", "PROJECT", "TEAM", "EVALUATION", "MESSAGE", "ASSIGNMENT", "DEADLINE", "APPROVAL", "NOTIFICATION_PRIORITIES", "LOW", "NORMAL", "HIGH", "URGENT", "NOTIFICATION_STATUS", "UNREAD", "READ", "ARCHIVED", "notificationUtils", "formatNotificationTime", "time", "now", "Date", "notificationTime", "diff", "Math", "floor", "toLocaleDateString", "getNotificationTypeLabel", "type", "typeMap", "getNotificationPriorityColor", "priority", "colorMap", "getNotificationIcon", "iconMap", "generateNotificationContent", "templates", "PROJECT_CREATED", "PROJECT_ASSIGNED", "TEAM_JOINED", "EVALUATION_RECEIVED", "DEADLINE_REMINDER", "APPROVAL_NEEDED"], "sources": ["D:/workspace/idea/worker/work_cli/src/api/notification.js"], "sourcesContent": ["import request from '@/utils/request'\n\nexport const notificationAPI = {\n  // 获取通知列表\n  getNotifications(params = {}) {\n    return request.get('/notifications', { params })\n  },\n\n  // 获取未读通知数量\n  getUnreadCount() {\n    return request.get('/notifications/unread-count')\n  },\n\n  // 标记通知为已读\n  markAsRead(notificationId) {\n    return request.put(`/notifications/${notificationId}/read`)\n  },\n\n  // 批量标记为已读\n  markAllAsRead() {\n    return request.put('/notifications/mark-all-read')\n  },\n\n  // 删除通知\n  deleteNotification(notificationId) {\n    return request.delete(`/notifications/${notificationId}`)\n  },\n\n  // 清空所有通知\n  clearAllNotifications() {\n    return request.delete('/notifications/clear-all')\n  },\n\n  // 获取通知详情\n  getNotificationDetail(notificationId) {\n    return request.get(`/notifications/${notificationId}`)\n  },\n\n  // 创建系统通知 (管理员功能)\n  createSystemNotification(data) {\n    return request.post('/notifications/system', data)\n  },\n\n  // 获取通知设置\n  getNotificationSettings() {\n    return request.get('/notifications/settings')\n  },\n\n  // 更新通知设置\n  updateNotificationSettings(settings) {\n    return request.put('/notifications/settings', settings)\n  },\n\n  // 基于records表的通知相关API\n  \n  // 获取项目相关通知\n  getProjectNotifications(projectId, params = {}) {\n    return request.get(`/records/projects/${projectId}/notifications`, { params })\n  },\n\n  // 获取团队相关通知\n  getTeamNotifications(teamId, params = {}) {\n    return request.get(`/records/teams/${teamId}/notifications`, { params })\n  },\n\n  // 创建项目通知记录\n  createProjectNotification(projectId, data) {\n    return request.post(`/records/projects/${projectId}/notifications`, {\n      ...data,\n      recordType: 'ANNOUNCEMENT',\n      targetType: 'PROJECT',\n      targetId: projectId\n    })\n  },\n\n  // 创建团队通知记录\n  createTeamNotification(teamId, data) {\n    return request.post(`/records/teams/${teamId}/notifications`, {\n      ...data,\n      recordType: 'ANNOUNCEMENT',\n      targetType: 'TEAM',\n      targetId: teamId\n    })\n  },\n\n  // 创建用户通知记录\n  createUserNotification(userId, data) {\n    return request.post('/records', {\n      ...data,\n      recordType: 'ANNOUNCEMENT',\n      targetType: 'USER',\n      targetId: userId\n    })\n  },\n\n  // 获取用户的所有通知记录\n  getUserNotifications(params = {}) {\n    return request.get('/records/notifications', { \n      params: {\n        ...params,\n        recordType: 'NOTIFICATION'\n      }\n    })\n  },\n\n  // 获取系统通知记录\n  getSystemNotifications(params = {}) {\n    return request.get('/records/system-notifications', { \n      params: {\n        ...params,\n        recordType: 'SYSTEM_NOTIFICATION'\n      }\n    })\n  },\n\n  // 通知类型枚举\n  NOTIFICATION_TYPES: {\n    SYSTEM: 'SYSTEM',           // 系统通知\n    PROJECT: 'PROJECT',         // 项目通知\n    TEAM: 'TEAM',              // 团队通知\n    EVALUATION: 'EVALUATION',   // 评价通知\n    MESSAGE: 'MESSAGE',         // 消息通知\n    ASSIGNMENT: 'ASSIGNMENT',   // 分配通知\n    DEADLINE: 'DEADLINE',       // 截止日期通知\n    APPROVAL: 'APPROVAL'        // 审批通知\n  },\n\n  // 通知优先级枚举\n  NOTIFICATION_PRIORITIES: {\n    LOW: 'LOW',\n    NORMAL: 'NORMAL',\n    HIGH: 'HIGH',\n    URGENT: 'URGENT'\n  },\n\n  // 通知状态枚举\n  NOTIFICATION_STATUS: {\n    UNREAD: 'UNREAD',\n    READ: 'READ',\n    ARCHIVED: 'ARCHIVED'\n  }\n}\n\n// 通知工具函数\nexport const notificationUtils = {\n  // 格式化通知时间\n  formatNotificationTime(time) {\n    const now = new Date()\n    const notificationTime = new Date(time)\n    const diff = now - notificationTime\n\n    if (diff < 60000) { // 1分钟内\n      return '刚刚'\n    } else if (diff < 3600000) { // 1小时内\n      return `${Math.floor(diff / 60000)}分钟前`\n    } else if (diff < 86400000) { // 1天内\n      return `${Math.floor(diff / 3600000)}小时前`\n    } else if (diff < 604800000) { // 1周内\n      return `${Math.floor(diff / 86400000)}天前`\n    } else {\n      return notificationTime.toLocaleDateString()\n    }\n  },\n\n  // 获取通知类型标签\n  getNotificationTypeLabel(type) {\n    const typeMap = {\n      SYSTEM: '系统',\n      PROJECT: '项目',\n      TEAM: '团队',\n      EVALUATION: '评价',\n      MESSAGE: '消息',\n      ASSIGNMENT: '分配',\n      DEADLINE: '截止',\n      APPROVAL: '审批'\n    }\n    return typeMap[type] || '其他'\n  },\n\n  // 获取通知优先级颜色\n  getNotificationPriorityColor(priority) {\n    const colorMap = {\n      LOW: '#909399',\n      NORMAL: '#409EFF',\n      HIGH: '#E6A23C',\n      URGENT: '#F56C6C'\n    }\n    return colorMap[priority] || '#909399'\n  },\n\n  // 获取通知图标\n  getNotificationIcon(type) {\n    const iconMap = {\n      SYSTEM: 'Setting',\n      PROJECT: 'Folder',\n      TEAM: 'UserFilled',\n      EVALUATION: 'Star',\n      MESSAGE: 'ChatDotRound',\n      ASSIGNMENT: 'DocumentChecked',\n      DEADLINE: 'Clock',\n      APPROVAL: 'Check'\n    }\n    return iconMap[type] || 'Bell'\n  },\n\n  // 生成通知内容\n  generateNotificationContent(type, data) {\n    const templates = {\n      PROJECT_CREATED: '新项目\"{title}\"已创建',\n      PROJECT_ASSIGNED: '项目\"{title}\"已分配给您的团队',\n      TEAM_JOINED: '新成员加入了团队\"{teamName}\"',\n      EVALUATION_RECEIVED: '您收到了新的评价',\n      DEADLINE_REMINDER: '项目\"{title}\"即将截止',\n      APPROVAL_NEEDED: '有新的申请需要您审核'\n    }\n    \n    return templates[type] || '您有新的通知'\n  }\n}\n\nexport default notificationAPI\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,iBAAiB;AAErC,OAAO,MAAMC,eAAe,GAAG;EAC7B;EACAC,gBAAgBA,CAACC,MAAM,GAAG,CAAC,CAAC,EAAE;IAC5B,OAAOH,OAAO,CAACI,GAAG,CAAC,gBAAgB,EAAE;MAAED;IAAO,CAAC,CAAC;EAClD,CAAC;EAED;EACAE,cAAcA,CAAA,EAAG;IACf,OAAOL,OAAO,CAACI,GAAG,CAAC,6BAA6B,CAAC;EACnD,CAAC;EAED;EACAE,UAAUA,CAACC,cAAc,EAAE;IACzB,OAAOP,OAAO,CAACQ,GAAG,CAAC,kBAAkBD,cAAc,OAAO,CAAC;EAC7D,CAAC;EAED;EACAE,aAAaA,CAAA,EAAG;IACd,OAAOT,OAAO,CAACQ,GAAG,CAAC,8BAA8B,CAAC;EACpD,CAAC;EAED;EACAE,kBAAkBA,CAACH,cAAc,EAAE;IACjC,OAAOP,OAAO,CAACW,MAAM,CAAC,kBAAkBJ,cAAc,EAAE,CAAC;EAC3D,CAAC;EAED;EACAK,qBAAqBA,CAAA,EAAG;IACtB,OAAOZ,OAAO,CAACW,MAAM,CAAC,0BAA0B,CAAC;EACnD,CAAC;EAED;EACAE,qBAAqBA,CAACN,cAAc,EAAE;IACpC,OAAOP,OAAO,CAACI,GAAG,CAAC,kBAAkBG,cAAc,EAAE,CAAC;EACxD,CAAC;EAED;EACAO,wBAAwBA,CAACC,IAAI,EAAE;IAC7B,OAAOf,OAAO,CAACgB,IAAI,CAAC,uBAAuB,EAAED,IAAI,CAAC;EACpD,CAAC;EAED;EACAE,uBAAuBA,CAAA,EAAG;IACxB,OAAOjB,OAAO,CAACI,GAAG,CAAC,yBAAyB,CAAC;EAC/C,CAAC;EAED;EACAc,0BAA0BA,CAACC,QAAQ,EAAE;IACnC,OAAOnB,OAAO,CAACQ,GAAG,CAAC,yBAAyB,EAAEW,QAAQ,CAAC;EACzD,CAAC;EAED;;EAEA;EACAC,uBAAuBA,CAACC,SAAS,EAAElB,MAAM,GAAG,CAAC,CAAC,EAAE;IAC9C,OAAOH,OAAO,CAACI,GAAG,CAAC,qBAAqBiB,SAAS,gBAAgB,EAAE;MAAElB;IAAO,CAAC,CAAC;EAChF,CAAC;EAED;EACAmB,oBAAoBA,CAACC,MAAM,EAAEpB,MAAM,GAAG,CAAC,CAAC,EAAE;IACxC,OAAOH,OAAO,CAACI,GAAG,CAAC,kBAAkBmB,MAAM,gBAAgB,EAAE;MAAEpB;IAAO,CAAC,CAAC;EAC1E,CAAC;EAED;EACAqB,yBAAyBA,CAACH,SAAS,EAAEN,IAAI,EAAE;IACzC,OAAOf,OAAO,CAACgB,IAAI,CAAC,qBAAqBK,SAAS,gBAAgB,EAAE;MAClE,GAAGN,IAAI;MACPU,UAAU,EAAE,cAAc;MAC1BC,UAAU,EAAE,SAAS;MACrBC,QAAQ,EAAEN;IACZ,CAAC,CAAC;EACJ,CAAC;EAED;EACAO,sBAAsBA,CAACL,MAAM,EAAER,IAAI,EAAE;IACnC,OAAOf,OAAO,CAACgB,IAAI,CAAC,kBAAkBO,MAAM,gBAAgB,EAAE;MAC5D,GAAGR,IAAI;MACPU,UAAU,EAAE,cAAc;MAC1BC,UAAU,EAAE,MAAM;MAClBC,QAAQ,EAAEJ;IACZ,CAAC,CAAC;EACJ,CAAC;EAED;EACAM,sBAAsBA,CAACC,MAAM,EAAEf,IAAI,EAAE;IACnC,OAAOf,OAAO,CAACgB,IAAI,CAAC,UAAU,EAAE;MAC9B,GAAGD,IAAI;MACPU,UAAU,EAAE,cAAc;MAC1BC,UAAU,EAAE,MAAM;MAClBC,QAAQ,EAAEG;IACZ,CAAC,CAAC;EACJ,CAAC;EAED;EACAC,oBAAoBA,CAAC5B,MAAM,GAAG,CAAC,CAAC,EAAE;IAChC,OAAOH,OAAO,CAACI,GAAG,CAAC,wBAAwB,EAAE;MAC3CD,MAAM,EAAE;QACN,GAAGA,MAAM;QACTsB,UAAU,EAAE;MACd;IACF,CAAC,CAAC;EACJ,CAAC;EAED;EACAO,sBAAsBA,CAAC7B,MAAM,GAAG,CAAC,CAAC,EAAE;IAClC,OAAOH,OAAO,CAACI,GAAG,CAAC,+BAA+B,EAAE;MAClDD,MAAM,EAAE;QACN,GAAGA,MAAM;QACTsB,UAAU,EAAE;MACd;IACF,CAAC,CAAC;EACJ,CAAC;EAED;EACAQ,kBAAkB,EAAE;IAClBC,MAAM,EAAE,QAAQ;IAAY;IAC5BC,OAAO,EAAE,SAAS;IAAU;IAC5BC,IAAI,EAAE,MAAM;IAAe;IAC3BC,UAAU,EAAE,YAAY;IAAI;IAC5BC,OAAO,EAAE,SAAS;IAAU;IAC5BC,UAAU,EAAE,YAAY;IAAI;IAC5BC,QAAQ,EAAE,UAAU;IAAQ;IAC5BC,QAAQ,EAAE,UAAU,CAAQ;EAC9B,CAAC;EAED;EACAC,uBAAuB,EAAE;IACvBC,GAAG,EAAE,KAAK;IACVC,MAAM,EAAE,QAAQ;IAChBC,IAAI,EAAE,MAAM;IACZC,MAAM,EAAE;EACV,CAAC;EAED;EACAC,mBAAmB,EAAE;IACnBC,MAAM,EAAE,QAAQ;IAChBC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE;EACZ;AACF,CAAC;;AAED;AACA,OAAO,MAAMC,iBAAiB,GAAG;EAC/B;EACAC,sBAAsBA,CAACC,IAAI,EAAE;IAC3B,MAAMC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;IACtB,MAAMC,gBAAgB,GAAG,IAAID,IAAI,CAACF,IAAI,CAAC;IACvC,MAAMI,IAAI,GAAGH,GAAG,GAAGE,gBAAgB;IAEnC,IAAIC,IAAI,GAAG,KAAK,EAAE;MAAE;MAClB,OAAO,IAAI;IACb,CAAC,MAAM,IAAIA,IAAI,GAAG,OAAO,EAAE;MAAE;MAC3B,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACF,IAAI,GAAG,KAAK,CAAC,KAAK;IACzC,CAAC,MAAM,IAAIA,IAAI,GAAG,QAAQ,EAAE;MAAE;MAC5B,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACF,IAAI,GAAG,OAAO,CAAC,KAAK;IAC3C,CAAC,MAAM,IAAIA,IAAI,GAAG,SAAS,EAAE;MAAE;MAC7B,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACF,IAAI,GAAG,QAAQ,CAAC,IAAI;IAC3C,CAAC,MAAM;MACL,OAAOD,gBAAgB,CAACI,kBAAkB,CAAC,CAAC;IAC9C;EACF,CAAC;EAED;EACAC,wBAAwBA,CAACC,IAAI,EAAE;IAC7B,MAAMC,OAAO,GAAG;MACd7B,MAAM,EAAE,IAAI;MACZC,OAAO,EAAE,IAAI;MACbC,IAAI,EAAE,IAAI;MACVC,UAAU,EAAE,IAAI;MAChBC,OAAO,EAAE,IAAI;MACbC,UAAU,EAAE,IAAI;MAChBC,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE;IACZ,CAAC;IACD,OAAOsB,OAAO,CAACD,IAAI,CAAC,IAAI,IAAI;EAC9B,CAAC;EAED;EACAE,4BAA4BA,CAACC,QAAQ,EAAE;IACrC,MAAMC,QAAQ,GAAG;MACfvB,GAAG,EAAE,SAAS;MACdC,MAAM,EAAE,SAAS;MACjBC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACV,CAAC;IACD,OAAOoB,QAAQ,CAACD,QAAQ,CAAC,IAAI,SAAS;EACxC,CAAC;EAED;EACAE,mBAAmBA,CAACL,IAAI,EAAE;IACxB,MAAMM,OAAO,GAAG;MACdlC,MAAM,EAAE,SAAS;MACjBC,OAAO,EAAE,QAAQ;MACjBC,IAAI,EAAE,YAAY;MAClBC,UAAU,EAAE,MAAM;MAClBC,OAAO,EAAE,cAAc;MACvBC,UAAU,EAAE,iBAAiB;MAC7BC,QAAQ,EAAE,OAAO;MACjBC,QAAQ,EAAE;IACZ,CAAC;IACD,OAAO2B,OAAO,CAACN,IAAI,CAAC,IAAI,MAAM;EAChC,CAAC;EAED;EACAO,2BAA2BA,CAACP,IAAI,EAAE/C,IAAI,EAAE;IACtC,MAAMuD,SAAS,GAAG;MAChBC,eAAe,EAAE,iBAAiB;MAClCC,gBAAgB,EAAE,qBAAqB;MACvCC,WAAW,EAAE,sBAAsB;MACnCC,mBAAmB,EAAE,UAAU;MAC/BC,iBAAiB,EAAE,iBAAiB;MACpCC,eAAe,EAAE;IACnB,CAAC;IAED,OAAON,SAAS,CAACR,IAAI,CAAC,IAAI,QAAQ;EACpC;AACF,CAAC;AAED,eAAe7D,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}