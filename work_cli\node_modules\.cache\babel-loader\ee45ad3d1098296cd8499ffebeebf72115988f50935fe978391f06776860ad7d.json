{"ast": null, "code": "import { createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, withCtx as _withCtx, createTextVNode as _createTextVNode, createBlock as _createBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"team-edit\"\n};\nconst _hoisted_2 = {\n  key: 0,\n  class: \"loading\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_skeleton = _resolveComponent(\"el-skeleton\");\n  const _component_el_input = _resolveComponent(\"el-input\");\n  const _component_el_form_item = _resolveComponent(\"el-form-item\");\n  const _component_el_input_number = _resolveComponent(\"el-input-number\");\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_el_form = _resolveComponent(\"el-form\");\n  const _component_el_card = _resolveComponent(\"el-card\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_el_card, null, {\n    header: _withCtx(() => _cache[3] || (_cache[3] = [_createElementVNode(\"h3\", null, \"编辑团队\", -1 /* CACHED */)])),\n    default: _withCtx(() => [$setup.loading ? (_openBlock(), _createElementBlock(\"div\", _hoisted_2, [_createVNode(_component_el_skeleton, {\n      rows: 5,\n      animated: \"\"\n    })])) : (_openBlock(), _createBlock(_component_el_form, {\n      key: 1,\n      ref: \"formRef\",\n      model: $setup.form,\n      rules: $setup.rules,\n      \"label-width\": \"100px\",\n      size: \"large\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_form_item, {\n        label: \"团队名称\",\n        prop: \"name\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.form.name,\n          \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $setup.form.name = $event),\n          placeholder: \"请输入团队名称\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"团队描述\",\n        prop: \"description\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.form.description,\n          \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $setup.form.description = $event),\n          type: \"textarea\",\n          rows: 4,\n          placeholder: \"请输入团队描述\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"最大成员数\",\n        prop: \"maxMembers\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input_number, {\n          modelValue: $setup.form.maxMembers,\n          \"onUpdate:modelValue\": _cache[2] || (_cache[2] = $event => $setup.form.maxMembers = $event),\n          min: 2,\n          max: 20,\n          placeholder: \"最大成员数\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, null, {\n        default: _withCtx(() => [_createVNode(_component_el_button, {\n          type: \"primary\",\n          onClick: $setup.handleSubmit,\n          loading: $setup.submitting\n        }, {\n          default: _withCtx(() => _cache[4] || (_cache[4] = [_createTextVNode(\" 保存修改 \")])),\n          _: 1 /* STABLE */,\n          __: [4]\n        }, 8 /* PROPS */, [\"onClick\", \"loading\"]), $setup.team && $setup.team.status === 'APPROVED' ? (_openBlock(), _createBlock(_component_el_button, {\n          key: 0,\n          type: \"warning\",\n          onClick: $setup.handleStopRecruiting,\n          loading: $setup.stoppingRecruiting\n        }, {\n          default: _withCtx(() => _cache[5] || (_cache[5] = [_createTextVNode(\" 停止招募 \")])),\n          _: 1 /* STABLE */,\n          __: [5]\n        }, 8 /* PROPS */, [\"onClick\", \"loading\"])) : _createCommentVNode(\"v-if\", true), _createVNode(_component_el_button, {\n          onClick: $setup.handleCancel\n        }, {\n          default: _withCtx(() => _cache[6] || (_cache[6] = [_createTextVNode(\" 取消 \")])),\n          _: 1 /* STABLE */,\n          __: [6]\n        }, 8 /* PROPS */, [\"onClick\"])]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"model\", \"rules\"]))]),\n    _: 1 /* STABLE */\n  })]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_card", "header", "_withCtx", "_cache", "_createElementVNode", "$setup", "loading", "_hoisted_2", "_component_el_skeleton", "rows", "animated", "_createBlock", "_component_el_form", "ref", "model", "form", "rules", "size", "_component_el_form_item", "label", "prop", "_component_el_input", "name", "$event", "placeholder", "description", "type", "_component_el_input_number", "maxMembers", "min", "max", "_component_el_button", "onClick", "handleSubmit", "submitting", "team", "status", "handleStopRecruiting", "stoppingRecruiting", "handleCancel"], "sources": ["D:\\workspace\\idea\\worker\\work_cli\\src\\views\\team\\TeamEditView.vue"], "sourcesContent": ["<template>\n  <div class=\"team-edit\">\n    <el-card>\n      <template #header>\n        <h3>编辑团队</h3>\n      </template>\n      \n      <div v-if=\"loading\" class=\"loading\">\n        <el-skeleton :rows=\"5\" animated />\n      </div>\n      \n      <el-form\n        v-else\n        ref=\"formRef\"\n        :model=\"form\"\n        :rules=\"rules\"\n        label-width=\"100px\"\n        size=\"large\"\n      >\n        <el-form-item label=\"团队名称\" prop=\"name\">\n          <el-input v-model=\"form.name\" placeholder=\"请输入团队名称\" />\n        </el-form-item>\n        \n        <el-form-item label=\"团队描述\" prop=\"description\">\n          <el-input\n            v-model=\"form.description\"\n            type=\"textarea\"\n            :rows=\"4\"\n            placeholder=\"请输入团队描述\"\n          />\n        </el-form-item>\n        \n        <el-form-item label=\"最大成员数\" prop=\"maxMembers\">\n          <el-input-number\n            v-model=\"form.maxMembers\"\n            :min=\"2\"\n            :max=\"20\"\n            placeholder=\"最大成员数\"\n          />\n        </el-form-item>\n        \n        <el-form-item>\n          <el-button type=\"primary\" @click=\"handleSubmit\" :loading=\"submitting\">\n            保存修改\n          </el-button>\n          <el-button\n            v-if=\"team && team.status === 'APPROVED'\"\n            type=\"warning\"\n            @click=\"handleStopRecruiting\"\n            :loading=\"stoppingRecruiting\"\n          >\n            停止招募\n          </el-button>\n          <el-button @click=\"handleCancel\">\n            取消\n          </el-button>\n        </el-form-item>\n      </el-form>\n    </el-card>\n  </div>\n</template>\n\n<script>\nimport { ref, reactive, onMounted } from 'vue'\nimport { useRouter, useRoute } from 'vue-router'\nimport { teamAPI } from '@/api'\nimport { ElMessage, ElMessageBox } from 'element-plus'\n\nexport default {\n  name: 'TeamEditView',\n  setup() {\n    const router = useRouter()\n    const route = useRoute()\n    const formRef = ref()\n    const loading = ref(false)\n    const submitting = ref(false)\n    const stoppingRecruiting = ref(false)\n    const team = ref(null)\n    \n    const form = reactive({\n      name: '',\n      description: '',\n      maxMembers: 5\n    })\n    \n    const rules = {\n      name: [\n        { required: true, message: '请输入团队名称', trigger: 'blur' },\n        { min: 2, max: 50, message: '团队名称长度在 2 到 50 个字符', trigger: 'blur' }\n      ],\n      description: [\n        { required: true, message: '请输入团队描述', trigger: 'blur' },\n        { min: 10, max: 500, message: '团队描述长度在 10 到 500 个字符', trigger: 'blur' }\n      ],\n      maxMembers: [\n        { required: true, message: '请输入最大成员数', trigger: 'blur' }\n      ]\n    }\n    \n    const fetchTeam = async () => {\n      try {\n        loading.value = true\n        const teamId = route.params.id\n        const response = await teamAPI.getTeam(teamId)\n        team.value = response\n\n        Object.assign(form, {\n          name: team.value.name,\n          description: team.value.description,\n          maxMembers: team.value.maxMembers\n        })\n      } catch (error) {\n        console.error('Fetch team error:', error)\n        ElMessage.error('获取团队信息失败: ' + (error.message || '未知错误'))\n      } finally {\n        loading.value = false\n      }\n    }\n    \n    const handleSubmit = async () => {\n      if (!formRef.value) return\n      \n      try {\n        await formRef.value.validate()\n        submitting.value = true\n        \n        const teamId = route.params.id\n        await teamAPI.updateTeam(teamId, form)\n        ElMessage.success('团队更新成功！')\n        router.push('/my-teams')\n      } catch (error) {\n        console.error('Update team error:', error)\n        ElMessage.error('更新团队失败')\n      } finally {\n        submitting.value = false\n      }\n    }\n    \n    const handleStopRecruiting = async () => {\n      try {\n        await ElMessageBox.confirm(\n          '停止招募后，其他用户将无法申请加入团队，但团队可以申请项目。确定要停止招募吗？',\n          '停止招募确认',\n          {\n            confirmButtonText: '确定停止',\n            cancelButtonText: '取消',\n            type: 'warning'\n          }\n        )\n\n        stoppingRecruiting.value = true\n        const teamId = route.params.id\n        await teamAPI.stopRecruiting(teamId)\n\n        ElMessage.success('停止招募成功！')\n        // 更新团队状态\n        team.value.status = 'RECRUITING_STOPPED'\n      } catch (error) {\n        if (error === 'cancel') {\n          return\n        }\n        console.error('Stop recruiting error:', error)\n        ElMessage.error('停止招募失败: ' + (error.response?.data?.message || error.message || '未知错误'))\n      } finally {\n        stoppingRecruiting.value = false\n      }\n    }\n\n    const handleCancel = () => {\n      router.back()\n    }\n    \n    onMounted(() => {\n      fetchTeam()\n    })\n    \n    return {\n      formRef,\n      form,\n      rules,\n      loading,\n      submitting,\n      stoppingRecruiting,\n      team,\n      handleSubmit,\n      handleStopRecruiting,\n      handleCancel\n    }\n  }\n}\n</script>\n\n<style scoped>\n.team-edit {\n  padding: 20px;\n  max-width: 800px;\n  margin: 0 auto;\n}\n\n.loading {\n  padding: 20px;\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAW;;;EAMEA,KAAK,EAAC;;;;;;;;;;uBAN9BC,mBAAA,CA0DM,OA1DNC,UA0DM,GAzDJC,YAAA,CAwDUC,kBAAA;IAvDGC,MAAM,EAAAC,QAAA,CACf,MAAaC,MAAA,QAAAA,MAAA,OAAbC,mBAAA,CAAa,YAAT,MAAI,mB;sBACA,MAEM,CAALC,MAAA,CAAAC,OAAO,I,cAAlBT,mBAAA,CAEM,OAFNU,UAEM,GADJR,YAAA,CAAkCS,sBAAA;MAApBC,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAR;2BAGzBC,YAAA,CA8CUC,kBAAA;;MA5CRC,GAAG,EAAC,SAAS;MACZC,KAAK,EAAET,MAAA,CAAAU,IAAI;MACXC,KAAK,EAAEX,MAAA,CAAAW,KAAK;MACb,aAAW,EAAC,OAAO;MACnBC,IAAI,EAAC;;wBAEL,MAEe,CAFflB,YAAA,CAEemB,uBAAA;QAFDC,KAAK,EAAC,MAAM;QAACC,IAAI,EAAC;;0BAC9B,MAAsD,CAAtDrB,YAAA,CAAsDsB,mBAAA;sBAAnChB,MAAA,CAAAU,IAAI,CAACO,IAAI;qEAATjB,MAAA,CAAAU,IAAI,CAACO,IAAI,GAAAC,MAAA;UAAEC,WAAW,EAAC;;;UAG5CzB,YAAA,CAOemB,uBAAA;QAPDC,KAAK,EAAC,MAAM;QAACC,IAAI,EAAC;;0BAC9B,MAKE,CALFrB,YAAA,CAKEsB,mBAAA;sBAJShB,MAAA,CAAAU,IAAI,CAACU,WAAW;qEAAhBpB,MAAA,CAAAU,IAAI,CAACU,WAAW,GAAAF,MAAA;UACzBG,IAAI,EAAC,UAAU;UACdjB,IAAI,EAAE,CAAC;UACRe,WAAW,EAAC;;;UAIhBzB,YAAA,CAOemB,uBAAA;QAPDC,KAAK,EAAC,OAAO;QAACC,IAAI,EAAC;;0BAC/B,MAKE,CALFrB,YAAA,CAKE4B,0BAAA;sBAJStB,MAAA,CAAAU,IAAI,CAACa,UAAU;qEAAfvB,MAAA,CAAAU,IAAI,CAACa,UAAU,GAAAL,MAAA;UACvBM,GAAG,EAAE,CAAC;UACNC,GAAG,EAAE,EAAE;UACRN,WAAW,EAAC;;;UAIhBzB,YAAA,CAeemB,uBAAA;0BAdb,MAEY,CAFZnB,YAAA,CAEYgC,oBAAA;UAFDL,IAAI,EAAC,SAAS;UAAEM,OAAK,EAAE3B,MAAA,CAAA4B,YAAY;UAAG3B,OAAO,EAAED,MAAA,CAAA6B;;4BAAY,MAEtE/B,MAAA,QAAAA,MAAA,O,iBAFsE,QAEtE,E;;;mDAEQE,MAAA,CAAA8B,IAAI,IAAI9B,MAAA,CAAA8B,IAAI,CAACC,MAAM,mB,cAD3BzB,YAAA,CAOYoB,oBAAA;;UALVL,IAAI,EAAC,SAAS;UACbM,OAAK,EAAE3B,MAAA,CAAAgC,oBAAoB;UAC3B/B,OAAO,EAAED,MAAA,CAAAiC;;4BACX,MAEDnC,MAAA,QAAAA,MAAA,O,iBAFC,QAED,E;;;wFACAJ,YAAA,CAEYgC,oBAAA;UAFAC,OAAK,EAAE3B,MAAA,CAAAkC;QAAY;4BAAE,MAEjCpC,MAAA,QAAAA,MAAA,O,iBAFiC,MAEjC,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}