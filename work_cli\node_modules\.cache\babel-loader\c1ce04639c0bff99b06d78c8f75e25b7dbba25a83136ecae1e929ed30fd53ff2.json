{"ast": null, "code": "import { createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, withCtx as _withCtx, createTextVNode as _createTextVNode, createBlock as _createBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"project-edit\"\n};\nconst _hoisted_2 = {\n  key: 0,\n  class: \"loading\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_skeleton = _resolveComponent(\"el-skeleton\");\n  const _component_el_input = _resolveComponent(\"el-input\");\n  const _component_el_form_item = _resolveComponent(\"el-form-item\");\n  const _component_el_date_picker = _resolveComponent(\"el-date-picker\");\n  const _component_el_input_number = _resolveComponent(\"el-input-number\");\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_el_form = _resolveComponent(\"el-form\");\n  const _component_el_card = _resolveComponent(\"el-card\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_el_card, null, {\n    header: _withCtx(() => _cache[6] || (_cache[6] = [_createElementVNode(\"h3\", null, \"编辑项目\", -1 /* CACHED */)])),\n    default: _withCtx(() => [$setup.loading ? (_openBlock(), _createElementBlock(\"div\", _hoisted_2, [_createVNode(_component_el_skeleton, {\n      rows: 5,\n      animated: \"\"\n    })])) : (_openBlock(), _createBlock(_component_el_form, {\n      key: 1,\n      ref: \"formRef\",\n      model: $setup.form,\n      rules: $setup.rules,\n      \"label-width\": \"100px\",\n      size: \"large\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_form_item, {\n        label: \"项目名称\",\n        prop: \"name\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.form.name,\n          \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $setup.form.name = $event),\n          placeholder: \"请输入项目名称\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"项目描述\",\n        prop: \"description\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.form.description,\n          \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $setup.form.description = $event),\n          type: \"textarea\",\n          rows: 4,\n          placeholder: \"请输入项目描述\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"项目要求\",\n        prop: \"requirements\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.form.requirements,\n          \"onUpdate:modelValue\": _cache[2] || (_cache[2] = $event => $setup.form.requirements = $event),\n          type: \"textarea\",\n          rows: 3,\n          placeholder: \"请输入项目要求\",\n          maxlength: \"1000\",\n          \"show-word-limit\": \"\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"项目目标\",\n        prop: \"objectives\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.form.objectives,\n          \"onUpdate:modelValue\": _cache[3] || (_cache[3] = $event => $setup.form.objectives = $event),\n          type: \"textarea\",\n          rows: 3,\n          placeholder: \"请描述项目的具体目标和预期成果\",\n          maxlength: \"500\",\n          \"show-word-limit\": \"\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"截止时间\",\n        prop: \"deadline\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_date_picker, {\n          modelValue: $setup.form.deadline,\n          \"onUpdate:modelValue\": _cache[4] || (_cache[4] = $event => $setup.form.deadline = $event),\n          type: \"datetime\",\n          placeholder: \"选择截止时间\",\n          style: {\n            \"width\": \"100%\"\n          }\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"最大团队数\",\n        prop: \"maxTeams\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input_number, {\n          modelValue: $setup.form.maxTeams,\n          \"onUpdate:modelValue\": _cache[5] || (_cache[5] = $event => $setup.form.maxTeams = $event),\n          min: 1,\n          max: 100,\n          placeholder: \"最大团队数\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, null, {\n        default: _withCtx(() => [_createVNode(_component_el_button, {\n          type: \"primary\",\n          onClick: $setup.handleSubmit,\n          loading: $setup.submitting\n        }, {\n          default: _withCtx(() => _cache[7] || (_cache[7] = [_createTextVNode(\" 保存修改 \")])),\n          _: 1 /* STABLE */,\n          __: [7]\n        }, 8 /* PROPS */, [\"onClick\", \"loading\"]), _createVNode(_component_el_button, {\n          onClick: $setup.handleCancel\n        }, {\n          default: _withCtx(() => _cache[8] || (_cache[8] = [_createTextVNode(\" 取消 \")])),\n          _: 1 /* STABLE */,\n          __: [8]\n        }, 8 /* PROPS */, [\"onClick\"])]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"model\", \"rules\"]))]),\n    _: 1 /* STABLE */\n  })]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_card", "header", "_withCtx", "_cache", "_createElementVNode", "$setup", "loading", "_hoisted_2", "_component_el_skeleton", "rows", "animated", "_createBlock", "_component_el_form", "ref", "model", "form", "rules", "size", "_component_el_form_item", "label", "prop", "_component_el_input", "name", "$event", "placeholder", "description", "type", "requirements", "maxlength", "objectives", "_component_el_date_picker", "deadline", "style", "_component_el_input_number", "maxTeams", "min", "max", "_component_el_button", "onClick", "handleSubmit", "submitting", "handleCancel"], "sources": ["D:\\workspace\\idea\\worker\\work_cli\\src\\views\\project\\ProjectEditView.vue"], "sourcesContent": ["<template>\n  <div class=\"project-edit\">\n    <el-card>\n      <template #header>\n        <h3>编辑项目</h3>\n      </template>\n      \n      <div v-if=\"loading\" class=\"loading\">\n        <el-skeleton :rows=\"5\" animated />\n      </div>\n      \n      <el-form\n        v-else\n        ref=\"formRef\"\n        :model=\"form\"\n        :rules=\"rules\"\n        label-width=\"100px\"\n        size=\"large\"\n      >\n        <el-form-item label=\"项目名称\" prop=\"name\">\n          <el-input v-model=\"form.name\" placeholder=\"请输入项目名称\" />\n        </el-form-item>\n        \n        <el-form-item label=\"项目描述\" prop=\"description\">\n          <el-input\n            v-model=\"form.description\"\n            type=\"textarea\"\n            :rows=\"4\"\n            placeholder=\"请输入项目描述\"\n          />\n        </el-form-item>\n        \n        <el-form-item label=\"项目要求\" prop=\"requirements\">\n          <el-input\n            v-model=\"form.requirements\"\n            type=\"textarea\"\n            :rows=\"3\"\n            placeholder=\"请输入项目要求\"\n            maxlength=\"1000\"\n            show-word-limit\n          />\n        </el-form-item>\n\n        <el-form-item label=\"项目目标\" prop=\"objectives\">\n          <el-input\n            v-model=\"form.objectives\"\n            type=\"textarea\"\n            :rows=\"3\"\n            placeholder=\"请描述项目的具体目标和预期成果\"\n            maxlength=\"500\"\n            show-word-limit\n          />\n        </el-form-item>\n        \n        <el-form-item label=\"截止时间\" prop=\"deadline\">\n          <el-date-picker\n            v-model=\"form.deadline\"\n            type=\"datetime\"\n            placeholder=\"选择截止时间\"\n            style=\"width: 100%\"\n          />\n        </el-form-item>\n        \n        <el-form-item label=\"最大团队数\" prop=\"maxTeams\">\n          <el-input-number\n            v-model=\"form.maxTeams\"\n            :min=\"1\"\n            :max=\"100\"\n            placeholder=\"最大团队数\"\n          />\n        </el-form-item>\n        \n        <el-form-item>\n          <el-button type=\"primary\" @click=\"handleSubmit\" :loading=\"submitting\">\n            保存修改\n          </el-button>\n          <el-button @click=\"handleCancel\">\n            取消\n          </el-button>\n        </el-form-item>\n      </el-form>\n    </el-card>\n  </div>\n</template>\n\n<script>\nimport { ref, reactive, onMounted } from 'vue'\nimport { useRouter, useRoute } from 'vue-router'\nimport { projectAPI } from '@/api'\nimport { ElMessage } from 'element-plus'\n\nexport default {\n  name: 'ProjectEditView',\n  setup() {\n    const router = useRouter()\n    const route = useRoute()\n    const formRef = ref()\n    const loading = ref(false)\n    const submitting = ref(false)\n    \n    const form = reactive({\n      name: '',\n      description: '',\n      requirements: '',\n      objectives: '',\n      deadline: '',\n      maxTeams: 1\n    })\n\n    const rules = {\n      name: [\n        { required: true, message: '请输入项目名称', trigger: 'blur' }\n      ],\n      description: [\n        { required: true, message: '请输入项目描述', trigger: 'blur' }\n      ],\n      requirements: [\n        { required: true, message: '请输入项目要求', trigger: 'blur' }\n      ],\n      objectives: [\n        { required: true, message: '请输入项目目标', trigger: 'blur' }\n      ],\n      deadline: [\n        { required: true, message: '请选择截止时间', trigger: 'change' }\n      ],\n      maxTeams: [\n        { required: true, message: '请输入最大团队数', trigger: 'blur' }\n      ]\n    }\n    \n    const fetchProject = async () => {\n      try {\n        loading.value = true\n        const projectId = route.params.id\n        const response = await projectAPI.getProject(projectId)\n        console.log('编辑页面获取项目数据:', response)\n        // 响应拦截器已经提取了数据，直接使用response\n        const project = response\n\n        Object.assign(form, {\n          name: project.name,\n          description: project.description,\n          requirements: project.requirements,\n          objectives: project.objectives || '',\n          deadline: new Date(project.deadline),\n          maxTeams: project.maxTeams\n        })\n      } catch (error) {\n        console.error('Fetch project error:', error)\n        ElMessage.error('获取项目信息失败')\n      } finally {\n        loading.value = false\n      }\n    }\n    \n    const handleSubmit = async () => {\n      if (!formRef.value) return\n      \n      try {\n        await formRef.value.validate()\n        submitting.value = true\n        \n        const projectId = route.params.id\n        await projectAPI.updateProject(projectId, form)\n        ElMessage.success('项目更新成功！')\n        router.push('/dashboard/my-projects')\n      } catch (error) {\n        console.error('Update project error:', error)\n        ElMessage.error('更新项目失败')\n      } finally {\n        submitting.value = false\n      }\n    }\n    \n    const handleCancel = () => {\n      router.back()\n    }\n    \n    onMounted(() => {\n      fetchProject()\n    })\n    \n    return {\n      formRef,\n      form,\n      rules,\n      loading,\n      submitting,\n      handleSubmit,\n      handleCancel\n    }\n  }\n}\n</script>\n\n<style scoped>\n.project-edit {\n  padding: 20px;\n  max-width: 800px;\n  margin: 0 auto;\n}\n\n.loading {\n  padding: 20px;\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAc;;;EAMDA,KAAK,EAAC;;;;;;;;;;;uBAN9BC,mBAAA,CAiFM,OAjFNC,UAiFM,GAhFJC,YAAA,CA+EUC,kBAAA;IA9EGC,MAAM,EAAAC,QAAA,CACf,MAAaC,MAAA,QAAAA,MAAA,OAAbC,mBAAA,CAAa,YAAT,MAAI,mB;sBACG,MAEM,CAARC,MAAA,CAAAC,OAAO,I,cAAlBT,mBAAA,CAEM,OAFNU,UAEM,GADJR,YAAA,CAAkCS,sBAAA;MAApBC,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAR;2BAGzBC,YAAA,CAqEUC,kBAAA;;MAnERC,GAAG,EAAC,SAAS;MACZC,KAAK,EAAET,MAAA,CAAAU,IAAI;MACXC,KAAK,EAAEX,MAAA,CAAAW,KAAK;MACb,aAAW,EAAC,OAAO;MACnBC,IAAI,EAAC;;wBAEL,MAEe,CAFflB,YAAA,CAEemB,uBAAA;QAFDC,KAAK,EAAC,MAAM;QAACC,IAAI,EAAC;;0BAC9B,MAAsD,CAAtDrB,YAAA,CAAsDsB,mBAAA;sBAAnChB,MAAA,CAAAU,IAAI,CAACO,IAAI;qEAATjB,MAAA,CAAAU,IAAI,CAACO,IAAI,GAAAC,MAAA;UAAEC,WAAW,EAAC;;;UAG5CzB,YAAA,CAOemB,uBAAA;QAPDC,KAAK,EAAC,MAAM;QAACC,IAAI,EAAC;;0BAC9B,MAKE,CALFrB,YAAA,CAKEsB,mBAAA;sBAJShB,MAAA,CAAAU,IAAI,CAACU,WAAW;qEAAhBpB,MAAA,CAAAU,IAAI,CAACU,WAAW,GAAAF,MAAA;UACzBG,IAAI,EAAC,UAAU;UACdjB,IAAI,EAAE,CAAC;UACRe,WAAW,EAAC;;;UAIhBzB,YAAA,CASemB,uBAAA;QATDC,KAAK,EAAC,MAAM;QAACC,IAAI,EAAC;;0BAC9B,MAOE,CAPFrB,YAAA,CAOEsB,mBAAA;sBANShB,MAAA,CAAAU,IAAI,CAACY,YAAY;qEAAjBtB,MAAA,CAAAU,IAAI,CAACY,YAAY,GAAAJ,MAAA;UAC1BG,IAAI,EAAC,UAAU;UACdjB,IAAI,EAAE,CAAC;UACRe,WAAW,EAAC,SAAS;UACrBI,SAAS,EAAC,MAAM;UAChB,iBAAe,EAAf;;;UAIJ7B,YAAA,CASemB,uBAAA;QATDC,KAAK,EAAC,MAAM;QAACC,IAAI,EAAC;;0BAC9B,MAOE,CAPFrB,YAAA,CAOEsB,mBAAA;sBANShB,MAAA,CAAAU,IAAI,CAACc,UAAU;qEAAfxB,MAAA,CAAAU,IAAI,CAACc,UAAU,GAAAN,MAAA;UACxBG,IAAI,EAAC,UAAU;UACdjB,IAAI,EAAE,CAAC;UACRe,WAAW,EAAC,iBAAiB;UAC7BI,SAAS,EAAC,KAAK;UACf,iBAAe,EAAf;;;UAIJ7B,YAAA,CAOemB,uBAAA;QAPDC,KAAK,EAAC,MAAM;QAACC,IAAI,EAAC;;0BAC9B,MAKE,CALFrB,YAAA,CAKE+B,yBAAA;sBAJSzB,MAAA,CAAAU,IAAI,CAACgB,QAAQ;qEAAb1B,MAAA,CAAAU,IAAI,CAACgB,QAAQ,GAAAR,MAAA;UACtBG,IAAI,EAAC,UAAU;UACfF,WAAW,EAAC,QAAQ;UACpBQ,KAAmB,EAAnB;YAAA;UAAA;;;UAIJjC,YAAA,CAOemB,uBAAA;QAPDC,KAAK,EAAC,OAAO;QAACC,IAAI,EAAC;;0BAC/B,MAKE,CALFrB,YAAA,CAKEkC,0BAAA;sBAJS5B,MAAA,CAAAU,IAAI,CAACmB,QAAQ;qEAAb7B,MAAA,CAAAU,IAAI,CAACmB,QAAQ,GAAAX,MAAA;UACrBY,GAAG,EAAE,CAAC;UACNC,GAAG,EAAE,GAAG;UACTZ,WAAW,EAAC;;;UAIhBzB,YAAA,CAOemB,uBAAA;0BANb,MAEY,CAFZnB,YAAA,CAEYsC,oBAAA;UAFDX,IAAI,EAAC,SAAS;UAAEY,OAAK,EAAEjC,MAAA,CAAAkC,YAAY;UAAGjC,OAAO,EAAED,MAAA,CAAAmC;;4BAAY,MAEtErC,MAAA,QAAAA,MAAA,O,iBAFsE,QAEtE,E;;;mDACAJ,YAAA,CAEYsC,oBAAA;UAFAC,OAAK,EAAEjC,MAAA,CAAAoC;QAAY;4BAAE,MAEjCtC,MAAA,QAAAA,MAAA,O,iBAFiC,MAEjC,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}